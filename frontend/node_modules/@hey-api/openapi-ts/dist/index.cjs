'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var fs$1 = require('fs');
var ae = require('path');
var Hl = require('@apidevtools/json-schema-ref-parser');
var c12 = require('c12');
var y = require('typescript');
var os$1 = require('os');
var d = require('handlebars');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var ae__default = /*#__PURE__*/_interopDefault(ae);
var Hl__default = /*#__PURE__*/_interopDefault(Hl);
var y__default = /*#__PURE__*/_interopDefault(y);
var d__default = /*#__PURE__*/_interopDefault(d);

var Sp=Object.create;var Un=Object.defineProperty;var Ap=Object.getOwnPropertyDescriptor;var Ip=Object.getOwnPropertyNames;var Ep=Object.getPrototypeOf,vp=Object.prototype.hasOwnProperty;var ye=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,r)=>(typeof require<"u"?require:t)[r]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});var Y=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var kp=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of Ip(t))!vp.call(e,n)&&n!==r&&Un(e,n,{get:()=>t[n],enumerable:!(o=Ap(t,n))||o.enumerable});return e};var qp=(e,t,r)=>(r=e!=null?Sp(Ep(e)):{},kp(Un(r,"default",{value:e,enumerable:!0}),e));var Jn=Y((ru,Gn)=>{Gn.exports=zn;zn.sync=Np;var Qn=ye("fs");function jp(e,t){var r=t.pathExt!==void 0?t.pathExt:process.env.PATHEXT;if(!r||(r=r.split(";"),r.indexOf("")!==-1))return !0;for(var o=0;o<r.length;o++){var n=r[o].toLowerCase();if(n&&e.substr(-n.length).toLowerCase()===n)return !0}return !1}function Xn(e,t,r){return !e.isSymbolicLink()&&!e.isFile()?!1:jp(t,r)}function zn(e,t,r){Qn.stat(e,function(o,n){r(o,o?!1:Xn(n,e,t));});}function Np(e,t){return Xn(Qn.statSync(e),e,t)}});var ro=Y((nu,to)=>{to.exports=Yn;Yn.sync=wp;var Zn=ye("fs");function Yn(e,t,r){Zn.stat(e,function(o,n){r(o,o?!1:eo(n,t));});}function wp(e,t){return eo(Zn.statSync(e),t)}function eo(e,t){return e.isFile()&&$p(e,t)}function $p(e,t){var r=e.mode,o=e.uid,n=e.gid,s=t.uid!==void 0?t.uid:process.getuid&&process.getuid(),i=t.gid!==void 0?t.gid:process.getgid&&process.getgid(),a=parseInt("100",8),c=parseInt("010",8),m=parseInt("001",8),l=a|c,u=r&m||r&c&&n===i||r&a&&o===s||r&l&&s===0;return u}});var oo=Y((su,no)=>{ye("fs");var Zt;process.platform==="win32"||global.TESTING_WINDOWS?Zt=Jn():Zt=ro();no.exports=Wr;Wr.sync=Dp;function Wr(e,t,r){if(typeof t=="function"&&(r=t,t={}),!r){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(o,n){Wr(e,t||{},function(s,i){s?n(s):o(i);});})}Zt(e,t||{},function(o,n){o&&(o.code==="EACCES"||t&&t.ignoreErrors)&&(o=null,n=!1),r(o,n);});}function Dp(e,t){try{return Zt.sync(e,t||{})}catch(r){if(t&&t.ignoreErrors||r.code==="EACCES")return !1;throw r}}});var lo=Y((iu,mo)=>{var ct=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",so=ye("path"),Mp=ct?";":":",io=oo(),ao=e=>Object.assign(new Error(`not found: ${e}`),{code:"ENOENT"}),po=(e,t)=>{let r=t.colon||Mp,o=e.match(/\//)||ct&&e.match(/\\/)?[""]:[...ct?[process.cwd()]:[],...(t.path||process.env.PATH||"").split(r)],n=ct?t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",s=ct?n.split(r):[""];return ct&&e.indexOf(".")!==-1&&s[0]!==""&&s.unshift(""),{pathEnv:o,pathExt:s,pathExtExe:n}},co=(e,t,r)=>{typeof t=="function"&&(r=t,t={}),t||(t={});let{pathEnv:o,pathExt:n,pathExtExe:s}=po(e,t),i=[],a=m=>new Promise((l,u)=>{if(m===o.length)return t.all&&i.length?l(i):u(ao(e));let f=o[m],g=/^".*"$/.test(f)?f.slice(1,-1):f,x=so.join(g,e),h=!g&&/^\.[\\\/]/.test(e)?e.slice(0,2)+x:x;l(c(h,m,0));}),c=(m,l,u)=>new Promise((f,g)=>{if(u===n.length)return f(a(l+1));let x=n[u];io(m+x,{pathExt:s},(h,C)=>{if(!h&&C)if(t.all)i.push(m+x);else return f(m+x);return f(c(m,l,u+1))});});return r?a(0).then(m=>r(null,m),r):a(0)},Fp=(e,t)=>{t=t||{};let{pathEnv:r,pathExt:o,pathExtExe:n}=po(e,t),s=[];for(let i=0;i<r.length;i++){let a=r[i],c=/^".*"$/.test(a)?a.slice(1,-1):a,m=so.join(c,e),l=!c&&/^\.[\\\/]/.test(e)?e.slice(0,2)+m:m;for(let u=0;u<o.length;u++){let f=l+o[u];try{if(io.sync(f,{pathExt:n}))if(t.all)s.push(f);else return f}catch{}}}if(t.all&&s.length)return s;if(t.nothrow)return null;throw ao(e)};mo.exports=co;co.sync=Fp;});var fo=Y((au,Ur)=>{var uo=(e={})=>{let t=e.env||process.env;return (e.platform||process.platform)!=="win32"?"PATH":Object.keys(t).reverse().find(o=>o.toUpperCase()==="PATH")||"Path"};Ur.exports=uo;Ur.exports.default=uo;});var xo=Y((pu,ho)=>{var yo=ye("path"),Lp=lo(),Hp=fo();function go(e,t){let r=e.options.env||process.env,o=process.cwd(),n=e.options.cwd!=null,s=n&&process.chdir!==void 0&&!process.chdir.disabled;if(s)try{process.chdir(e.options.cwd);}catch{}let i;try{i=Lp.sync(e.command,{path:r[Hp({env:r})],pathExt:t?yo.delimiter:void 0});}catch{}finally{s&&process.chdir(o);}return i&&(i=yo.resolve(n?e.options.cwd:"",i)),i}function _p(e){return go(e)||go(e,!0)}ho.exports=_p;});var bo=Y((cu,Xr)=>{var Qr=/([()\][%!^"`<>&|;, *?])/g;function Bp(e){return e=e.replace(Qr,"^$1"),e}function Vp(e,t){return e=`${e}`,e=e.replace(/(?=(\\+?)?)\1"/g,'$1$1\\"'),e=e.replace(/(?=(\\+?)?)\1$/,"$1$1"),e=`"${e}"`,e=e.replace(Qr,"^$1"),t&&(e=e.replace(Qr,"^$1")),e}Xr.exports.command=Bp;Xr.exports.argument=Vp;});var Ro=Y((mu,Oo)=>{Oo.exports=/^#!(.*)/;});var Co=Y((lu,To)=>{var Kp=Ro();To.exports=(e="")=>{let t=e.match(Kp);if(!t)return null;let[r,o]=t[0].replace(/#! ?/,"").split(" "),n=r.split("/").pop();return n==="env"?o:o?`${n} ${o}`:n};});var So=Y((uu,Po)=>{var zr=ye("fs"),Wp=Co();function Up(e){let r=Buffer.alloc(150),o;try{o=zr.openSync(e,"r"),zr.readSync(o,r,0,150,0),zr.closeSync(o);}catch{}return Wp(r.toString())}Po.exports=Up;});var vo=Y((fu,Eo)=>{var Qp=ye("path"),Ao=xo(),Io=bo(),Xp=So(),zp=process.platform==="win32",Gp=/\.(?:com|exe)$/i,Jp=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function Zp(e){e.file=Ao(e);let t=e.file&&Xp(e.file);return t?(e.args.unshift(e.file),e.command=t,Ao(e)):e.file}function Yp(e){if(!zp)return e;let t=Zp(e),r=!Gp.test(t);if(e.options.forceShell||r){let o=Jp.test(t);e.command=Qp.normalize(e.command),e.command=Io.command(e.command),e.args=e.args.map(s=>Io.argument(s,o));let n=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${n}"`],e.command=process.env.comspec||"cmd.exe",e.options.windowsVerbatimArguments=!0;}return e}function ec(e,t,r){t&&!Array.isArray(t)&&(r=t,t=null),t=t?t.slice(0):[],r=Object.assign({},r);let o={command:e,args:t,options:r,file:void 0,original:{command:e,args:t}};return r.shell?o:Yp(o)}Eo.exports=ec;});var jo=Y((du,qo)=>{var Gr=process.platform==="win32";function Jr(e,t){return Object.assign(new Error(`${t} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${t} ${e.command}`,path:e.command,spawnargs:e.args})}function tc(e,t){if(!Gr)return;let r=e.emit;e.emit=function(o,n){if(o==="exit"){let s=ko(n,t);if(s)return r.call(e,"error",s)}return r.apply(e,arguments)};}function ko(e,t){return Gr&&e===1&&!t.file?Jr(t.original,"spawn"):null}function rc(e,t){return Gr&&e===1&&!t.file?Jr(t.original,"spawnSync"):null}qo.exports={hookChildProcess:tc,verifyENOENT:ko,verifyENOENTSync:rc,notFoundError:Jr};});var $o=Y((yu,mt)=>{var No=ye("child_process"),Zr=vo(),Yr=jo();function wo(e,t,r){let o=Zr(e,t,r),n=No.spawn(o.command,o.args,o.options);return Yr.hookChildProcess(n,o),n}function nc(e,t,r){let o=Zr(e,t,r),n=No.spawnSync(o.command,o.args,o.options);return n.error=n.error||Yr.verifyENOENTSync(n.status,o),n}mt.exports=wo;mt.exports.spawn=wo;mt.exports.sync=nc;mt.exports._parse=Zr;mt.exports._enoent=Yr;});var Kn=qp($o());var en=/^[^$_\p{ID_Start}]+/u,oe=/^(arguments|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|eval|export|extends|false|finally|for|function|if|implements|import|in|instanceof|interface|let|new|null|package|private|protected|public|return|static|super|switch|this|throw|true|try|typeof|var|void|while|with|yield)$/g,ue=/^[$_\p{ID_Start}][$\u200c\u200d\p{ID_Continue}]*$/u;var se=e=>(e||e==="")&&(ue.lastIndex=0,!ue.test(e))?`'${e}'`:e,lt=e=>e&&e.startsWith("'")&&e.endsWith("'")?e.slice(1,e.length-1):e,A=e=>e.replace(/\*\//g,"*").replace(/\/\*/g,"*").replace(/\r?\n(.*)/g,(t,r)=>os$1.EOL+r.trim());var Do,b=()=>Do,Mo=e=>(Do=e,b()),P=e=>("client"in e?e.client.name:e.name).startsWith("legacy/"),j=e=>{if(P(e))return e.name};var sc=y__default.default.createPrinter({newLine:y__default.default.NewLineKind.LineFeed,removeComments:!1}),Fo=e=>y__default.default.createSourceFile("",e,y__default.default.ScriptTarget.ESNext,!1,y__default.default.ScriptKind.TS),ic=Fo(""),ac=e=>e.replace(/\\u([0-9a-fA-F]{4})/g,(t,r)=>String.fromCharCode(Number.parseInt(r,16)));function ge({node:e,unescape:t=!1}){let r=sc.printNode(y__default.default.EmitHint.Unspecified,e,ic);if(!t)return r;try{return ac(r)}catch{return b().debug&&console.warn("Could not decode value:",r),r}}function Lo(e){return Fo(e).statements[0]}var R=({text:e})=>y__default.default.factory.createIdentifier(e),ee={boolean:e=>e?y__default.default.factory.createTrue():y__default.default.factory.createFalse(),export:({alias:e,asType:t=!1,name:r})=>{let o=R({text:r});if(e){let n=R({text:e});return y__default.default.factory.createExportSpecifier(t,o,n)}return y__default.default.factory.createExportSpecifier(t,void 0,o)},import:({alias:e,asType:t=!1,name:r})=>{let o=R({text:r});if(e){let n=R({text:e});return y__default.default.factory.createImportSpecifier(t,o,n)}return y__default.default.factory.createImportSpecifier(t,void 0,o)},number:e=>e<0?y__default.default.factory.createPrefixUnaryExpression(y__default.default.SyntaxKind.MinusToken,y__default.default.factory.createNumericLiteral(Math.abs(e))):y__default.default.factory.createNumericLiteral(e),string:(e,t=!1)=>{let r=e;t&&(r=lt(r));let o=r.includes("'")&&r.includes('"'),n=r.includes(`
`),s=r.startsWith("`"),i=r.startsWith("\\`")&&r.endsWith("\\`");return (n||o||s)&&!i&&(r=`\`${r.replace(/(?<!\\)`/g,"\\`").replace(/\${/g,"\\${")}\``),r.startsWith("`")?R({text:r}):ut({text:r})}},Ze=e=>e!==null&&typeof e=="object"&&typeof e.kind=="number"&&typeof e.flags=="number"&&typeof e.pos=="number"&&typeof e.end=="number",Le=e=>e!==void 0,pc=({commentObject:e,node:t})=>{let r=e.lines.filter(i=>!!i||i==="");if(!r.length)return;if(!e.jsdoc){for(let i of r)y__default.default.addSyntheticLeadingComment(t,y__default.default.SyntaxKind.SingleLineCommentTrivia,` ${i}`,!0);return}let o=r.map((i,a)=>{let c=i;return a!==r.length&&(c=`${c}
`),y__default.default.factory.createJSDocText(c)}),n=y__default.default.factory.createJSDocComment(y__default.default.factory.createNodeArray(o),void 0),s=ge({node:n,unescape:!0}).replace("/*","").replace("*  */","");y__default.default.addSyntheticLeadingComment(t,y__default.default.SyntaxKind.MultiLineCommentTrivia,s,!0);},H=({comments:e=[],node:t})=>{let r=!!e.find(n=>typeof n=="object"&&n),o=e;r||(o=[{jsdoc:!0,lines:e}]);for(let n of o)pc({commentObject:n,node:t});};var W=(e,t)=>y__default.default.isTypeNode(e)?e:typeof e=="number"?y__default.default.factory.createLiteralTypeNode(ee.number(e)):fe({typeArguments:t?.map(r=>W(r)),typeName:e}),tn=({expression:e,name:t})=>y__default.default.factory.createPropertyAccessChain(e,y__default.default.factory.createToken(y__default.default.SyntaxKind.QuestionDotToken),t),xe=({expression:e,isOptional:t,name:r})=>{let o=typeof e=="string"?R({text:e}):e;if(t)return tn({expression:o,name:r});if(typeof r=="string"&&(ue.lastIndex=0,!ue.test(r))){!r.startsWith("'")&&!r.endsWith("'")&&(r=`'${r}'`);let s=R({text:r});return y__default.default.factory.createElementAccessExpression(o,s)}let n=typeof r=="string"?R({text:r}):r;return y__default.default.factory.createPropertyAccessExpression(o,n)},rn=()=>y__default.default.factory.createNull(),he=({identifiers:e=[],isValueAccess:t,shorthand:r,unescape:o,value:n})=>{if(n===null)return rn();if(Array.isArray(n))return nn({elements:n});if(typeof n=="object")return rr({identifiers:e,obj:n,shorthand:r});if(typeof n=="number")return ee.number(n);if(typeof n=="boolean")return ee.boolean(n);if(typeof n=="string"){if(t){let s=n.split(".");return xe({expression:s[0],name:s[1]})}return ee.string(n,o)}},er=e=>{let t=e==="public"?y__default.default.SyntaxKind.PublicKeyword:e==="protected"?y__default.default.SyntaxKind.ProtectedKeyword:e==="private"?y__default.default.SyntaxKind.PrivateKeyword:void 0,r=[];return t&&r.push(y__default.default.factory.createModifier(t)),r},wt=e=>e.map(t=>{if("destructure"in t)return ft({name:y__default.default.factory.createObjectBindingPattern(t.destructure.map(o=>"destructure"in o?void 0:y__default.default.factory.createBindingElement(void 0,void 0,R({text:o.name}),void 0)).filter(Boolean))});let r=er(t.accessLevel);return t.isReadOnly&&(r=[...r,y__default.default.factory.createModifier(y__default.default.SyntaxKind.ReadonlyKeyword)]),ft({initializer:t.default!==void 0?he({value:t.default}):void 0,modifiers:r,name:R({text:t.name}),required:t.isRequired!==!1,type:t.type!==void 0?W(t.type):void 0})}),dt=({keyword:e})=>{let t=y__default.default.SyntaxKind.AnyKeyword;switch(e){case"boolean":t=y__default.default.SyntaxKind.BooleanKeyword;break;case"never":t=y__default.default.SyntaxKind.NeverKeyword;break;case"number":t=y__default.default.SyntaxKind.NumberKeyword;break;case"string":t=y__default.default.SyntaxKind.StringKeyword;break;case"undefined":t=y__default.default.SyntaxKind.UndefinedKeyword;break;case"unknown":t=y__default.default.SyntaxKind.UnknownKeyword;break;case"void":t=y__default.default.SyntaxKind.VoidKeyword;break}return y__default.default.factory.createKeywordTypeNode(t)},$t=e=>e.map(t=>y__default.default.factory.createTypeParameterDeclaration(void 0,t.name,t.extends?typeof t.extends=="string"?dt({keyword:"boolean"}):t.extends:void 0,t.default!==void 0?Ze(t.default)?t.default:y__default.default.factory.createLiteralTypeNode(t.default?y__default.default.factory.createTrue():y__default.default.factory.createFalse()):void 0)),Ho=({literal:e})=>y__default.default.factory.createLiteralTypeNode(e),tr=({async:e,comment:t,multiLine:r,parameters:o=[],returnType:n,statements:s=[],types:i=[]})=>{let a=y__default.default.factory.createArrowFunction(e?[y__default.default.factory.createModifier(y__default.default.SyntaxKind.AsyncKeyword)]:void 0,i?$t(i):void 0,wt(o),n?W(n):void 0,void 0,Array.isArray(s)?ie({multiLine:r,statements:s}):s);return H({comments:t,node:a}),a},_o=({async:e,comment:t,multiLine:r,parameters:o=[],returnType:n,statements:s=[],types:i=[]})=>{let a=y__default.default.factory.createFunctionExpression(e?[y__default.default.factory.createModifier(y__default.default.SyntaxKind.AsyncKeyword)]:void 0,void 0,void 0,i?$t(i):void 0,wt(o),n?W(n):void 0,ie({multiLine:r,statements:s}));return H({comments:t,node:a}),a},nn=({elements:e,multiLine:t=!1})=>y__default.default.factory.createArrayLiteralExpression(e.map(o=>Ze(o)?o:he({value:o})).filter(Le),t||!Array.isArray(e[0])&&typeof e[0]=="object"),Bo=({expression:e})=>y__default.default.factory.createAwaitExpression(e),Vo=({parameters:e=[],returnType:t,typeParameters:r})=>y__default.default.factory.createFunctionTypeNode(r,e,t),rr=({comments:e,identifiers:t=[],multiLine:r=!0,obj:o,shorthand:n,unescape:s=!1})=>{let i=Array.isArray(o)?o.map(c=>{let m=!1;if("key"in c){let{key:u}=c;m=u===c.value,(u.match(/^[0-9]/)&&u.match(/\D+/g)||u.match(/\W/g))&&!u.startsWith("'")&&!u.endsWith("'")&&(c.key=`'${u}'`);}let l;if("spread"in c){let u=Ze(c.spread)?c.spread:R({text:c.spread});l=y__default.default.factory.createSpreadAssignment(c.assertion?y__default.default.factory.createAsExpression(u,dt({keyword:c.assertion})):u);}else if(c.shorthand||n&&m)l=y__default.default.factory.createShorthandPropertyAssignment(c.value);else {let u=Ze(c.value)?c.value:Array.isArray(c.value)?rr({multiLine:r,obj:c.value,shorthand:n,unescape:s}):he({identifiers:t.includes(c.key)?Object.keys(c.value):[],isValueAccess:c.isValueAccess,shorthand:n,unescape:s,value:c.value});if(!u)return;t.includes(c.key)&&!y__default.default.isObjectLiteralExpression(u)&&(u=R({text:c.value})),l=Yt({initializer:u,name:c.key});}return H({comments:c.comments,node:l}),l}).filter(Le):Object.entries(o).map(([c,m])=>{let l=he({identifiers:t.includes(c)?Object.keys(m):[],shorthand:n,unescape:s,value:m});if(!l)return;t.includes(c)&&!y__default.default.isObjectLiteralExpression(l)&&(l=R({text:m}));let u=c===m;return c.match(/^[0-9]/)&&c.match(/\D+/g)&&!c.startsWith("'")&&!c.endsWith("'")&&(c=`'${c}'`),c.match(/\W/g)&&!c.startsWith("'")&&!c.endsWith("'")&&(c=`'${c}'`),n&&u?y__default.default.factory.createShorthandPropertyAssignment(m):Yt({initializer:l,name:c})}).filter(Le),a=y__default.default.factory.createObjectLiteralExpression(i,r);return H({comments:e,node:a}),a},Ko=({comments:e={},leadingComment:t,name:r,obj:o})=>{let n=Array.isArray(o)?o.map(i=>{let a=y__default.default.factory.createEnumMember(se(i.key),he({value:i.value}));return H({comments:i.comments,node:a}),a}):Object.entries(o).map(([i,a])=>{let c=he({unescape:!0,value:a}),m=y__default.default.factory.createEnumMember(i,c);return H({comments:e[i],node:m}),m}),s=y__default.default.factory.createEnumDeclaration([y__default.default.factory.createModifier(y__default.default.SyntaxKind.ExportKeyword)],R({text:r}),n);return H({comments:t,node:s}),s},Wo=({name:e,statements:t})=>y__default.default.factory.createModuleDeclaration([y__default.default.factory.createModifier(y__default.default.SyntaxKind.ExportKeyword)],R({text:e}),y__default.default.factory.createModuleBlock(t),y__default.default.NodeFlags.Namespace),Uo=({indexType:e,objectType:t})=>y__default.default.factory.createIndexedAccessTypeNode(t,e),ut=({isSingleQuote:e,text:t})=>(e===void 0&&(e=!t.includes("'")),y__default.default.factory.createStringLiteral(t,e)),Qo=({condition:e,whenFalse:t,whenTrue:r})=>y__default.default.factory.createConditionalExpression(e,y__default.default.factory.createToken(y__default.default.SyntaxKind.QuestionToken),r,y__default.default.factory.createToken(y__default.default.SyntaxKind.ColonToken),t),Xo=({text:e})=>y__default.default.factory.createTypeOfExpression(R({text:e})),zo=({comment:e,exportType:t,name:r,type:o,typeParameters:n=[]})=>{let s=y__default.default.factory.createTypeAliasDeclaration(t?[y__default.default.factory.createModifier(y__default.default.SyntaxKind.ExportKeyword)]:void 0,R({text:r}),$t(n),W(o));return H({comments:e,node:s}),s},fe=({typeArguments:e,typeName:t})=>y__default.default.factory.createTypeReferenceNode(t,e),Go=({type:e})=>y__default.default.factory.createParenthesizedType(e),ft=({initializer:e,modifiers:t,name:r,required:o=!0,type:n})=>y__default.default.factory.createParameterDeclaration(t,void 0,r,o?void 0:y__default.default.factory.createToken(y__default.default.SyntaxKind.QuestionToken),n,e),Dt=({argumentsArray:e,expression:t,typeArguments:r})=>y__default.default.factory.createNewExpression(t,r,e),Jo=({awaitModifier:e,expression:t,initializer:r,statement:o})=>y__default.default.factory.createForOfStatement(e,r,t,o),Zo=({left:e,right:t})=>y__default.default.factory.createAssignment(e,t),ie=({multiLine:e=!0,statements:t})=>y__default.default.factory.createBlock(t,e),Yt=({initializer:e,name:t})=>y__default.default.factory.createPropertyAssignment(t,e);var Yo=({module:e})=>y__default.default.factory.createExportDeclaration(void 0,!1,void 0,ee.string(e)),be=({functionName:e,parameters:t=[],types:r})=>{let o=typeof e=="string"?R({text:e}):e,n=t.filter(i=>i!==void 0).map(i=>typeof i=="string"?R({text:i}):i);return y__default.default.factory.createCallExpression(o,r,n)},es=({exports:e,module:t})=>{let r=Array.isArray(e)?e:[e],o=r.some(c=>typeof c!="object"||!c.asType),n=r.map(c=>{let m=typeof c=="string"?{name:c}:c;return ee.export({alias:m.alias,asType:o&&m.asType,name:m.name})}),s=y__default.default.factory.createNamedExports(n),i=ee.string(t);return y__default.default.factory.createExportDeclaration(void 0,!o,s,i)},ts=({assertion:e,comment:t,destructure:r,exportConst:o,expression:n,name:s,typeName:i})=>{let a=e?y__default.default.factory.createAsExpression(n,typeof e=="string"?fe({typeName:e}):e):n,c=R({text:s}),m=y__default.default.factory.createVariableDeclaration(r?y__default.default.factory.createObjectBindingPattern([y__default.default.factory.createBindingElement(void 0,void 0,c,void 0)]):c,void 0,i?typeof i=="string"?fe({typeName:i}):i:void 0,a),l=y__default.default.factory.createVariableStatement(o?[y__default.default.factory.createModifier(y__default.default.SyntaxKind.ExportKeyword)]:void 0,y__default.default.factory.createVariableDeclarationList([m],y__default.default.NodeFlags.Const));return H({comments:t,node:l}),l},rs=({imports:e,module:t})=>{let r=Array.isArray(e)?e:[e],o=r.some(m=>typeof m!="object"||!m.asType),n=r.map(m=>{let l=typeof m=="string"?{name:m}:m;return ee.import({alias:l.alias,asType:o&&l.asType,name:l.name})}),s=y__default.default.factory.createNamedImports(n),i=y__default.default.factory.createImportClause(!o,void 0,s),a=ee.string(t);return y__default.default.factory.createImportDeclaration(void 0,i,a)};var ns=({accessLevel:e,comment:t,multiLine:r=!0,parameters:o=[],statements:n=[]})=>{let s=y__default.default.factory.createConstructorDeclaration(er(e),wt(o),ie({multiLine:r,statements:n}));return H({comments:t,node:s}),s},os=({accessLevel:e,comment:t,isStatic:r=!1,multiLine:o=!0,name:n,parameters:s=[],returnType:i,statements:a=[],types:c=[]})=>{let m=er(e);r&&(m=[...m,y__default.default.factory.createModifier(y__default.default.SyntaxKind.StaticKeyword)]);let l=y__default.default.factory.createMethodDeclaration(m,void 0,R({text:n}),void 0,c?$t(c):void 0,wt(s),i?W(i):void 0,ie({multiLine:o,statements:a}));return H({comments:t,node:l}),l},ss=({decorator:e,members:t=[],name:r})=>{let o=[y__default.default.factory.createModifier(y__default.default.SyntaxKind.ExportKeyword)];e&&(o=[y__default.default.factory.createDecorator(be({functionName:e.name,parameters:e.args.map(s=>he({value:s})).filter(Le)})),...o]);let n=[];return t.forEach(s=>{n=[...n,s,R({text:`
`})];}),y__default.default.factory.createClassDeclaration(o,R({text:r}),[],[],n)};var Ye=({expression:e})=>y__default.default.factory.createExpressionStatement(e);var nr=({expression:e})=>y__default.default.factory.createReturnStatement(e),as=({args:e=[],name:t,types:r=[]})=>{let o=r.map(a=>fe({typeName:a})),n=e.map(a=>y__default.default.isExpression(a)?a:R({text:a})).filter(Le),s=be({functionName:t,parameters:n,types:o});return nr({expression:s})},ps=({expression:e})=>nr({expression:typeof e=="string"?R({text:e}):e});var yt=e=>e.slice(1).reduce((t,r)=>(ue.lastIndex=0,ue.test(r)?tn({expression:t,name:r}):y__default.default.factory.createElementAccessChain(t,y__default.default.factory.createToken(y__default.default.SyntaxKind.QuestionDotToken),R({text:r}))),R({text:e[0]})),or=e=>e.slice(1).reduce((t,r)=>xe({expression:t,name:r}),R({text:e[0]})),cs=({expressions:e})=>e.reduce((r,o)=>xe({expression:r,name:o})),ms=({left:e,operator:t="=",right:r})=>y__default.default.factory.createBinaryExpression(e,t==="="?y__default.default.SyntaxKind.EqualsToken:t==="==="?y__default.default.SyntaxKind.EqualsEqualsEqualsToken:y__default.default.SyntaxKind.InKeyword,typeof r=="string"?R({text:r}):r),gt=({elseStatement:e,expression:t,thenStatement:r})=>y__default.default.factory.createIfStatement(t,r,e),ls=({path:e})=>{let t=yt(e),r=or(e),o=ie({statements:[Ye({expression:y__default.default.factory.createBinaryExpression(r,y__default.default.SyntaxKind.EqualsToken,Dt({argumentsArray:[r],expression:R({text:"Date"})}))})]});return gt({expression:t,thenStatement:o})},us=({path:e,transformerName:t})=>{let r=yt(e),o=or(e),n=ie({statements:[Ye({expression:be({functionName:t,parameters:[o]})})]});return [gt({expression:r,thenStatement:n})]},fs=({path:e,transformerName:t})=>{let r=yt(e),o=or(e);return gt({expression:be({functionName:xe({expression:"Array",name:"isArray"}),parameters:[r]}),thenStatement:ie({statements:[Ye({expression:y__default.default.factory.createCallChain(xe({expression:o,name:"forEach"}),void 0,void 0,[R({text:t})])})]})})},ds=({parameterName:e})=>{let t=R({text:"Date"});return Dt({argumentsArray:[R({text:e})],expression:t})},ys=({path:e,transformExpression:t})=>{let r=yt(e),o=or(e);return gt({expression:be({functionName:xe({expression:"Array",name:"isArray"}),parameters:[r]}),thenStatement:ie({statements:[Ye({expression:y__default.default.factory.createBinaryExpression(o,y__default.default.factory.createToken(y__default.default.SyntaxKind.EqualsToken),y__default.default.factory.createCallChain(xe({expression:o,name:"map"}),void 0,void 0,[tr({parameters:[{name:"item"}],statements:t})]))})]})})};var hc=fe({typeName:"null"}),ht=({isNullable:e,node:t})=>e?y__default.default.factory.createUnionTypeNode([t,hc]):t,on=({indexProperty:e,isNullable:t,properties:r,useLegacyResolution:o})=>{let s=r.map(a=>{let c=a.isReadOnly?[y__default.default.factory.createModifier(y__default.default.SyntaxKind.ReadonlyKeyword)]:void 0,m=a.isRequired!==!1?void 0:y__default.default.factory.createToken(y__default.default.SyntaxKind.QuestionToken),l=W(a.type);let u=y__default.default.factory.createPropertySignature(c,o||typeof a.name=="string"&&a.name.match(ue)||typeof a.name!="string"&&y__default.default.isPropertyName(a.name)?a.name:ut({text:a.name}),m,l);return H({comments:a.comment,node:u}),u});if(e){let a=e.isReadOnly?[y__default.default.factory.createModifier(y__default.default.SyntaxKind.ReadonlyKeyword)]:void 0,c=y__default.default.factory.createIndexSignature(a,[ft({name:R({text:String(e.name)}),type:dt({keyword:"string"})})],W(e.type));s.push(c);}let i=y__default.default.factory.createTypeLiteralNode(s);return ht({isNullable:t,node:i})},Mt=({isNullable:e,types:t})=>{let r=t.map(n=>W(n)),o=y__default.default.factory.createUnionTypeNode(r);return ht({isNullable:e,node:o})},gs=({isNullable:e,types:t})=>{let r=t.map(n=>W(n)),o=y__default.default.factory.createIntersectionTypeNode(r);return ht({isNullable:e,node:o})},hs=({isNullable:e=!1,types:t})=>{let r=t.map(n=>W(n)),o=y__default.default.factory.createTupleTypeNode(r);return ht({isNullable:e,node:o})},xs=(e,t,r=!1,o=!0)=>{let n=Mt({types:e}),s=Mt({types:t}),i=on({properties:[{name:`[key: ${ge({node:n,unescape:!0})}]`,type:s}],useLegacyResolution:o});return ht({isNullable:r,node:i})},bs=(e,t=!1)=>{let r=fe({typeArguments:[Array.isArray(e)?Mt({types:e}):e],typeName:"Array"});return ht({isNullable:t,node:r})};var p={anonymousFunction:_o,arrayLiteralExpression:nn,arrowFunction:tr,assignment:Zo,awaitExpression:Bo,binaryExpression:ms,block:ie,callExpression:be,classDeclaration:ss,conditionalExpression:Qo,constVariable:ts,constructorDeclaration:ns,enumDeclaration:Ko,exportAllDeclaration:Yo,exportNamedDeclaration:es,expressionToStatement:Ye,forOfStatement:Jo,functionTypeNode:Vo,identifier:R,ifStatement:gt,indexedAccessTypeNode:Uo,isTsNode:Ze,keywordTypeNode:dt,literalTypeNode:Ho,methodDeclaration:os,namedImportDeclarations:rs,namespaceDeclaration:Wo,newExpression:Dt,nodeToString:ge,null:rn,objectExpression:rr,ots:ee,parameterDeclaration:ft,propertyAccessExpression:xe,propertyAccessExpressions:cs,propertyAssignment:Yt,returnFunctionCall:as,returnStatement:nr,returnVariable:ps,safeAccessExpression:yt,stringLiteral:ut,stringToTsNodes:Lo,transformArrayMap:ys,transformArrayMutation:fs,transformDateMutation:ls,transformFunctionMutation:us,transformNewDate:ds,typeAliasDeclaration:zo,typeArrayNode:bs,typeInterfaceNode:on,typeIntersectionNode:gs,typeNode:W,typeOfExpression:Xo,typeParenthesizedNode:Go,typeRecordNode:xs,typeReferenceNode:fe,typeTupleNode:hs,typeUnionNode:Mt,valueToExpression:he};var Os=async({context:e})=>{if(await e.broadcast("before"),e.ir.components){for(let t in e.ir.components.schemas){let r=e.ir.components.schemas[t],o=`#/components/schemas/${t}`;await e.broadcast("schema",{$ref:o,name:t,schema:r});}for(let t in e.ir.components.parameters){let r=e.ir.components.parameters[t],o=`#/components/parameters/${t}`;await e.broadcast("parameter",{$ref:o,name:t,parameter:r});}}for(let t in e.ir.paths){let r=e.ir.paths[t];for(let o in r){let n=o,s=r[n];await e.broadcast("operation",{method:n,operation:s,path:t});}}await e.broadcast("after");};var sr=e=>{switch(e.name){case"legacy/angular":return "AngularHttpRequest";case"legacy/axios":return "AxiosHttpRequest";case"legacy/fetch":return "FetchHttpRequest";case"legacy/node":return "NodeHttpRequest";case"legacy/xhr":return "XHRHttpRequest";default:return ""}};function xt(e,t){let r=e.toLocaleLowerCase(),o=t.toLocaleLowerCase();return r.localeCompare(o,"en")}var Ft=(e,t)=>xt(e.name,t.name);function Lt(e){return e.sort(Ft)}var bt=e=>{fs$1.existsSync(e)||fs$1.mkdirSync(e,{recursive:!0});},Ht=({moduleOutput:e,sourceOutput:t})=>{let r=t.split("/");return `${new Array(r.length).fill("").join("../")||"./"}${e}`};var Rs=async(e,t,r,o)=>{let n=b(),s=o.client({$config:n,...r,httpRequest:sr(n.client),models:Lt(r.models),services:Lt(r.services)});j(n)&&(bt(t),fs$1.writeFileSync(ae__default.default.resolve(t,`${j(n)}.ts`),s));};var Q=({config:e,sourceOutput:t})=>e.client.bundle?Ht({moduleOutput:"client",sourceOutput:t}):e.client.name,te=()=>"Options",sn=({name:e,outputPath:t})=>{let r=ae__default.default.resolve(t,"client");bt(r);let n=ae__default.default.normalize(ye.resolve(e)).split(ae__default.default.sep),s=[...n.slice(0,n.indexOf("dist")),"src"].join(ae__default.default.sep);["index.ts","types.ts","utils.ts"].forEach(a=>{fs$1.copyFileSync(ae__default.default.resolve(s,a),ae__default.default.resolve(r,a));});};var Ts=async(e,t,r)=>{let o=b();if(o.exportCore){let n={httpRequest:sr(o.client),server:o.base!==void 0?o.base:t.server,version:t.version};if(fs$1.rmSync(ae__default.default.resolve(e),{force:!0,recursive:!0}),fs$1.mkdirSync(ae__default.default.resolve(e),{recursive:!0}),await fs$1.writeFileSync(ae__default.default.resolve(e,"OpenAPI.ts"),r.core.settings({$config:o,...n})),await fs$1.writeFileSync(ae__default.default.resolve(e,"ApiError.ts"),r.core.apiError({$config:o,...n})),await fs$1.writeFileSync(ae__default.default.resolve(e,"ApiRequestOptions.ts"),r.core.apiRequestOptions({$config:o,...n})),await fs$1.writeFileSync(ae__default.default.resolve(e,"ApiResult.ts"),r.core.apiResult({$config:o,...n})),o.client.name!=="legacy/angular"&&await fs$1.writeFileSync(ae__default.default.resolve(e,"CancelablePromise.ts"),r.core.cancelablePromise({$config:o,...n})),await fs$1.writeFileSync(ae__default.default.resolve(e,"request.ts"),r.core.request({$config:o,...n})),j(o)&&(await fs$1.writeFileSync(ae__default.default.resolve(e,"BaseHttpRequest.ts"),r.core.baseHttpRequest({$config:o,...n})),await fs$1.writeFileSync(ae__default.default.resolve(e,`${n.httpRequest}.ts`),r.core.httpRequest({$config:o,...n}))),o.request){let s=ae__default.default.resolve(process.cwd(),o.request);if(!await fs$1.existsSync(s))throw new Error(`Custom request file "${s}" does not exists`);await fs$1.copyFileSync(s,ae__default.default.resolve(e,"request.ts"));}}};var Rt="#/ir/",Tt=e=>{let t=an(e);return t.length===3&&t[0]==="components"},Ct=e=>{let t=an(e),r=t[t.length-1];return decodeURI(r)},an=e=>e.replace(/^#\//,"").split("/"),pn=({$ref:e,spec:t})=>{let r=an(decodeURI(e)),o=t;for(let n of r){let s=n;if(o[s]===void 0)throw new Error(`Reference not found: ${e}`);o=o[s];}return o};var ir=class{config;files;ir;spec;listeners;constructor({config:t,spec:r}){this.config=t,this.files={},this.ir={},this.listeners={},this.spec=r;}async broadcast(t,...r){this.listeners[t]&&await Promise.all(this.listeners[t].map((o,n)=>{try{let s=o(...r);return Promise.resolve(s)}catch(s){throw console.error(`\u{1F525} Event broadcast: "${t}"
index: ${n}
arguments: ${JSON.stringify(r,null,2)}`),s}}));}createFile(t){let r=t.path.split("/"),o=ae__default.default.resolve(this.config.output.path,...r.slice(0,r.length-1)),n=new Z({dir:o,name:`${r[r.length-1]}.ts`});return this.files[t.id]=n,n}file({id:t}){return this.files[t]}resolveIrRef(t){return pn({$ref:t,spec:this.ir})}resolveRef(t){return pn({$ref:t,spec:this.spec})}subscribe(t,r){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push(r);}};var E=({$ref:e,excludeRegExp:t,includeRegExp:r})=>!t&&!r?!0:t&&(t.lastIndex=0,t.test(e))?!1:r?(r.lastIndex=0,r.test(e)):!0;var vc=/[\p{Lu}]/u,kc=/[\p{Ll}]/u,Ss=/([\p{Alpha}\p{N}_]|$)/u,cn=/[_.\- ]+/,qc=new RegExp("^"+cn.source),Cs=new RegExp(cn.source+Ss.source,"gu"),Ps=new RegExp("\\d+"+Ss.source,"gu"),jc=e=>{let t=!1,r=!1,o=!1,n=!1;for(let s=0;s<e.length;s++){let i=e[s];n=s>2?e[s-3]==="-":!0,t&&vc.test(i)?(e=e.slice(0,s)+"-"+e.slice(s),t=!1,o=r,r=!0,s++):r&&o&&kc.test(i)&&!n?(e=e.slice(0,s-1)+"-"+e.slice(s-1),o=r,r=!1,t=!0):(t=i.toLocaleLowerCase()===i&&i.toLocaleUpperCase()!==i,o=r,r=i.toLocaleUpperCase()===i&&i.toLocaleLowerCase()!==i);}return e},I=({input:e,pascalCase:t})=>{let r=e.trim();return r.length?r.length===1?cn.test(r)?"":t?r.toLocaleUpperCase():r.toLocaleLowerCase():(r!==r.toLocaleLowerCase()&&(r=jc(r)),r=r.replace(qc,""),r=r.toLocaleLowerCase(),t&&(r=r.charAt(0).toLocaleUpperCase()+r.slice(1)),Cs.lastIndex=0,Ps.lastIndex=0,r=r.replaceAll(Ps,(n,s,i)=>["_","-","."].includes(r.charAt(i+n.length))?n:n.toLocaleUpperCase()),r=r.replaceAll(Cs,(n,s)=>s.toLocaleUpperCase()),r):""};var X=e=>{en.lastIndex=0;let t=e.replace(/[^$\u200c\u200d\p{ID_Continue}]/gu,"_");return en.test(t)?`_${t}`:t},Be=e=>e.replace(/^[^\p{ID_Start}]+/u,"").replace(/[^$\u200c\u200d\p{ID_Continue}]/gu,"-").replace(/\$/g,"-"),mn=e=>{let t=e.replace("[]","Array");return Be(t)};var ar=({context:e,id:t,method:r,path:o})=>{if(t&&(!e.config.plugins["@hey-api/sdk"]||e.config.plugins["@hey-api/sdk"].operationId))return I({input:Be(t)});let n=o.replace(/{(.*?)}/g,"by-$1").replace(/[/:]/g,"-");return I({input:`${r}-${n}`})};var As=/^(application\/(pdf|rtf|msword|vnd\.(ms-|openxmlformats-officedocument\.)|zip|x-(7z|tar|rar|zip|iso)|octet-stream|gzip|x-msdownload|json\+download|xml|x-yaml|x-7z-compressed|x-tar)|text\/(plain|yaml|css|javascript)|audio\/(mpeg|wav)|video\/(mp4|x-matroska)|image\/(vnd\.adobe\.photoshop|svg\+xml))(; ?charset=[^;]+)?$/i,Is=/^application\/(.*\+)?json(;.*)?$/i,Es=/^multipart\/form-data(;.*)?$/i,vs=/^application\/x-www-form-urlencoded(;.*)?$/i,Pt=({mediaType:e})=>(As.lastIndex=0,As.test(e)),pr=({mediaType:e})=>{if(Is.lastIndex=0,Is.test(e))return "json";if(Es.lastIndex=0,Es.test(e))return "form-data";if(vs.lastIndex=0,vs.test(e))return "url-search-params"};var ks=({content:e})=>{let{mediaType:t,schema:r}=e;return r&&"$ref"in r?{allOf:[{...r}]}:r?r.type==="string"&&!r.format&&Pt({mediaType:t})?{...r,format:"binary"}:r:Pt({mediaType:t})?{format:"binary",type:"string"}:void 0},et=({content:e})=>{for(let t in e)return {mediaType:t,schema:e[t].schema,type:pr({mediaType:t})}};var w=/^(cursor|offset|page|start)$/;var $=({items:e,logicalOperator:t="or",mutateSchemaOneItem:r=!1,schema:o})=>e.length?o.type==="tuple"?(o.items=e,o):e.length!==1?(o.items=e,o.logicalOperator=t,o):r?(o={...o,...e[0]},o):(o.items=e,o):o;var Ve=(e,t)=>{for(let r in t)if(t[r]===e)return r;return Ct(e)};var tt=({schema:e})=>{if(e.type)return e.type;if(e.properties)return "object"},cr=({irSchema:e,schema:t})=>{t.default!==void 0&&(e.default=t.default),t.exclusiveMaximum?t.maximum!==void 0&&(e.exclusiveMaximum=t.maximum):t.maximum!==void 0&&(e.maximum=t.maximum),t.exclusiveMinimum?t.minimum!==void 0&&(e.exclusiveMinimum=t.minimum):t.minimum!==void 0&&(e.minimum=t.minimum),t.format&&(e.format=t.format),t.maxItems!==void 0&&(e.maxItems=t.maxItems),t.maxLength!==void 0&&(e.maxLength=t.maxLength),t.minItems!==void 0&&(e.minItems=t.minItems),t.minLength!==void 0&&(e.minLength=t.minLength),t.readOnly?e.accessScope="read":t.writeOnly&&(e.accessScope="write"),t.title&&(e.title=t.title);},Nc=({context:e,irSchema:t={},schema:r})=>{r.maxItems&&r.maxItems===r.minItems?t.type="tuple":t.type="array";let o=[];if(r.items){let n=pe({context:e,schema:r.items});if(!o.length&&r.maxItems&&r.maxItems===r.minItems)o=Array(r.maxItems).fill(n);else if("$ref"in r.items)o.push(n);else {let s=r.items.allOf||r.items.anyOf||r.items.oneOf;s&&s.length>1&&!r.items.nullable?t={...t,...n}:o.push(n);}}return t=$({items:o,schema:t}),t},wc=({irSchema:e={}})=>(e.type="boolean",e),$c=({irSchema:e={}})=>(e.type="number",e),Dc=({context:e,irSchema:t={},schema:r})=>{t.type="object";let o={};for(let n in r.properties){let s=r.properties[n];typeof s=="boolean"||(o[n]=pe({context:e,schema:s}));}if(Object.keys(o).length&&(t.properties=o),r.additionalProperties!==void 0)if(typeof r.additionalProperties=="boolean")t.additionalProperties={type:r.additionalProperties?"unknown":"never"};else {let n=pe({context:e,schema:r.additionalProperties});(t.properties||n.type!=="unknown")&&(t.additionalProperties=n);}return r.required&&(t.required=r.required),t},Mc=({irSchema:e={}})=>(e.type="string",e),Fc=({irSchema:e,schema:t})=>{t.deprecated!==void 0&&(e.deprecated=t.deprecated),t.description&&(e.description=t.description);},Ke=({schema:e})=>{let t={};return Fc({irSchema:t,schema:e}),t},Lc=({$ref:e,context:t,schema:r})=>{let o=Ke({schema:r}),n=[],s=tt({schema:r}),i=r.allOf;for(let a of i)if(n.push(pe({context:t,schema:a})),"$ref"in a){let c=t.resolveRef(a.$ref);if(c.discriminator&&e){let m={properties:{[c.discriminator.propertyName]:{const:Ve(e,c.discriminator.mapping),type:"string"}},type:"object"};n.push(m);}}if(s==="object"){let a=rt({context:t,schema:{...r,type:"object"}});if(a.properties){for(let c of a.required??[])if(!a.properties[c])for(let m of i){let l="$ref"in m?t.resolveRef(m.$ref):m;if(tt({schema:l})==="object"){let u=rt({context:t,schema:{...l,type:"object"}});if(u.properties?.[c]){a.properties[c]=u.properties[c];break}}}n.push(a);}}if(o=$({items:n,logicalOperator:"and",mutateSchemaOneItem:!0,schema:o}),r.nullable){let a=[{type:"null"}];n.length&&a.unshift(o),o={items:a,logicalOperator:"or"},a[0].deprecated&&(o.deprecated=a[0].deprecated),a[0].description&&(o.description=a[0].description);}return o},Hc=({context:e,schema:t})=>{let r=Ke({schema:t}),o=[],n=tt({schema:t}),s=t.anyOf;for(let i of s){let a=pe({context:e,schema:i});t.discriminator&&"$ref"in i&&(a={items:[{properties:{[t.discriminator.propertyName]:{const:Ve(i.$ref,t.discriminator.mapping),type:"string"}},type:"object"},a],logicalOperator:"and"}),o.push(a);}if(t.nullable&&o.push({type:"null"}),r=$({items:o,mutateSchemaOneItem:!0,schema:r}),n==="object"){let i=rt({context:e,schema:{...t,type:"object"}});i.properties&&(r={items:[r,i],logicalOperator:"and"});}return r},_c=({context:e,schema:t})=>{let r=Ke({schema:t});r.type="enum";let o=[];for(let[n,s]of t.enum.entries()){let i=typeof s,a;if(i==="string"||i==="number"||i==="boolean"?a=i:s===null?t.nullable&&(a="null"):console.warn("\u{1F6A8}",`unhandled "${i}" typeof value "${s}" for enum`,t.enum),!a)continue;let c=rt({context:e,schema:{description:t["x-enum-descriptions"]?.[n],title:t["x-enum-varnames"]?.[n]??t["x-enumNames"]?.[n],type:a==="null"?"string":a}});c.const=s,a==="null"&&(c.type=a),o.push(c);}return r=$({items:o,schema:r}),r},Bc=({context:e,schema:t})=>{let r=Ke({schema:t}),o=[],n=tt({schema:t}),s=t.oneOf;for(let i of s){let a=pe({context:e,schema:i});t.discriminator&&"$ref"in i&&(a={items:[{properties:{[t.discriminator.propertyName]:{const:Ve(i.$ref,t.discriminator.mapping),type:"string"}},type:"object"},a],logicalOperator:"and"}),a.logicalOperator==="or"&&a.items?o=o.concat(a.items):o.push(a);}if(t.nullable&&o.push({type:"null"}),r=$({items:o,mutateSchemaOneItem:!0,schema:r}),n==="object"){let i=rt({context:e,schema:{...t,type:"object"}});i.properties&&(r={items:[r,i],logicalOperator:"and"});}return r},Vc=({schema:e})=>{let t={};return t.$ref=decodeURI(e.$ref),t},Kc=({context:e,irSchema:t,schema:r})=>{t||(t=Ke({schema:r}),cr({irSchema:t,schema:r}));let o=[rt({context:e,irSchema:{},schema:r}),{type:"null"}];return t=$({items:o,schema:t}),t},Wc=({context:e,schema:t})=>{let r=Ke({schema:t});cr({irSchema:r,schema:t});let o=tt({schema:t});return o?t.nullable?Kc({context:e,irSchema:r,schema:{...t,type:o}}):rt({context:e,irSchema:r,schema:{...t,type:o}}):r},rt=({context:e,irSchema:t,schema:r})=>{switch(t||(t=Ke({schema:r}),cr({irSchema:t,schema:r})),r.type){case"array":return Nc({context:e,irSchema:t,schema:r});case"boolean":return wc({context:e,irSchema:t,schema:r});case"integer":case"number":return $c({context:e,irSchema:t,schema:r});case"object":return Dc({context:e,irSchema:t,schema:r});case"string":return Mc({context:e,irSchema:t,schema:r});default:return qs({context:e,irSchema:t,schema:r})}},qs=({irSchema:e,schema:t})=>(e||(e=Ke({schema:t})),e.type="unknown",cr({irSchema:e,schema:t}),e),pe=({$ref:e,context:t,schema:r})=>"$ref"in r?Vc({$ref:e,context:t,schema:r}):r.enum?_c({$ref:e,context:t,schema:r}):r.allOf?Lc({$ref:e,context:t,schema:r}):r.anyOf?Hc({$ref:e,context:t,schema:r}):r.oneOf?Bc({$ref:e,context:t,schema:r}):r.type||r.properties?Wc({$ref:e,context:t,schema:r}):qs({$ref:e,context:t,schema:r}),js=({$ref:e,context:t,schema:r})=>{t.ir.components||(t.ir.components={}),t.ir.components.schemas||(t.ir.components.schemas={}),t.ir.components.schemas[Ct(e)]=pe({$ref:e,context:t,schema:r});};var nt=({context:e,name:t,schema:r})=>{if(w.lastIndex=0,w.test(t))return !0;if("$ref"in r){let o=e.resolveRef(r.$ref);if("content"in o||"in"in o){let n;if("in"in o&&(n=o.schema),!n){let s=et({content:o.content});s?.schema&&(n=s.schema);}return n?nt({context:e,name:t,schema:n}):!1}return nt({context:e,name:t,schema:o})}for(let o in r.properties)if(w.lastIndex=0,w.test(o)){let n=r.properties[o];if(typeof n!="boolean"&&!("$ref"in n)){let s=tt({schema:n});if(s==="boolean"||s==="integer"||s==="number"||s==="string")return o}}for(let o of r.allOf??[]){let n=nt({context:e,name:t,schema:o});if(n)return n}return !1};var Uc=({irOperation:e,operation:t})=>{t.deprecated!==void 0&&(e.deprecated=t.deprecated),t.description&&(e.description=t.description),t.summary&&(e.summary=t.summary),t.tags&&t.tags.length&&(e.tags=t.tags);},Qc=({operation:e})=>{let t={id:e.id};return Uc({irOperation:t,operation:e}),t},Xc=({context:e,operation:t})=>{let r=Qc({operation:t});if(t.parameters&&(r.parameters=t.parameters),t.requestBody){let o="$ref"in t.requestBody?e.resolveRef(t.requestBody.$ref):t.requestBody,n=et({content:o.content});if(n){let s=n.schema&&"$ref"in n.schema?{allOf:[{...n.schema}],description:o.description}:{description:o.description,...n.schema},i=nt({context:e,name:"",schema:s});r.body={mediaType:n.mediaType,schema:pe({context:e,schema:s})},i&&(r.body.pagination=i),o.required&&(r.body.required=o.required),n.type&&(r.body.type=n.type);}}for(let o in t.responses){r.responses||(r.responses={});let n=t.responses[o],s="$ref"in n?e.resolveRef(n.$ref):n,i=et({content:s.content});i?r.responses[o]={mediaType:i.mediaType,schema:pe({context:e,schema:{description:s.description,...ks({content:i})}})}:r.responses[o]={schema:{description:s.description,type:o==="204"?"void":"unknown"}};}return r},ke=({context:e,method:t,operation:r,operationIds:o,path:n})=>{if(r.operationId){let s=`${t.toUpperCase()} ${n}`;o.has(r.operationId)?console.warn(`\u2757\uFE0F Duplicate operationId: ${r.operationId} in ${s}. Please ensure your operation IDs are unique. This behavior is not supported and will likely lead to unexpected results.`):o.set(r.operationId,s);}e.ir.paths||(e.ir.paths={}),e.ir.paths[n]||(e.ir.paths[n]={}),r.id=ar({context:e,id:r.operationId,method:t,path:n}),e.ir.paths[n][t]=Xc({context:e,operation:r});};var zc=e=>{switch(e){case"query":return !1;default:return}},Gc=e=>{switch(e){case"deepObject":case"form":return !0;default:return !1}},Jc=e=>{switch(e){case"header":case"path":return "simple";case"cookie":case"query":return "form"}},Oe=({context:e,parameters:t})=>{if(!t||!Object.keys(t).length)return;let r={};for(let o of t){let n="$ref"in o?e.resolveRef(o.$ref):o;r[n.in]||(r[n.in]={}),r[n.in][n.name]=Ns({context:e,parameter:n});}return r},qe=({source:e,target:t})=>{let r={...t};if(e&&(e.cookie&&(r.cookie?r.cookie={...r.cookie,...e.cookie}:r.cookie=e.cookie),e.header&&(r.header?r.header={...r.header,...e.header}:r.header=e.header),e.path&&(r.path?r.path={...r.path,...e.path}:r.path=e.path),e.query&&(r.query?r.query={...r.query,...e.query}:r.query=e.query)),!!Object.keys(r).length)return r},Ns=({context:e,parameter:t})=>{let r=t.schema;if(!r){let m=et({content:t.content});m&&(r=m.schema);}let o=r&&"$ref"in r?{allOf:[{...r}],deprecated:t.deprecated,description:t.description}:{deprecated:t.deprecated,description:t.description,...r},n=nt({context:e,name:t.name,schema:o}),s=t.style||Jc(t.in),i=t.explode!==void 0?t.explode:Gc(s),c={allowReserved:t.allowReserved!==void 0?t.allowReserved:zc(t.in),explode:i,location:t.in,name:t.name,schema:pe({context:e,schema:o}),style:s};return n&&(c.pagination=n),t.required&&(c.required=t.required),c},ws=({context:e,name:t,parameter:r})=>{e.ir.components||(e.ir.components={}),e.ir.components.parameters||(e.ir.components.parameters={}),e.ir.components.parameters[t]=Ns({context:e,parameter:r});};var ln=e=>{let t=new Map,r=e.config.input.exclude?new RegExp(e.config.input.exclude):void 0,o=e.config.input.include?new RegExp(e.config.input.include):void 0;for(let n in e.spec.paths){let s=e.spec.paths[n],i=s.$ref?{...e.resolveRef(s.$ref),...s}:s,a={context:e,operation:{description:i.description,id:"",parameters:Oe({context:e,parameters:i.parameters}),servers:i.servers,summary:i.summary},operationIds:t,path:n},c=`#/paths${n}/delete`;i.delete&&E({$ref:c,excludeRegExp:r,includeRegExp:o})&&ke({...a,method:"delete",operation:{...a.operation,...i.delete,parameters:qe({source:Oe({context:e,parameters:i.delete.parameters}),target:a.operation.parameters})}});let m=`#/paths${n}/get`;i.get&&E({$ref:m,excludeRegExp:r,includeRegExp:o})&&ke({...a,method:"get",operation:{...a.operation,...i.get,parameters:qe({source:Oe({context:e,parameters:i.get.parameters}),target:a.operation.parameters})}});let l=`#/paths${n}/head`;i.head&&E({$ref:l,excludeRegExp:r,includeRegExp:o})&&ke({...a,method:"head",operation:{...a.operation,...i.head,parameters:qe({source:Oe({context:e,parameters:i.head.parameters}),target:a.operation.parameters})}});let u=`#/paths${n}/options`;i.options&&E({$ref:u,excludeRegExp:r,includeRegExp:o})&&ke({...a,method:"options",operation:{...a.operation,...i.options,parameters:qe({source:Oe({context:e,parameters:i.options.parameters}),target:a.operation.parameters})}});let f=`#/paths${n}/patch`;i.patch&&E({$ref:f,excludeRegExp:r,includeRegExp:o})&&ke({...a,method:"patch",operation:{...a.operation,...i.patch,parameters:qe({source:Oe({context:e,parameters:i.patch.parameters}),target:a.operation.parameters})}});let g=`#/paths${n}/post`;i.post&&E({$ref:g,excludeRegExp:r,includeRegExp:o})&&ke({...a,method:"post",operation:{...a.operation,...i.post,parameters:qe({source:Oe({context:e,parameters:i.post.parameters}),target:a.operation.parameters})}});let x=`#/paths${n}/put`;i.put&&E({$ref:x,excludeRegExp:r,includeRegExp:o})&&ke({...a,method:"put",operation:{...a.operation,...i.put,parameters:qe({source:Oe({context:e,parameters:i.put.parameters}),target:a.operation.parameters})}});let h=`#/paths${n}/trace`;i.trace&&E({$ref:h,excludeRegExp:r,includeRegExp:o})&&ke({...a,method:"trace",operation:{...a.operation,...i.trace,parameters:qe({source:Oe({context:e,parameters:i.trace.parameters}),target:a.operation.parameters})}});}if(e.spec.components){for(let n in e.spec.components.parameters){let s=`#/components/parameters/${n}`;if(!E({$ref:s,excludeRegExp:r,includeRegExp:o}))continue;let i=e.spec.components.parameters[n],a="$ref"in i?e.resolveRef(i.$ref):i;ws({context:e,name:n,parameter:a});}for(let n in e.spec.components.schemas){let s=`#/components/schemas/${n}`;if(!E({$ref:s,excludeRegExp:r,includeRegExp:o}))continue;let i=e.spec.components.schemas[n];js({$ref:s,context:e,schema:i});}}};var $s=({content:e})=>{let{mediaType:t,schema:r}=e;return r?r.type==="string"&&!r.format&&Pt({mediaType:t})?{...r,format:"binary"}:r:Pt({mediaType:t})?{format:"binary",type:"string"}:void 0},ot=({content:e})=>{for(let t in e)return {mediaType:t,schema:e[t].schema,type:pr({mediaType:t})}};var je=({schema:e})=>typeof e.type=="string"?[e.type]:e.type?e.type:e.properties?["object"]:[],mr=({irSchema:e,schema:t})=>{if(t.const!==void 0&&(e.const=t.const,!t.type))if(t.const===null)e.type="null";else switch(typeof t.const){case"bigint":case"number":e.type="number";break;case"boolean":e.type="boolean";break;case"string":e.type="string";break}t.default!==void 0&&(e.default=t.default),t.exclusiveMaximum&&(e.exclusiveMaximum=t.exclusiveMaximum),t.exclusiveMinimum&&(e.exclusiveMinimum=t.exclusiveMinimum),t.format&&(e.format=t.format),t.maximum!==void 0&&(e.maximum=t.maximum),t.maxItems!==void 0&&(e.maxItems=t.maxItems),t.maxLength!==void 0&&(e.maxLength=t.maxLength),t.minimum!==void 0&&(e.minimum=t.minimum),t.minItems!==void 0&&(e.minItems=t.minItems),t.minLength!==void 0&&(e.minLength=t.minLength),t.readOnly?e.accessScope="read":t.writeOnly&&(e.accessScope="write"),t.title&&(e.title=t.title);},Zc=({context:e,irSchema:t={},schema:r})=>{r.prefixItems&&r.prefixItems.length||r.maxItems&&r.maxItems===r.minItems?t.type="tuple":t.type="array";let o=[];for(let n of r.prefixItems??[])o.push(re({context:e,schema:n}));if(r.items){let n=re({context:e,schema:r.items});if(!o.length&&r.maxItems&&r.maxItems===r.minItems)o=Array(r.maxItems).fill(n);else {let s=r.items.allOf||r.items.anyOf||r.items.oneOf;s&&s.length>1&&!je({schema:r.items}).includes("null")?t={...t,...n}:o.push(n);}}return t=$({items:o,schema:t}),t},Yc=({irSchema:e={}})=>(e.type="boolean",e),em=({irSchema:e={}})=>(e.type="null",e),tm=({irSchema:e={}})=>(e.type="number",e),rm=({context:e,irSchema:t={},schema:r})=>{t.type="object";let o={};for(let n in r.properties){let s=r.properties[n];typeof s=="boolean"||(o[n]=re({context:e,schema:s}));}if(Object.keys(o).length&&(t.properties=o),r.additionalProperties!==void 0)if(typeof r.additionalProperties=="boolean")t.additionalProperties={type:r.additionalProperties?"unknown":"never"};else {let n=re({context:e,schema:r.additionalProperties});(t.properties||n.type!=="unknown")&&(t.additionalProperties=n);}return r.required&&(t.required=r.required),t},nm=({irSchema:e={}})=>(e.type="string",e),om=({irSchema:e,schema:t})=>{t.deprecated!==void 0&&(e.deprecated=t.deprecated),t.description&&(e.description=t.description);},Ne=({schema:e})=>{let t={};return om({irSchema:t,schema:e}),t},sm=({$ref:e,context:t,schema:r})=>{let o=Ne({schema:r}),n=[],s=je({schema:r}),i=r.allOf;for(let a of i)if(n.push(re({context:t,schema:a})),a.$ref){let c=t.resolveRef(a.$ref);if(c.discriminator&&e){let m={properties:{[c.discriminator.propertyName]:{const:Ve(e,c.discriminator.mapping),type:"string"}},type:"object"};n.push(m);}}if(s.includes("object")){let a=st({context:t,schema:{...r,type:"object"}});if(a.properties){for(let c of a.required??[])if(!a.properties[c])for(let m of i){let l=m.$ref?t.resolveRef(m.$ref):m;if(je({schema:l}).includes("object")){let u=st({context:t,schema:{...l,type:"object"}});if(u.properties?.[c]){a.properties[c]=u.properties[c];break}}}n.push(a);}}if(o=$({items:n,logicalOperator:"and",mutateSchemaOneItem:!0,schema:o}),s.includes("null")){let a=[{type:"null"}];n.length&&a.unshift(o),o={items:a,logicalOperator:"or"};}return o},im=({context:e,schema:t})=>{let r=Ne({schema:t}),o=[],n=je({schema:t}),s=t.anyOf;for(let i of s){let a=re({context:e,schema:i});t.discriminator&&i.$ref&&(a={items:[{properties:{[t.discriminator.propertyName]:{const:Ve(i.$ref,t.discriminator.mapping),type:"string"}},type:"object"},a],logicalOperator:"and"}),o.push(a);}if(n.includes("null")&&o.push({type:"null"}),r=$({items:o,mutateSchemaOneItem:!0,schema:r}),n.includes("object")){let i=st({context:e,schema:{...t,type:"object"}});i.properties&&(r={items:[r,i],logicalOperator:"and"});}return r},am=({context:e,schema:t})=>{let r=Ne({schema:t});r.type="enum";let o=[],n=je({schema:t});for(let[s,i]of t.enum.entries()){let a=typeof i,c;a==="string"||a==="number"||a==="boolean"?c=a:i===null?n.includes("null")&&(c="null"):console.warn("\u{1F6A8}",`unhandled "${a}" typeof value "${i}" for enum`,t.enum),c&&o.push(st({context:e,schema:{const:i,description:t["x-enum-descriptions"]?.[s],title:t["x-enum-varnames"]?.[s]??t["x-enumNames"]?.[s],type:c}}));}return r=$({items:o,schema:r}),r},pm=({context:e,schema:t})=>{let r=Ne({schema:t}),o=[],n=je({schema:t}),s=t.oneOf;for(let i of s){let a=re({context:e,schema:i});t.discriminator&&i.$ref&&(a={items:[{properties:{[t.discriminator.propertyName]:{const:Ve(i.$ref,t.discriminator.mapping),type:"string"}},type:"object"},a],logicalOperator:"and"}),a.logicalOperator==="or"&&a.items?o=o.concat(a.items):o.push(a);}if(n.includes("null")&&o.push({type:"null"}),r=$({items:o,mutateSchemaOneItem:!0,schema:r}),n.includes("object")){let i=st({context:e,schema:{...t,type:"object"}});i.properties&&(r={items:[r,i],logicalOperator:"and"});}return r},cm=({schema:e})=>{let t=Ne({schema:e});return t.$ref=decodeURI(e.$ref),t},st=({context:e,irSchema:t,schema:r})=>{switch(t||(t=Ne({schema:r}),mr({irSchema:t,schema:r})),r.type){case"array":return Zc({context:e,irSchema:t,schema:r});case"boolean":return Yc({context:e,irSchema:t,schema:r});case"integer":case"number":return tm({context:e,irSchema:t,schema:r});case"null":return em({context:e,irSchema:t,schema:r});case"object":return rm({context:e,irSchema:t,schema:r});case"string":return nm({context:e,irSchema:t,schema:r});default:return Ds({context:e,irSchema:t,schema:r})}},mm=({context:e,irSchema:t,schema:r})=>{t||(t=Ne({schema:r}),mr({irSchema:t,schema:r}));let o=[];for(let n of r.type)o.push(st({context:e,irSchema:{},schema:{...r,type:n}}));return t=$({items:o,schema:t}),t},lm=({context:e,schema:t})=>{let r=Ne({schema:t});mr({irSchema:r,schema:t});let o=je({schema:t});return o.length===1?st({context:e,irSchema:r,schema:{...t,type:o[0]}}):mm({context:e,irSchema:r,schema:{...t,type:o}})},Ds=({irSchema:e,schema:t})=>(e||(e=Ne({schema:t})),e.type="unknown",mr({irSchema:e,schema:t}),e),re=({$ref:e,context:t,schema:r})=>r.$ref?cm({$ref:e,context:t,schema:r}):r.enum?am({$ref:e,context:t,schema:r}):r.allOf?sm({$ref:e,context:t,schema:r}):r.anyOf?im({$ref:e,context:t,schema:r}):r.oneOf?pm({$ref:e,context:t,schema:r}):r.type||r.properties?lm({$ref:e,context:t,schema:r}):Ds({$ref:e,context:t,schema:r}),Ms=({$ref:e,context:t,schema:r})=>{t.ir.components||(t.ir.components={}),t.ir.components.schemas||(t.ir.components.schemas={}),t.ir.components.schemas[Ct(e)]=re({$ref:e,context:t,schema:r});};var it=({context:e,name:t,schema:r})=>{if(w.lastIndex=0,w.test(t))return !0;if(r.$ref){let o=e.resolveRef(r.$ref);if("content"in o||"in"in o){let n;if("in"in o&&(n=o.schema),!n){let s=ot({content:o.content});s?.schema&&(n=s.schema);}return n?it({context:e,name:t,schema:n}):!1}return it({context:e,name:t,schema:o})}for(let o in r.properties)if(w.lastIndex=0,w.test(o)){let n=r.properties[o];if(typeof n!="boolean"){let s=je({schema:n});if(s.includes("boolean")||s.includes("integer")||s.includes("number")||s.includes("string"))return o}}for(let o of r.allOf??[]){let n=it({context:e,name:t,schema:o});if(n)return n}return !1};var um=({irOperation:e,operation:t})=>{t.deprecated!==void 0&&(e.deprecated=t.deprecated),t.description&&(e.description=t.description),t.summary&&(e.summary=t.summary),t.tags&&t.tags.length&&(e.tags=t.tags);},fm=({operation:e})=>{let t={id:e.id};return um({irOperation:t,operation:e}),t},dm=({context:e,operation:t})=>{let r=fm({operation:t});if(t.parameters&&(r.parameters=t.parameters),t.requestBody){let o="$ref"in t.requestBody?e.resolveRef(t.requestBody.$ref):t.requestBody,n=ot({content:o.content});if(n){let s={description:o.description,...n.schema},i=it({context:e,name:"",schema:s});r.body={mediaType:n.mediaType,schema:re({context:e,schema:s})},i&&(r.body.pagination=i),o.required&&(r.body.required=o.required),n.type&&(r.body.type=n.type);}}for(let o in t.responses){r.responses||(r.responses={});let n=t.responses[o],s="$ref"in n?e.resolveRef(n.$ref):n,i=ot({content:s.content});i?r.responses[o]={mediaType:i.mediaType,schema:re({context:e,schema:{description:s.description,...$s({content:i})}})}:r.responses[o]={schema:{description:s.description,type:o==="204"?"void":"unknown"}};}return r},we=({context:e,method:t,operation:r,operationIds:o,path:n})=>{if(r.operationId){let s=`${t.toUpperCase()} ${n}`;o.has(r.operationId)?console.warn(`\u2757\uFE0F Duplicate operationId: ${r.operationId} in ${s}. Please ensure your operation IDs are unique. This behavior is not supported and will likely lead to unexpected results.`):o.set(r.operationId,s);}e.ir.paths||(e.ir.paths={}),e.ir.paths[n]||(e.ir.paths[n]={}),r.id=ar({context:e,id:r.operationId,method:t,path:n}),e.ir.paths[n][t]=dm({context:e,operation:r});};var ym=e=>{switch(e){case"query":return !1;default:return}},gm=e=>{switch(e){case"deepObject":case"form":return !0;default:return !1}},hm=e=>{switch(e){case"header":case"path":return "simple";case"cookie":case"query":return "form"}},Re=({context:e,parameters:t})=>{if(!t||!Object.keys(t).length)return;let r={};for(let o of t){let n="$ref"in o?e.resolveRef(o.$ref):o;r[n.in]||(r[n.in]={}),r[n.in][n.name]=Fs({context:e,parameter:n});}return r},$e=({source:e,target:t})=>{let r={...t};if(e&&(e.cookie&&(r.cookie?r.cookie={...r.cookie,...e.cookie}:r.cookie=e.cookie),e.header&&(r.header?r.header={...r.header,...e.header}:r.header=e.header),e.path&&(r.path?r.path={...r.path,...e.path}:r.path=e.path),e.query&&(r.query?r.query={...r.query,...e.query}:r.query=e.query)),!!Object.keys(r).length)return r},Fs=({context:e,parameter:t})=>{let r=t.schema;if(!r){let m=ot({content:t.content});m&&(r=m.schema);}let o={deprecated:t.deprecated,description:t.description,...r},n=it({context:e,name:t.name,schema:o}),s=t.style||hm(t.in),i=t.explode!==void 0?t.explode:gm(s),c={allowReserved:t.allowReserved!==void 0?t.allowReserved:ym(t.in),explode:i,location:t.in,name:t.name,schema:re({context:e,schema:o}),style:s};return n&&(c.pagination=n),t.required&&(c.required=t.required),c},Ls=({context:e,name:t,parameter:r})=>{e.ir.components||(e.ir.components={}),e.ir.components.parameters||(e.ir.components.parameters={}),e.ir.components.parameters[t]=Fs({context:e,parameter:r});};var un=e=>{let t=new Map,r=e.config.input.exclude?new RegExp(e.config.input.exclude):void 0,o=e.config.input.include?new RegExp(e.config.input.include):void 0;for(let n in e.spec.paths){let s=e.spec.paths[n],i=s.$ref?{...e.resolveRef(s.$ref),...s}:s,a={context:e,operation:{description:i.description,id:"",parameters:Re({context:e,parameters:i.parameters}),servers:i.servers,summary:i.summary},operationIds:t,path:n},c=`#/paths${n}/delete`;i.delete&&E({$ref:c,excludeRegExp:r,includeRegExp:o})&&we({...a,method:"delete",operation:{...a.operation,...i.delete,parameters:$e({source:Re({context:e,parameters:i.delete.parameters}),target:a.operation.parameters})}});let m=`#/paths${n}/get`;i.get&&E({$ref:m,excludeRegExp:r,includeRegExp:o})&&we({...a,method:"get",operation:{...a.operation,...i.get,parameters:$e({source:Re({context:e,parameters:i.get.parameters}),target:a.operation.parameters})}});let l=`#/paths${n}/head`;i.head&&E({$ref:l,excludeRegExp:r,includeRegExp:o})&&we({...a,method:"head",operation:{...a.operation,...i.head,parameters:$e({source:Re({context:e,parameters:i.head.parameters}),target:a.operation.parameters})}});let u=`#/paths${n}/options`;i.options&&E({$ref:u,excludeRegExp:r,includeRegExp:o})&&we({...a,method:"options",operation:{...a.operation,...i.options,parameters:$e({source:Re({context:e,parameters:i.options.parameters}),target:a.operation.parameters})}});let f=`#/paths${n}/patch`;i.patch&&E({$ref:f,excludeRegExp:r,includeRegExp:o})&&we({...a,method:"patch",operation:{...a.operation,...i.patch,parameters:$e({source:Re({context:e,parameters:i.patch.parameters}),target:a.operation.parameters})}});let g=`#/paths${n}/post`;i.post&&E({$ref:g,excludeRegExp:r,includeRegExp:o})&&we({...a,method:"post",operation:{...a.operation,...i.post,parameters:$e({source:Re({context:e,parameters:i.post.parameters}),target:a.operation.parameters})}});let x=`#/paths${n}/put`;i.put&&E({$ref:x,excludeRegExp:r,includeRegExp:o})&&we({...a,method:"put",operation:{...a.operation,...i.put,parameters:$e({source:Re({context:e,parameters:i.put.parameters}),target:a.operation.parameters})}});let h=`#/paths${n}/trace`;i.trace&&E({$ref:h,excludeRegExp:r,includeRegExp:o})&&we({...a,method:"trace",operation:{...a.operation,...i.trace,parameters:$e({source:Re({context:e,parameters:i.trace.parameters}),target:a.operation.parameters})}});}if(e.spec.components){for(let n in e.spec.components.parameters){let s=`#/components/parameters/${n}`;if(!E({$ref:s,excludeRegExp:r,includeRegExp:o}))continue;let i=e.spec.components.parameters[n],a="$ref"in i?e.resolveRef(i.$ref):i;Ls({context:e,name:n,parameter:a});}for(let n in e.spec.components.schemas){let s=`#/components/schemas/${n}`;if(!E({$ref:s,excludeRegExp:r,includeRegExp:o}))continue;let i=e.spec.components.schemas[n];Ms({$ref:s,context:e,schema:i});}}};var lr=["connect","delete","get","head","options","patch","post","put","trace"];function ur(e="1.0"){return String(e).replace(/^v/gi,"")}var St="#/components/parameters/",fr="#/components/schemas/";var Te=({config:e,name:t})=>e.plugins["@hey-api/sdk"]?.serviceNameBuilder?e.plugins["@hey-api/sdk"].serviceNameBuilder.replace("{{name}}",t):t,fn=e=>b().plugins["@hey-api/typescript"]?.style==="PascalCase"?I({input:e,pascalCase:!0}):e,Hs=e=>{let t=fn(e);return X(t).replace(oe,"_$1")};var _s=(e,t)=>{if(e.enum&&!t.includes("boolean"))return "enum"},dn=e=>At(e).includes("null"),We=e=>e.nullable===!0||dn(e),At=({type:e})=>Array.isArray(e)?e:e?[e]:[];var _t=e=>e.trim().replace(/^#\/definitions\//,"").replace(/^#\/parameters\//,"").replace(/^#\/responses\//,"").replace(/^#\/securityDefinitions\//,"").replace(/^#\/components\/schemas\//,"").replace(/^#\/components\/responses\//,"").replace(/^#\/components\/parameters\//,"").replace(/^#\/components\/examples\//,"").replace(/^#\/components\/requestBodies\//,"").replace(/^#\/components\/headers\//,"").replace(/^#\/components\/securitySchemes\//,"").replace(/^#\/components\/links\//,"").replace(/^#\/components\/callbacks\//,"");var Bs=(e,t)=>{if(t==="binary")return "binary";switch(e){case"any":case"object":case"unknown":return "unknown";case"array":return "unknown[]";case"boolean":return "boolean";case"byte":case"double":case"float":case"int":case"integer":case"long":case"number":case"short":return "number";case"char":case"date":case"date-time":case"password":case"string":return "string";case"file":return "binary";case"null":return "null";case"void":return "void"}},Vs=/(?<!`1)\[.*\]$/g,T=({debug:e,format:t,type:r="unknown"})=>{let o={$refs:[],base:"unknown",imports:[],isNullable:!1,template:null,type:"unknown"};if(Array.isArray(r)){let i=r.filter(a=>a!=="null").map(a=>Bs(a,t)).filter(Boolean).join(" | ");return o.type=i,o.base=i,o.isNullable=dn({type:r}),o}let n=Bs(r,t);if(n)return o.type=n,o.base=n,o;let s=decodeURIComponent(_t(r));if(Vs.lastIndex=0,Vs.test(s)){let i=s.match(/(.*?)\[(.*)\]$/);if(i?.length){let a=T({debug:e,type:X(i[1])}),c=T({debug:e,type:X(i[2])});return a.type==="unknown[]"?(o.type=`${c.type}[]`,o.base=`${c.type}`,a.$refs=[],a.imports=[]):c.type?(o.type=`${a.type}<${c.type}>`,o.base=a.type,o.template=c.type):(o.type=a.type,o.base=a.type,o.template=a.type),o.$refs=[...o.$refs,...a.$refs,...c.$refs],o.imports=[...o.imports,...a.imports,...c.imports],o}}if(s){let i=fn(X(s));return r.startsWith(St)&&(i=`Parameter${i}`),o.type=i,o.base=i,r.startsWith("#")&&(o.$refs=[...o.$refs,decodeURIComponent(r)]),o.imports=[...o.imports,i],o}return o};function z(e,t,r){return r.indexOf(e)===t}var It=(e,t)=>{if(!Array.isArray(t))return [];let r=(e["x-enum-descriptions"]??[]).filter(n=>typeof n=="string"),o=(e["x-enum-varnames"]??e["x-enumNames"]??[]).filter(n=>typeof n=="string");return t.filter(z).filter(n=>typeof n=="number"||typeof n=="string").map((n,s)=>({customDescription:r[s],customName:o[s],description:void 0,value:n}))};var N=e=>e?.replace(/\\/g,"\\\\").replace(/'/g,"\\'");var dr=({definition:e,getModel:t,openApi:r,types:o})=>{let n=[];return Object.entries(e.properties??{}).forEach(([s,i])=>{let a=!!e.required?.includes(s);if(i.$ref){let c=T({type:i.$ref});n.push({$refs:[],base:c.base,description:i.description||null,enum:[],enums:[],exclusiveMaximum:i.exclusiveMaximum,exclusiveMinimum:i.exclusiveMinimum,export:"reference",format:i.format,imports:c.imports,in:"",isDefinition:!1,isNullable:i["x-nullable"]===!0,isReadOnly:i.readOnly===!0,isRequired:a,link:null,maxItems:i.maxItems,maxLength:i.maxLength,maxProperties:i.maxProperties,maximum:i.maximum,minItems:i.minItems,minLength:i.minLength,minProperties:i.minProperties,minimum:i.minimum,multipleOf:i.multipleOf,name:se(s),pattern:N(i.pattern),properties:[],template:c.template,type:c.type,uniqueItems:i.uniqueItems});}else {let c=t({definition:i,openApi:r,types:o});n.push({$refs:[],base:c.base,description:i.description||null,enum:c.enum,enums:c.enums,exclusiveMaximum:i.exclusiveMaximum,exclusiveMinimum:i.exclusiveMinimum,export:c.export,format:i.format,imports:c.imports,in:"",isDefinition:!1,isNullable:i["x-nullable"]===!0,isReadOnly:i.readOnly===!0,isRequired:a,link:c.link,maxItems:i.maxItems,maxLength:i.maxLength,maxProperties:i.maxProperties,maximum:i.maximum,minItems:i.minItems,minLength:i.minLength,minProperties:i.minProperties,minimum:i.minimum,multipleOf:i.multipleOf,name:se(s),pattern:N(i.pattern),properties:c.properties,template:c.template,type:c.type,uniqueItems:i.uniqueItems});}}),n};var xm=/~1/g,bm=/~0/g;function v(e,t){if(t.$ref){let r=t.$ref.replace(/^#/g,"").split("/").filter(Boolean),o=e;return r.forEach(n=>{let s=decodeURIComponent(n.replace(xm,"/").replace(bm,"~"));if(o.hasOwnProperty(s))o=o[s];else throw new Error(`Could not find reference: "${t.$ref}"`)}),o}return t}var Ks=({definitions:e,getModel:t,openApi:r,required:o,types:n})=>e.reduce((s,i)=>{if(i.$ref){let a=v(r,i);return [...s,...t({definition:a,openApi:r,types:n}).properties]}return [...s,...t({definition:i,openApi:r,types:n}).properties]},[]).filter(s=>!s.isRequired&&o.includes(s.name)).map(s=>({...s,isRequired:!0}));var Ws=({definition:e,definitions:t,getModel:r,openApi:o,type:n,types:s})=>{let i={$refs:[],enums:[],export:n,imports:[],properties:[]},a=[];if(t.map(c=>r({definition:c,openApi:o,types:s})).filter(c=>{let m=c.properties.length,l=c.enums.length;return !(c.type==="unknown"&&!m&&!l)}).forEach(c=>{i.imports.push(...c.imports),i.enums.push(...c.enums),i.properties.push(c);}),e.required&&n==="all-of"){let c=Ks({definitions:t,getModel:r,openApi:o,required:e.required,types:s});c.forEach(m=>{i.imports.push(...m.imports),i.enums.push(...m.enums);}),a.push(...c);}if(e.properties){let c=dr({definition:e,getModel:r,openApi:o,types:s});c.forEach(m=>{i.imports.push(...m.imports),i.enums.push(...m.enums),m.export==="enum"&&i.enums.push(m);}),a.push(...c);}return a.length&&i.properties.push({$refs:[],base:"unknown",description:"",enum:[],enums:[],export:"interface",imports:[],in:"",isDefinition:!1,isNullable:!1,isReadOnly:!1,isRequired:!1,link:null,name:"properties",properties:a,template:null,type:"unknown"}),i};var Ce=({definition:e,isDefinition:t=!1,meta:r,openApi:o,types:n})=>{let s={$refs:[],base:"unknown",description:e.description||null,enum:[],enums:[],exclusiveMaximum:e.exclusiveMaximum,exclusiveMinimum:e.exclusiveMinimum,export:"interface",format:e.format,imports:[],in:"",isDefinition:t,isNullable:e["x-nullable"]===!0,isReadOnly:e.readOnly===!0,isRequired:!1,link:null,maxItems:e.maxItems,maxLength:e.maxLength,maxProperties:e.maxProperties,maximum:e.maximum,meta:r,minItems:e.minItems,minLength:e.minLength,minProperties:e.minProperties,minimum:e.minimum,multipleOf:e.multipleOf,name:r?.name??"",pattern:N(e.pattern),properties:[],template:null,type:"unknown",uniqueItems:e.uniqueItems};if(e.$ref){let i=T({type:e.$ref});return s.export="reference",s.type=i.type,s.base=i.base,s.template=i.template,s.imports.push(...i.imports),s}if(e.enum&&e.type!=="boolean"){let i=It(e,e.enum);if(i.length)return s.base="string",s.enum=[...s.enum,...i],s.export="enum",s.type="string",s}if(e.type==="array"&&e.items)if(e.items.$ref){let i=T({type:e.items.$ref});return s.export="array",s.type=i.type,s.base=i.base,s.template=i.template,s.imports.push(...i.imports),s}else {let i=Ce({definition:e.items,openApi:o,types:n});return s.export="array",s.type=i.type,s.base=i.base,s.template=i.template,s.link=i,s.imports.push(...i.imports),s}if(e.type==="object"&&typeof e.additionalProperties=="object")if(e.additionalProperties.$ref){let i=T({type:e.additionalProperties.$ref});return s.export="dictionary",s.type=i.type,s.base=i.base,s.template=i.template,s.imports.push(...i.imports),s}else {let i=Ce({definition:e.additionalProperties,openApi:o,types:n});return s.export="dictionary",s.type=i.type,s.base=i.base,s.template=i.template,s.link=i,s.imports.push(...i.imports),s}if(e.allOf?.length){let i=Ws({definition:e,definitions:e.allOf,getModel:Ce,openApi:o,type:"all-of",types:n});return s.export=i.export,s.imports.push(...i.imports),s.properties.push(...i.properties),s.enums=[...s.enums,...i.enums],s}if(e.type==="object")return s.export="interface",s.type="unknown",s.base="unknown",e.properties&&dr({definition:e,getModel:Ce,openApi:o,types:n}).forEach(a=>{s.imports.push(...a.imports),s.enums=[...s.enums,...a.enums],s.properties.push(a),a.export==="enum"&&(s.enums=[...s.enums,a]);}),s;if(e.type){let i=T({format:e.format,type:e.type});return s.export="generic",s.type=i.type,s.base=i.base,s.template=i.template,s.imports.push(...i.imports),s}return s};var Us=e=>{let t={},r=[];return Object.entries(e.definitions??{}).forEach(([o,n])=>{let i=T({type:o}).base.replace(oe,"_$1"),a={$ref:`#/definitions/${o}`,name:i};t[i]=a;let c=Ce({definition:n,isDefinition:!0,meta:a,openApi:e,types:t});r=[...r,c];}),{models:r,types:t}};var Om=(e,t)=>{if(t)return t;if(typeof e=="number")return `'_${e}'`;let r="";return typeof e=="string"&&(r=e.replace(/[^$\u200c\u200d\p{ID_Continue}]/gu,"_").replace(/^([^$_\p{ID_Start}])/u,"_$1").replace(/(\p{Lowercase})(\p{Uppercase}+)/gu,"$1_$2")),r=r.trim(),r||(r="empty_string"),r.toUpperCase()},Qs=e=>e.map(t=>yr(t.value,!0)).filter(z).join(" | "),yr=(e,t=!1)=>typeof e=="string"?e.includes("'")&&t?`"${e}"`:`'${e}'`:e,gr=e=>{let t=Om(e.value,e.customName),r=yr(e.value);return {key:t,value:r}},hr=e=>{let t=e.enum.map(r=>gr(r)).sort((r,o)=>xt(r.key,o.key)).map(r=>`${r.key}=${r.value}`).join("&");return {$ref:`enum/${e.name}/${t}`,name:X(lt(e.name))}};var Xs=e=>{let r=`Parameter${T({type:e}).base.replace(oe,"_$1")}`;return {$ref:St+e,name:r}},xr=e=>{let t=Hs(e);return {$ref:fr+e,name:t}};var Bt=e=>e.format==="date"||e.format==="date-time",yn=e=>{let t=b();return e.base==="binary"?p.typeUnionNode({types:["Blob","File"]}):t.plugins["@hey-api/transformers"]?.dates&&Bt(e)?p.typeNode("Date"):p.typeNode(e.base)},Rm=e=>{let t=e.base==="null"?!1:e.isNullable,r=yn(e);if(e.export==="reference"&&e.$refs.length===1&&e.$refs[0].startsWith(fr)){let n=xr(e.base);r=p.typeNode(n.name);}return p.typeUnionNode({isNullable:t,types:[r]})},Tm=e=>{if(e.link){if(Array.isArray(e.link)){let t=e.link.map(o=>ce(o));return p.typeTupleNode({isNullable:e.isNullable,types:t})}if(e.export==="array"&&e.maxItems&&e.minItems&&e.maxItems===e.minItems&&e.maxItems<=100){let t=Array(e.maxItems).fill(ce(e.link));return p.typeTupleNode({isNullable:e.isNullable,types:t})}return p.typeArrayNode([ce(e.link)],e.isNullable)}return p.typeArrayNode([yn(e)],e.isNullable)},Cm=e=>{let t=e.enum.map(r=>yr(r.value));return p.typeUnionNode({isNullable:e.isNullable,types:t})},Pm=e=>{let t=e.link&&!Array.isArray(e.link)?ce(e.link):yn(e);return p.typeRecordNode(["string"],[t],e.isNullable,!0)},zs=({model:e,style:t})=>{let r=e.properties.map(n=>p.nodeToString({node:ce(n),unescape:!0})).filter(z),o=t==="union"?p.typeUnionNode({isNullable:e.isNullable&&!e.properties.find(n=>n.isNullable),types:r}):p.typeIntersectionNode({isNullable:e.isNullable,types:r});return e.meta?o:p.typeParenthesizedNode({type:o})},Sm=e=>{if(!e.properties.length)return p.typeNode("unknown");let t=b(),r=P(t),o=e.properties.map(n=>{let s=n.isRequired?"":"?",i=ce(n),a=r?/^\dXX$/.test(n.name)?se(n.name):n.name:se(lt(gn(n.name)));return n.name==="[key: string]"&&(a=n.name,s&&(s="",i=p.typeUnionNode({types:[i,"undefined"]}))),{comment:[n.description&&A(n.description),n.deprecated&&"@deprecated"],isReadOnly:n.isReadOnly,isRequired:s==="",name:a,type:i}});return p.typeInterfaceNode({isNullable:e.isNullable,properties:o,useLegacyResolution:!0})},ce=e=>{switch(e.export){case"all-of":return zs({model:e,style:"intersection"});case"any-of":case"one-of":return zs({model:e,style:"union"});case"array":return Tm(e);case"dictionary":return Pm(e);case"enum":return Cm(e);case"interface":return Sm(e);case"const":case"generic":case"reference":default:return Rm(e)}},me=({client:e,count:t=1,create:r=!1,meta:o,nameTransformer:n})=>{let s={created:!1,name:""},i=o.name;n&&(i=n(i)),t>1&&(i=`${i}${t}`);let a=e.types[i];return a?a.$ref===o.$ref?s={created:!1,name:i}:s=me({client:e,count:t+1,create:r,meta:o,nameTransformer:n}):r&&(e.types[i]=o,s={created:!0,name:i}),s},Gs=({client:e,name:t})=>{let r={deleted:!1,name:""};return e.types[t]&&(delete e.types[t],r={deleted:!0,name:t}),r},gn=e=>{let t=b();return P(t)?I({input:mn(e)}).replace(oe,"_$1"):e};var Ue=e=>`${e.method.toUpperCase()} ${e.path}`,br=e=>{let t=e.find(r=>r.in==="header");return t?t.name:null},Pe=e=>e.some(r=>r.isRequired),Or=e=>{if(e==="default")return "default";if(e==="1XX")return "1XX";if(e==="2XX")return "2XX";if(e==="3XX")return "3XX";if(e==="4XX")return "4XX";if(e==="5XX")return "5XX";if(/\d{3}/g.test(e)){let t=Number.parseInt(e,10);if(t>=100&&t<600)return t}return null},Rr=(e,t)=>e.code>t.code?1:e.code<t.code?-1:0,Am=e=>e==="3XX"||e==="4XX"||e==="5XX"||typeof e=="number"&&e>=300,Js=e=>e==="2XX"||typeof e=="number"&&e>=200&&e<300,Im=(e,t)=>{let r=[],o=m=>{r.includes(m)||(r=[...r,m]);};t.some(({code:m})=>Js(m))||o("success");let s=(e.description??"").toLocaleLowerCase(),i=e.$refs.join("|").toLocaleLowerCase(),a=["error","problem"];return ["success"].some(m=>s.includes(m)||i.includes(m))&&o("success"),a.some(m=>s.includes(m)||i.includes(m))&&o("error"),r.length||o("error"),r},Tr=({config:e,operationKey:t})=>{let r=e.plugins["@hey-api/sdk"]?.filter?new RegExp(e.plugins["@hey-api/sdk"]?.filter):void 0;return !r||r.test(t)},Cr=({config:e,method:t,operationId:r,path:o})=>{if(e.plugins["@hey-api/sdk"]?.operationId&&r)return I({input:Be(r)});let n=o;return P(e)&&(n=n.replace(/[^/]*?{api-version}.*?\//g,"")),n=n.replace(/{(.*?)}/g,"by-$1").replace(/[/:]/g,"-"),I({input:`${t}-${n}`})},Pr=e=>{let t=b();return !P(t)||e.prop!=="api-version"},Sr=e=>{let t=b();return P(t)?gn(e.prop):e.prop},Ar=e=>e.map(t=>{let{code:r}=t;return r==="default"?t.responseTypes=Im(t,e):Js(r)?t.responseTypes=["success"]:Am(r)&&(t.responseTypes=["error"]),t});var k=(e,t)=>{if(e.default===void 0||e.default===null)return e.default;switch(At(e).find(n=>n!=="null")||typeof e.default){case"int":case"integer":case"number":if(t?.export==="enum"&&t.enum?.[e.default]){let{value:n}=t.enum[e.default];return n}return e.default;case"array":case"boolean":case"object":case"string":return e.default;default:return}};var Zs=({openApi:e,parameter:t,types:r})=>{let o={$refs:[],base:"unknown",description:t.description||null,enum:[],enums:[],exclusiveMaximum:t.exclusiveMaximum,exclusiveMinimum:t.exclusiveMinimum,export:"interface",format:t.format,imports:[],in:t.in,isDefinition:!1,isNullable:t["x-nullable"]===!0,isReadOnly:!1,isRequired:t.required===!0,link:null,maxItems:t.maxItems,maxLength:t.maxLength,maximum:t.maximum,mediaType:null,minItems:t.minItems,minLength:t.minLength,minimum:t.minimum,multipleOf:t.multipleOf,pattern:N(t.pattern),prop:t.name,properties:[],template:null,type:"unknown",uniqueItems:t.uniqueItems},n={...o,name:Sr(o)};if(t.$ref){let i=T({type:t.$ref});return n={...n,$refs:[...n.$refs,...i.$refs],base:i.base,export:"reference",imports:[...n.imports,...i.imports],template:i.template,type:i.type},n.default=k(t,n),n}if(t.enum){let i=It(t,t.enum);if(i.length)return n={...n,base:"string",enum:[...n.enum,...i],export:"enum",type:"string"},n.default=k(t,n),n}if(t.type==="array"&&t.items){let i=T({format:t.items.format,type:t.items.type});return n={...n,$refs:[...n.$refs,...i.$refs],base:i.base,export:"array",imports:[...n.imports,...i.imports],template:i.template,type:i.type},n.default=k(t,n),n}if(t.type==="object"&&t.items){let i=T({format:t.items.format,type:t.items.type});return n={...n,$refs:[...n.$refs,...i.$refs],base:i.base,export:"dictionary",imports:[...n.imports,...i.imports],template:i.template,type:i.type},n.default=k(t,n),n}let s=t.schema;if(s){if(s.$ref?.startsWith("#/parameters/")&&(s=v(e,s)),s.$ref){let a=T({type:s.$ref});return n={...n,$refs:[...n.$refs,...a.$refs],base:a.base,export:"reference",imports:[...n.imports,...a.imports],template:a.template,type:a.type},n.default=k(t,n),n}let i=Ce({definition:s,openApi:e,types:r});return n={...n,$refs:[...n.$refs,...i.$refs],base:i.base,enum:[...n.enum,...i.enum],enums:[...n.enums,...i.enums],export:i.export,imports:[...n.imports,...i.imports],link:i.link,properties:[...n.properties,...i.properties],template:i.template,type:i.type},n.default=k(t,n),n}if(t.type){let i=T({format:t.format,type:t.type});return n={...n,$refs:[...n.$refs,...i.$refs],base:i.base,export:"generic",imports:[...n.imports,...i.imports],template:i.template,type:i.type},n.default=k(t,n),n}return n};var Em=["body","formData","header","path","query"],Ir=({openApi:e,parameters:t,types:r})=>{let o={$refs:[],imports:[],parameters:[],parametersBody:null,parametersCookie:[],parametersForm:[],parametersHeader:[],parametersPath:[],parametersQuery:[]};return t.forEach(n=>{let s=v(e,n),i=Zs({openApi:e,parameter:s,types:r}),a=!Pr(i);if(!(!Em.includes(s.in)||a)){switch(s.in){case"body":o.parametersBody=i;break;case"formData":o.parametersForm=[...o.parametersForm,i];break;case"header":o.parametersHeader=[...o.parametersHeader,i];break;case"path":o.parametersPath=[...o.parametersPath,i];break;case"query":o.parametersQuery=[...o.parametersQuery,i];break}o.$refs=[...o.$refs,...i.$refs],o.imports=[...o.imports,...i.imports],o.parameters=[...o.parameters,i];}}),o};function Er(e){return e.sort((t,r)=>{let o=t.isRequired&&t.default===void 0,n=r.isRequired&&r.default===void 0;return o&&!n?-1:n&&!o?1:0})}var Ys=({code:e,openApi:t,response:r,types:o})=>{let n={$refs:[],base:e!==204?"unknown":"void",code:e,description:r.description||null,enum:[],enums:[],export:"generic",imports:[],in:"response",isDefinition:!1,isNullable:!1,isReadOnly:!1,isRequired:!1,link:null,name:"",properties:[],responseTypes:[],template:null,type:e!==204?"unknown":"void"},s=r.schema;if(s){if(s.$ref?.startsWith("#/responses/")&&(s=v(t,s)),s.$ref){let a=T({type:s.$ref});return n.export="reference",n.type=a.type,n.base=a.base,n.template=a.template,n.imports=[...n.imports,...a.imports],n}let i=Ce({definition:s,openApi:t,types:o});return n.export=i.export,n.type=i.type,n.base=i.base,n.template=i.template,n.link=i.link,n.isReadOnly=i.isReadOnly,n.isRequired=i.isRequired,n.isNullable=i.isNullable,n.format=i.format,n.maximum=i.maximum,n.exclusiveMaximum=i.exclusiveMaximum,n.minimum=i.minimum,n.exclusiveMinimum=i.exclusiveMinimum,n.multipleOf=i.multipleOf,n.maxLength=i.maxLength,n.minLength=i.minLength,n.maxItems=i.maxItems,n.minItems=i.minItems,n.uniqueItems=i.uniqueItems,n.maxProperties=i.maxProperties,n.minProperties=i.minProperties,n.pattern=N(i.pattern),n.imports=[...n.imports,...i.imports],n.enum=[...n.enum,...i.enum],n.enums=[...n.enums,...i.enums],n.properties=[...n.properties,...i.properties],n}if(r.headers)for(let i in r.headers)return n.in="header",n.name=i,n.type="string",n.base="string",n;return n};var ei=({openApi:e,responses:t,types:r})=>{let o=[];return Object.entries(t).forEach(([n,s])=>{let i=Or(n);if(!i)return;let a=v(e,s),c=Ys({code:i,openApi:e,response:a,types:r});o=[...o,c];}),o=Ar(o),o.sort(Rr)};var ti=({method:e,op:t,openApi:r,pathParams:o,types:n,url:s})=>{let i={$refs:[],deprecated:t.deprecated===!0,description:t.description||null,id:t.operationId||null,imports:[],method:e.toUpperCase(),parameters:[...o.parameters],parametersBody:o.parametersBody,parametersCookie:[...o.parametersCookie],parametersForm:[...o.parametersForm],parametersHeader:[...o.parametersHeader],parametersPath:[...o.parametersPath],parametersQuery:[...o.parametersQuery],path:s,responseHeader:null,responses:[],summary:t.summary||null,tags:t.tags||null},a={...i,name:Cr({config:b(),method:i.method,operationId:t.operationId,path:i.path})};if(t.parameters){let c=Ir({openApi:r,parameters:t.parameters,types:n});a.$refs=[...a.$refs,...c.$refs],a.imports=[...a.imports,...c.imports],a.parameters=[...a.parameters,...c.parameters],a.parametersBody=c.parametersBody,a.parametersCookie=[...a.parametersCookie,...c.parametersCookie],a.parametersForm=[...a.parametersForm,...c.parametersForm],a.parametersHeader=[...a.parametersHeader,...c.parametersHeader],a.parametersPath=[...a.parametersPath,...c.parametersPath],a.parametersQuery=[...a.parametersQuery,...c.parametersQuery];}if(t.responses){a.responses=ei({openApi:r,responses:t.responses,types:n});let c=a.responses.filter(m=>m.responseTypes.includes("success"));a.responseHeader=br(c),c.forEach(m=>{a.$refs=[...a.$refs,...m.$refs],a.imports=[...a.imports,...m.imports];});}return a.parameters=Er(a.parameters),a};var ri=({openApi:e,types:t})=>{let r=new Map,o=[];for(let n in e.paths){let s=e.paths[n],i=Ir({openApi:e,parameters:s.parameters??[],types:t});for(let a in s){let c=a,m=Ue({method:c,path:n});if(lr.includes(c)){let l=s[c];if(l.operationId&&(r.has(l.operationId)?console.warn(`\u2757\uFE0F Duplicate operationId: ${l.operationId} in ${m}. Please ensure your operation IDs are unique. This behavior is not supported and will likely lead to unexpected results.`):r.set(l.operationId,m)),Tr({config:b(),operationKey:m})){let u=ti({method:c,op:l,openApi:e,pathParams:i,types:t,url:n});o.push(u);}}}}return o};var ni=e=>{let t=e.schemes?.[0]||"http",r=e.host,o=e.basePath||"";return (r?`${t}://${r}${o}`:o).replace(/\/$/g,"")};var oi=e=>{let t=ur(e.info.version),r=ni(e),{models:o,types:n}=Us(e),s=ri({openApi:e,types:n});return {models:o,operations:s,server:r,types:n,version:t}};var vm=e=>{let t={};for(let r in e)t[e[r]]=r;return t},si=(e,t)=>{if(e.components&&t){for(let r in e.components.schemas)if(e.components.schemas.hasOwnProperty(r)){let o=e.components.schemas[r];if(o.discriminator&&o.oneOf?.length&&o.oneOf.some(n=>n.$ref&&_t(n.$ref)==t.name))return o.discriminator}}},ii=(e,t)=>{if(e.mapping){let r=vm(e.mapping),o=Object.keys(r).find(n=>_t(n)==t.name);if(o&&r[o])return r[o]}return t.name};var hn=({debug:e,definition:t,getModel:r,model:o,openApi:n,types:s})=>{let i=typeof t.additionalProperties=="object"?t.additionalProperties:{},a=r({debug:e,definition:i,openApi:n,parentDefinition:t,types:s});if(i.$ref){let c=T({type:i.$ref});return o.base=c.base,o.default=k(t,o),o.export="dictionary",o.imports.push(...c.imports),o.template=c.template,o.type=c.type,o}if(t.additionalProperties&&t.properties&&Object.keys(t.properties).length>0){let c=typeof t.additionalProperties=="object"&&t.additionalProperties.type&&!Array.isArray(t.additionalProperties.type)?t.additionalProperties.type:a.base,m=[T({type:c}).base,...o.properties.map(l=>l.base)];return a.base=m.filter(z).join(" | "),a.default=k(t,o),a.export="generic",a.isRequired=t.additionalProperties===!0,a.name="[key: string]",a}return o.base=a.base,o.default=k(t,o),o.export="dictionary",o.imports.push(...a.imports),o.link=a,o.template=a.template,o.type=a.type,o},vr=({debug:e,definition:t,getModel:r,openApi:o,parent:n,types:s})=>{let i=[],a=si(o,n);return Object.entries(t.properties??{}).forEach(([c,m])=>{let l=!!t.required?.includes(c),u={default:m.default,deprecated:m.deprecated===!0,description:m.description||null,exclusiveMaximum:m.exclusiveMaximum,exclusiveMinimum:m.exclusiveMinimum,format:m.type==="array"?m.items?.format??m.format:m.format,in:"",isDefinition:!1,isReadOnly:m.readOnly===!0,isRequired:l,maxItems:m.maxItems,maxLength:m.maxLength,maxProperties:m.maxProperties,maximum:m.maximum,minItems:m.minItems,minLength:m.minLength,minProperties:m.minProperties,minimum:m.minimum,multipleOf:m.multipleOf,name:se(c),pattern:N(m.pattern),uniqueItems:m.uniqueItems};if(n&&a?.propertyName==c){i=[...i,{...u,$refs:[],base:`'${ii(a,n)}'`,enum:[],enums:[],export:"reference",imports:[],isNullable:We(m),link:null,properties:[],template:null,type:"string"}];return}if(m.$ref){let g=T({type:m.$ref});i=[...i,{...u,$refs:g.$refs,base:g.base,enum:[],enums:[],export:"reference",imports:g.imports,isNullable:g.isNullable||We(m),link:null,properties:[],template:g.template,type:g.type}];return}let f=r({debug:e,definition:m,initialValues:u,openApi:o,parentDefinition:t,types:s});f.isNullable=f.isNullable||We(m),i=[...i,f];}),i};var ai=({debug:e,definitions:t,getModel:r,openApi:o,required:n,types:s})=>t.reduce((a,c)=>{if(c.$ref){let m=T({type:c.$ref}),l={$ref:c.$ref,name:m.base};s[m.base]=l;let u=v(o,c);return [...a,...r({debug:e,definition:u,meta:l,openApi:o,types:s}).properties]}return [...a,...r({debug:e,definition:c,openApi:o,parentDefinition:c,types:s}).properties]},[]).filter(a=>!a.isRequired&&n.includes(a.name)).map(a=>({...a,isRequired:!0}));var kr=e=>[{definitions:e.allOf,type:"all-of"},{definitions:e.anyOf,type:"any-of"},{definitions:e.oneOf,type:"one-of"}].find(r=>r.definitions?.length),pi=({debug:e,definition:t,definitions:r,getModel:o,model:n,openApi:s,type:i,types:a})=>{let c={$refs:n.$refs,enums:n.enums,export:i,imports:n.imports,properties:n.properties},m=[];if(r.map(l=>o({debug:e,definition:l,openApi:s,parentDefinition:t,types:a})).forEach(l=>{c.$refs=[...c.$refs,...l.$refs],c.imports=[...c.imports,...l.imports],c.enums=[...c.enums,...l.enums],c.properties=[...c.properties,l];}),t.required&&i==="all-of"){let l=ai({debug:e,definitions:r,getModel:o,openApi:s,required:t.required,types:a});l.forEach(u=>{c.$refs=[...c.$refs,...u.$refs],c.imports=[...c.imports,...u.imports],c.enums=[...c.enums,...u.enums];}),m=[...m,...l];}if(t.properties){let l=vr({definition:t,getModel:o,openApi:s,types:a});l.forEach(u=>{c.$refs=[...c.$refs,...u.$refs],c.imports=[...c.imports,...u.imports],c.enums=[...c.enums,...u.enums],u.export==="enum"&&(c.enums=[...c.enums,u]);}),m=[...m,...l];}if(m.length){let l=kr(t);if(l){let u={$refs:[],base:"unknown",description:"",enum:[],enums:[],export:"interface",imports:[],in:"",isDefinition:!1,isNullable:!1,isReadOnly:!1,isRequired:!1,link:null,name:"properties",properties:m,template:null,type:"unknown"};l.type==="one-of"?(c.properties=[{...c,base:"",description:null,enum:[],in:"",isDefinition:!1,isNullable:!1,isReadOnly:!1,isRequired:!0,link:null,name:"",template:null,type:""},u],c.export="all-of"):c.properties=[...c.properties,u];}}return c};var B=({debug:e,definition:t,initialValues:r={},isDefinition:o=!1,meta:n,openApi:s,parentDefinition:i=null,types:a})=>{let c=At(t),m=_s(t,c),l={$refs:[],base:"unknown",deprecated:!!t.deprecated,description:t.description||null,enum:[],enums:[],exclusiveMaximum:t.exclusiveMaximum,exclusiveMinimum:t.exclusiveMinimum,export:"interface",format:t.format,imports:[],in:"",isDefinition:o,isNullable:We(t),isReadOnly:t.readOnly===!0,isRequired:!1,link:null,maxItems:t.maxItems,maxLength:t.maxLength,maxProperties:t.maxProperties,maximum:t.maximum,meta:n,minItems:t.minItems,minLength:t.minLength,minProperties:t.minProperties,minimum:t.minimum,multipleOf:t.multipleOf,name:n?.name??"",pattern:N(t.pattern),properties:[],template:null,type:"unknown",uniqueItems:t.uniqueItems,...r};if(t.$ref){let f=T({debug:e,type:t.$ref});return l.$refs=[...l.$refs,decodeURIComponent(t.$ref)],l.base=f.base,l.export="reference",l.imports=[...l.imports,...f.imports],l.template=f.template,l.type=f.type,l.default=k(t,l),l}if(m==="enum"){let f=It(t,t.enum);if(f.length)return l.base="string",l.enum=[...l.enum,...f],l.export="enum",l.type="string",l.default=k(t,l),l.meta||(l.meta=hr(l)),l}if(c.includes("array")&&(t.items||t.prefixItems)){if(t.prefixItems){let x=t.prefixItems.map(h=>B({definition:h,openApi:s,parentDefinition:t,types:a}));return l.export="array",l.$refs=[...l.$refs,...x.reduce((h,C)=>[...h,...C.$refs],[])],l.imports=[...l.imports,...x.reduce((h,C)=>[...h,...C.imports],[])],l.link=x,l.default=k(t,l),l}if(!t.items)return l;if(t.items.$ref){let x=T({type:t.items.$ref});return l.$refs=[...l.$refs,decodeURIComponent(t.items.$ref)],l.base=x.base,l.export="array",l.imports=[...l.imports,...x.imports],l.template=x.template,l.type=x.type,l.default=k(t,l),l}if(t.items.anyOf&&i&&i.type){let x=kr(i);if(x&&x.definitions.some(h=>!At(h).includes("array")))return B({definition:t.items,openApi:s,parentDefinition:t,types:a})}let f=Array.isArray(t.items)?{anyOf:t.items}:t.items,g=B({definition:f,openApi:s,parentDefinition:t,types:a});return l.base=g.base,l.export="array",l.$refs=[...l.$refs,...g.$refs],l.imports=[...l.imports,...g.imports],l.link=g,l.template=g.template,l.type=g.type,l.default=k(t,l),l}let u=kr(t);if(u){let f=pi({...u,debug:e,definition:t,getModel:B,model:l,openApi:s,types:a});return {...l,...f}}if(c.includes("object")||t.properties||t.additionalProperties){if(t.properties&&(Object.keys(t.properties).length>0||!t.additionalProperties)){if(l.base="unknown",l.export="interface",l.type="unknown",l.default=k(t,l),vr({debug:e,definition:t,getModel:B,openApi:s,parent:l,types:a}).forEach(x=>{l.$refs=[...l.$refs,...x.$refs],l.enums=[...l.enums,...x.enums],l.imports=[...l.imports,...x.imports],l.properties=[...l.properties,x],x.export==="enum"&&(l.enums=[...l.enums,x]);}),t.additionalProperties){let x=hn({debug:e,definition:t,getModel:B,model:l,openApi:s,types:a});l.properties=[...l.properties,x];}return !l.properties.length&&l.base==="unknown"&&l.type==="unknown"&&(l.export="dictionary",l.name||(l.name="[key: string]")),l}return hn({debug:e,definition:t,getModel:B,model:l,openApi:s,types:a})}if(t.const!==void 0){let f=t.const,g=typeof f=="string"?`"${f}"`:`${f}`;return l.base=g,l.export="const",l.type=g,l}if(c.length){let f=T({format:t.format,type:t.type});return l.base=f.base,l.export="generic",l.$refs=[...l.$refs,...f.$refs],l.imports=[...l.imports,...f.imports],l.isNullable=f.isNullable||l.isNullable,l.template=f.template,l.type=f.type,l.default=k(t,l),l}return l};var qr=e=>{if(e.schema)return e.schema;if(e.content){let t=Object.entries(e.content);for(let[r,o]of t)if(o.schema){let n=r;return e.content[n].schema}}};var ci=e=>{let t={},r=[];return e.components?(Object.entries(e.components.schemas??{}).forEach(([o,n])=>{let s=xr(o);t[s.name]=s;let i=B({definition:n,isDefinition:!0,meta:s,openApi:e,types:t});r=[...r,i];}),Object.entries(e.components.parameters??{}).forEach(([o,n])=>{let s=qr(n);if(!s)return;let i=Xs(o);t[i.name]=i;let a=B({definition:s,isDefinition:!0,meta:i,openApi:e,types:t});a.deprecated=n.deprecated,a.description=n.description||null,r=[...r,a];}),{models:r,types:t}):{models:r,types:t}};var mi=({openApi:e,parameter:t,types:r})=>{let o={$refs:[],base:"unknown",deprecated:t.deprecated===!0,description:t.description||null,enum:[],enums:[],export:"interface",imports:[],in:t.in,isDefinition:!1,isNullable:We(t),isReadOnly:!1,isRequired:t.required===!0,link:null,mediaType:null,prop:t.name,properties:[],template:null,type:"unknown"},n={...o,name:Sr(o)};if(t.$ref){let i=T({type:t.$ref});return n={...n,$refs:[...n.$refs,...i.$refs],base:i.base,export:"reference",imports:[...n.imports,...i.imports],template:i.template,type:i.type},n}let s=qr(t);if(s){if(s.$ref?.startsWith(St)&&(s=v(e,s)),s.$ref){let a=T({type:s.$ref});return n={...n,$refs:[...n.$refs,...a.$refs],base:a.base,export:"reference",imports:[...n.imports,...a.imports],template:a.template,type:a.type},n.default=k(s),n}let i=B({definition:s,openApi:e,types:r});return n={...n,$refs:[...n.$refs,...i.$refs],base:i.base,enum:[...n.enum,...i.enum],enums:[...n.enums,...i.enums],exclusiveMaximum:i.exclusiveMaximum,exclusiveMinimum:i.exclusiveMinimum,export:i.export,format:i.format,imports:[...n.imports,...i.imports],isNullable:n.isNullable||i.isNullable,isReadOnly:i.isReadOnly,isRequired:n.isRequired||i.isRequired,link:i.link,maxItems:i.maxItems,maxLength:i.maxLength,maxProperties:i.maxProperties,maximum:i.maximum,minItems:i.minItems,minLength:i.minLength,minProperties:i.minProperties,minimum:i.minimum,multipleOf:i.multipleOf,pattern:N(i.pattern),properties:[...n.properties,...i.properties],template:i.template,type:i.type,uniqueItems:i.uniqueItems},(n.enum.length||n.enums.length)&&!n.meta&&(n.meta=hr(n)),n.default=i.default,n}return n};var km=["cookie","formData","header","path","query"],jr=({openApi:e,parameters:t,types:r})=>{let o={$refs:[],imports:[],parameters:[],parametersBody:null,parametersCookie:[],parametersForm:[],parametersHeader:[],parametersPath:[],parametersQuery:[]};return t.forEach(n=>{let s=v(e,n),i=mi({openApi:e,parameter:s,types:r}),a=!Pr(i);if(!(!km.includes(s.in)||a)){switch(s.in){case"cookie":o.parametersCookie=[...o.parametersCookie,i];break;case"formData":o.parametersForm=[...o.parametersForm,i];break;case"header":o.parametersHeader=[...o.parametersHeader,i];break;case"path":o.parametersPath=[...o.parametersPath,i];break;case"query":o.parametersQuery=[...o.parametersQuery,i];break}o.$refs=[...o.$refs,...i.$refs],o.imports=[...o.imports,...i.imports],o.parameters=[...o.parameters,i];}}),o};var qm=["application/json-patch+json","application/json","application/ld+json","application/x-www-form-urlencoded","multipart/batch","multipart/form-data","multipart/mixed","multipart/related","text/json","text/plain"],Nr=(e,t)=>{let r=Object.keys(t).filter(n=>{let s=n.split(";")[0].trim();return qm.includes(s)}).find(n=>!!t[n]?.schema);if(r)return {mediaType:r,schema:t[r].schema};let o=Object.keys(t).find(n=>!!t[n]?.schema);if(o)return {mediaType:o,schema:t[o].schema}};var li=({body:e,debug:t,openApi:r,types:o})=>{let n=e["x-body-name"]??"requestBody",s={$refs:[],base:"unknown",default:void 0,description:e.description||null,enum:[],enums:[],export:"interface",imports:[],in:"body",isDefinition:!1,isNullable:e.nullable===!0,isReadOnly:!1,isRequired:e.required===!0,link:null,mediaType:null,name:n,prop:n,properties:[],template:null,type:"unknown"};if(!e.content)return s;let i=Nr(r,e.content);if(!i)return s;switch(s.mediaType=i.mediaType,s.mediaType){case"application/x-www-form-urlencoded":case"multipart/form-data":s.in="formData",s.name="formData",s.prop="formData";break}if(i.schema.$ref){let c=T({type:i.schema.$ref});return s.export="reference",s.type=c.type,s.base=c.base,s.template=c.template,s.$refs=[...s.$refs,...c.$refs],s.imports=[...s.imports,...c.imports],s}let a=B({debug:t,definition:i.schema,openApi:r,types:o});return s.$refs=[...s.$refs,...a.$refs],s.base=a.base,s.enum=[...s.enum,...a.enum],s.enums=[...s.enums,...a.enums],s.exclusiveMaximum=a.exclusiveMaximum,s.exclusiveMinimum=a.exclusiveMinimum,s.export=a.export,s.format=a.format,s.imports=[...s.imports,...a.imports],s.isNullable=s.isNullable||a.isNullable,s.isReadOnly=a.isReadOnly,s.isRequired=s.isRequired||a.isRequired,s.link=a.link,s.maximum=a.maximum,s.maxItems=a.maxItems,s.maxLength=a.maxLength,s.maxProperties=a.maxProperties,s.minimum=a.minimum,s.minItems=a.minItems,s.minLength=a.minLength,s.minProperties=a.minProperties,s.multipleOf=a.multipleOf,s.pattern=N(a.pattern),s.properties=[...s.properties,...a.properties],s.template=a.template,s.type=a.type,s.uniqueItems=a.uniqueItems,s};var ui=({code:e,openApi:t,response:r,types:o})=>{let n={$refs:[],base:e!==204?"unknown":"void",code:e,description:r.description||null,enum:[],enums:[],export:"generic",imports:[],in:"response",isDefinition:!1,isNullable:!1,isReadOnly:!1,isRequired:!1,link:null,name:"",properties:[],responseTypes:[],template:null,type:e!==204?"unknown":"void"};if(r.content){let s=Nr(t,r.content);if(s){if(s.schema.$ref?.startsWith("#/components/responses/")&&(s.schema=v(t,s.schema)),s.schema.$ref){let a=T({type:s.schema.$ref});return n.base=a.base,n.export="reference",n.$refs=[...n.$refs,...a.$refs],n.imports=[...n.imports,...a.imports],n.template=a.template,n.type=a.type,n}let i=B({definition:s.schema,openApi:t,types:o});return n.export=i.export,n.type=i.type,n.base=i.base,n.template=i.template,n.link=i.link,n.isReadOnly=i.isReadOnly,n.isRequired=i.isRequired,n.isNullable=i.isNullable,n.format=i.format,n.maximum=i.maximum,n.exclusiveMaximum=i.exclusiveMaximum,n.minimum=i.minimum,n.exclusiveMinimum=i.exclusiveMinimum,n.multipleOf=i.multipleOf,n.maxLength=i.maxLength,n.minLength=i.minLength,n.maxItems=i.maxItems,n.minItems=i.minItems,n.uniqueItems=i.uniqueItems,n.maxProperties=i.maxProperties,n.minProperties=i.minProperties,n.pattern=N(i.pattern),n.$refs=[...n.$refs,...i.$refs],n.imports=[...n.imports,...i.imports],n.enum=[...n.enum,...i.enum],n.enums=[...n.enums,...i.enums],n.properties=[...n.properties,...i.properties],n}}if(r.headers)for(let s in r.headers)return n.in="header",n.name=s,n.type="string",n.base="string",n;return n};var fi=({debug:e,openApi:t,responses:r,types:o})=>{let n=[];return Object.entries(r).forEach(([s,i])=>{let a=Or(s);if(!a)return;let c=v(t,i),m=ui({code:a,openApi:t,response:c,types:o});n=[...n,m];}),n=Ar(n),n.sort(Rr)};var Et=(e,t)=>{let r=[...e],o=[...t];for(;o.length>0;){let n=o[0];o=o.slice(1),r.every(i=>i.in!==n.in||i.name!==n.name)&&(r=[...r,n]);}return r},di=({debug:e,method:t,op:r,openApi:o,pathParams:n,types:s,url:i})=>{let a={$refs:[],deprecated:!!r.deprecated,description:r.description||null,id:r.operationId||null,imports:[],method:t.toUpperCase(),parameters:[],parametersBody:n.parametersBody,parametersCookie:[],parametersForm:[],parametersHeader:[],parametersPath:[],parametersQuery:[],path:i,responseHeader:null,responses:[],summary:r.summary||null,tags:r.tags||null},c={...a,name:Cr({config:b(),method:a.method,operationId:r.operationId,path:a.path})};if(r.parameters){let m=jr({openApi:o,parameters:r.parameters,types:s});c.$refs=[...c.$refs,...m.$refs],c.imports=[...c.imports,...m.imports],c.parameters=[...c.parameters,...m.parameters],c.parametersBody=m.parametersBody,c.parametersCookie=[...c.parametersCookie,...m.parametersCookie],c.parametersForm=[...c.parametersForm,...m.parametersForm],c.parametersHeader=[...c.parametersHeader,...m.parametersHeader],c.parametersPath=[...c.parametersPath,...m.parametersPath],c.parametersQuery=[...c.parametersQuery,...m.parametersQuery];}if(r.requestBody){let m=v(o,r.requestBody),l=li({body:m,debug:e,openApi:o,types:s});c.$refs=[...c.$refs,...l.$refs],c.imports=[...c.imports,...l.imports],c.parameters=[...c.parameters,l],c.parametersBody=l;}if(r.responses){c.responses=fi({openApi:o,responses:r.responses,types:s});let m=c.responses.filter(l=>l.responseTypes.includes("success"));c.responseHeader=br(m),m.forEach(l=>{c.$refs=[...c.$refs,...l.$refs],c.imports=[...c.imports,...l.imports];});}return c.parameters=Et(c.parameters,n.parameters),c.parametersCookie=Et(c.parametersCookie,n.parametersCookie),c.parametersForm=Et(c.parametersForm,n.parametersForm),c.parametersHeader=Et(c.parametersHeader,n.parametersHeader),c.parametersPath=Et(c.parametersPath,n.parametersPath),c.parametersQuery=Et(c.parametersQuery,n.parametersQuery),c.parameters=Er(c.parameters),c};var yi=({openApi:e,types:t})=>{let r=new Map,o=[];for(let n in e.paths){let s=e.paths[n],i=jr({openApi:e,parameters:s.parameters??[],types:t});for(let a in s){let c=a,m=Ue({method:c,path:n});if(lr.includes(c)){let l=s[c];if(l.operationId&&(r.has(l.operationId)?console.warn(`\u2757\uFE0F Duplicate operationId: ${l.operationId} in ${m}. Please ensure your operation IDs are unique. This behavior is not supported and will likely lead to unexpected results.`):r.set(l.operationId,m)),Tr({config:b(),operationKey:m})){let u=di({method:c,op:l,openApi:e,pathParams:i,types:t,url:n});o.push(u);}}}}return o};var gi=e=>{let t=e.servers?.[0],r=t?.variables||{},o=t?.url||"";return Object.entries(r).forEach(([n,s])=>{o=o.replace(`{${n}}`,s.default);}),o.replace(/\/$/g,"")};var hi=e=>{let t=ur(e.info.version),r=gi(e),{models:o,types:n}=ci(e),s=yi({openApi:e,types:n});return {models:o,operations:s,server:r,types:n,version:t}};function xi({openApi:e}){let t=e;if("openapi"in t)return hi(t);if("swagger"in t)return oi(t);throw new Error(`Unsupported OpenAPI specification: ${JSON.stringify(t,null,2)}`)}var bi=({config:e,spec:t})=>{let r=new ir({config:e,spec:t});switch(r.spec.openapi){case"3.0.0":case"3.0.1":case"3.0.2":case"3.0.3":case"3.0.4":return ln(r),r;case"3.1.0":case"3.1.1":return un(r),r;default:return}};var Z=class{_headers=[];_imports=new Map;_items=[];_name;_path;namespaces={type:{},value:{}};constructor({dir:t,header:r=!0,name:o}){this._name=this._setName(o),this._path=ae__default.default.resolve(t,this._name),r&&this._headers.push("// This file is auto-generated by @hey-api/openapi-ts");}add(...t){this._items=this._items.concat(t);}blockIdentifier({$ref:t,namespace:r}){let o=this.namespaces[r][t];if(!o)throw new Error(`Identifier for $ref ${t} in namespace ${r} not found`);return o.name=!1,{created:!1,name:o.name}}identifier({namespace:t,...r}){let o;switch(t){case"type":case"value":o=n=>X(n).replace(oe,"_$1");break}return Oi({namespace:this.namespaces[t],validNameTransformer:o,...r})}import({module:t,...r}){let o=this._imports.get(t);o||(o=new Map,this._imports.set(t,o));let n=o.get(r.name);return n||(o.set(r.name,r),r)}isEmpty(){return !this._items.length}nameWithoutExtension(){let{name:t}=xn(this._name);return t}relativePathToFile({context:t,id:r}){let o=t.file({id:r});if(!o)throw new Error(`File with id ${r} does not exist`);let n=this._path.substring(t.config.output.path.length+1),s=o._path.substring(t.config.output.path.length+1),i=n.split(ae__default.default.sep);return `${new Array(i.length).fill("").join("../")||"./"}${xn(s).name}`}remove(t){fs$1.rmSync(this._path,t);}removeNode(){this._items=this._items.slice(0,this._items.length-1);}_setName(t){if(t.includes("index"))return t;let{extension:r,name:o}=xn(t);return [o,"gen",r].filter(Boolean).join(".")}toString(t=`
`){let r=[];this._headers.length&&r.push(this._headers.join(`
`));let o=[];for(let[n,s]of this._imports.entries()){let i=Array.from(s.values()),a=p.namedImportDeclarations({imports:i,module:n});o.push(ge({node:a}));}return o.length&&r.push(o.join(`
`)),r=r.concat(this._items.map(n=>typeof n=="string"?n:ge({node:n,unescape:!0}))),r.join(t)}write(t=`
`){if(this.isEmpty()){this.remove({force:!0});return}let r=this._path;if(typeof this._path=="string"){let o=this._path.split(ae__default.default.sep);r=o.slice(0,o.length-1).join(ae__default.default.sep);}bt(r),fs$1.writeFileSync(this._path,this.toString(t));}},Oi=({$ref:e,count:t=1,create:r=!1,namespace:o,validNameTransformer:n})=>{let s=e.split("/"),i=s[s.length-1]||"";if(!i)return {created:!1,name:""};let a=o[e];if(a)return {created:!1,name:a.name};t>1&&(i=`${i}${t}`);let c=o[i];return c?c.$ref===e?{created:!1,name:c.name}:Oi({$ref:e,count:t+1,create:r,namespace:o,validNameTransformer:n}):r?(c={$ref:e,name:n?n(i):i},o[i]=c,o[c.$ref]=c,{created:!0,name:c.name}):{created:!1,name:""}},xn=e=>{let t=e.match(/\.[0-9a-z]+$/i),r=t?t[0].slice(1):"",o=e.slice(0,e.length-(r?r.length+1:0));return {extension:r,name:o}};var Ri=({files:e})=>{let t=b();e.index=new Z({dir:t.output.path,name:"index.ts"}),j(t)&&e.index.add(p.exportNamedDeclaration({exports:j(t),module:`./${j(t)}`})),t.exportCore&&(e.index.add(p.exportNamedDeclaration({exports:"ApiError",module:"./core/ApiError"})),t.plugins["@hey-api/sdk"]?.response==="response"&&e.index.add(p.exportNamedDeclaration({exports:{asType:!0,name:"ApiResult"},module:"./core/ApiResult"})),j(t)&&e.index.add(p.exportNamedDeclaration({exports:"BaseHttpRequest",module:"./core/BaseHttpRequest"})),t.client.name!=="legacy/angular"&&e.index.add(p.exportNamedDeclaration({exports:["CancelablePromise","CancelError"],module:"./core/CancelablePromise"})),e.index.add(p.exportNamedDeclaration({exports:["OpenAPI",{asType:!0,name:"OpenAPIConfig"}],module:"./core/OpenAPI"}))),Object.keys(e).sort().forEach(r=>{let o=e[r];r==="index"||o.isEmpty()||["schemas","sdk","transformers","types"].includes(r)&&e.index.add(p.exportAllDeclaration({module:`./${o.nameWithoutExtension()}`}));});};var Ti=async({client:e,openApi:t,templates:r})=>{let o=b(),n=t;if(e){if(o.plugins["@hey-api/sdk"]?.include&&o.plugins["@hey-api/sdk"].asClass){let a=new RegExp(o.plugins["@hey-api/sdk"].include);e.services=e.services.filter(c=>a.test(c.name));}if(o.plugins["@hey-api/typescript"]?.include){let a=new RegExp(o.plugins["@hey-api/typescript"].include);e.models=e.models.filter(c=>a.test(c.name));}}let s=ae__default.default.resolve(o.output.path);!P(o)&&o.client.bundle&&await sn({name:o.client.name,outputPath:s}),await Rs(n,s,e,r),await Ts(ae__default.default.resolve(o.output.path,"core"),e,r);let i={};for(let a of o.pluginOrder){let c=o.plugins[a],m=(c.output??"").split("/"),l=ae__default.default.resolve(o.output.path,...m.slice(0,m.length-1));i[c.name]=new Z({dir:l,name:`${m[m.length-1]}.ts`}),c._handlerLegacy({client:e,files:i,openApi:n,plugin:c});}Ri({files:i}),Object.entries(i).forEach(([a,c])=>{o.dryRun||(a==="index"?c.write():c.write(`

`));});},Ci=async({context:e})=>{let t=ae__default.default.resolve(e.config.output.path);e.config.client.bundle&&sn({name:e.config.client.name,outputPath:t});for(let o of e.config.pluginOrder){let n=e.config.plugins[o];n._handler({context:e,plugin:n});}await Os({context:e});let r=e.createFile({id:"_index",path:"index"});Object.entries(e.files).forEach(([o,n])=>{e.config.dryRun||o==="_index"||(!n.isEmpty()&&["schemas","sdk","transformers","types"].includes(o)&&r.add(p.exportAllDeclaration({module:`./${n.nameWithoutExtension()}`})),n.write(`

`));}),e.config.dryRun||r.write();};var bn="schemas",Pi=({context:e,schema:t})=>{e.config.plugins["@hey-api/schemas"]?.type==="form"&&(t.description&&delete t.description,t["x-enum-descriptions"]&&delete t["x-enum-descriptions"],t["x-enum-varnames"]&&delete t["x-enum-varnames"],t["x-enumNames"]&&delete t["x-enumNames"],t.title&&delete t.title);},Qe=({context:e,schema:t})=>{if(Array.isArray(t))return t.map(o=>Qe({context:e,schema:o}));let r=structuredClone(t);if("$ref"in r)return r.$ref=decodeURI(r.$ref),r;if(Pi({context:e,schema:r}),r.additionalProperties&&typeof r.additionalProperties!="boolean"&&(r.additionalProperties=Qe({context:e,schema:r.additionalProperties})),r.allOf&&(r.allOf=r.allOf.map(o=>Qe({context:e,schema:o}))),r.anyOf&&(r.anyOf=r.anyOf.map(o=>Qe({context:e,schema:o}))),r.items&&(r.items=Qe({context:e,schema:r.items})),r.oneOf&&(r.oneOf=r.oneOf.map(o=>Qe({context:e,schema:o}))),r.properties)for(let o in r.properties){let n=r.properties[o];typeof n!="boolean"&&(r.properties[o]=Qe({context:e,schema:n}));}return r},De=({context:e,schema:t})=>{if(Array.isArray(t))return t.map(o=>De({context:e,schema:o}));let r=structuredClone(t);if(Pi({context:e,schema:r}),r.$ref&&(r.$ref=decodeURI(r.$ref)),r.additionalProperties&&typeof r.additionalProperties!="boolean"&&(r.additionalProperties=De({context:e,schema:r.additionalProperties})),r.allOf&&(r.allOf=r.allOf.map(o=>De({context:e,schema:o}))),r.anyOf&&(r.anyOf=r.anyOf.map(o=>De({context:e,schema:o}))),r.items&&(r.items=De({context:e,schema:r.items})),r.oneOf&&(r.oneOf=r.oneOf.map(o=>De({context:e,schema:o}))),r.prefixItems&&(r.prefixItems=r.prefixItems.map(o=>De({context:e,schema:o}))),r.properties)for(let o in r.properties){let n=r.properties[o];typeof n!="boolean"&&(r.properties[o]=De({context:e,schema:n}));}return r},Si=({context:e,name:t,schema:r})=>{let o=X(t);return e.config.plugins["@hey-api/schemas"]?.nameBuilder?e.config.plugins["@hey-api/schemas"].nameBuilder(o,r):`${o}Schema`},wm=e=>{if(e.spec.components)for(let t in e.spec.components.schemas){let r=e.spec.components.schemas[t],o=Qe({context:e,schema:r}),n=p.constVariable({assertion:"const",exportConst:!0,expression:p.objectExpression({obj:o}),name:Si({context:e,name:t,schema:r})});e.file({id:bn}).add(n);}},$m=e=>{if(e.spec.components)for(let t in e.spec.components.schemas){let r=e.spec.components.schemas[t],o=De({context:e,schema:r}),n=p.constVariable({assertion:"const",exportConst:!0,expression:p.objectExpression({obj:o}),name:Si({context:e,name:t,schema:r})});e.file({id:bn}).add(n);}},Ai=({context:e,plugin:t})=>{if(e.createFile({id:bn,path:t.output}),e.spec.openapi)switch(e.spec.openapi){case"3.0.0":case"3.0.1":case"3.0.2":case"3.0.3":case"3.0.4":wm(e);break;case"3.1.0":case"3.1.1":$m(e);break;}};var On=(e,t)=>{let r=b();if(Array.isArray(e))return e.map(n=>On(n));if(typeof e!="object"||e===null)return e;let o={...e};return Object.entries(o).forEach(([n,s])=>{if(r.plugins["@hey-api/schemas"]?.type==="form"&&["description","x-enum-descriptions","x-enum-varnames","x-enumNames","title"].includes(n)&&t!=="properties"){delete o[n];return}n==="$ref"&&typeof s=="string"&&(o[n]=decodeURIComponent(s)),s&&typeof s=="object"&&(o[n]=On(s,n));}),o},Dm=(e,t)=>{let r=b(),o=X(e);return r.plugins["@hey-api/schemas"]?.nameBuilder?r.plugins["@hey-api/schemas"].nameBuilder(o,t):`${o}Schema`},Ii=({files:e,openApi:t})=>{let r=b();e.schemas=new Z({dir:r.output.path,name:"schemas.ts"});let o=(n,s)=>{let i=On(s),a=p.objectExpression({obj:i}),c=p.constVariable({assertion:"const",exportConst:!0,expression:a,name:Dm(n,s)});e.schemas.add(c);};"swagger"in t&&Object.entries(t.definitions??{}).forEach(([n,s])=>{o(n,s);}),"openapi"in t&&Object.entries(t.components?.schemas??{}).forEach(([n,s])=>{o(n,s);});};var Rn={_handler:Ai,_handlerLegacy:Ii,name:"@hey-api/schemas",nameBuilder:e=>`${e}Schema`,output:"schemas",type:"json"};var Xe=e=>{for(let t in e)if(e[t].required)return !0;return !1},Ei=e=>e?!!(Xe(e.cookie)||Xe(e.header)||Xe(e.path)||Xe(e.query)):!1,vi=e=>{if(e){for(let t in e.cookie){let r=e.cookie[t];if(r.pagination)return {in:r.location,name:r.pagination===!0?t:`${t}.${r.pagination}`,schema:r.pagination===!0?r.schema:r.schema.properties[r.pagination]}}for(let t in e.header){let r=e.header[t];if(r.pagination)return {in:r.location,name:r.pagination===!0?t:`${t}.${r.pagination}`,schema:r.pagination===!0?r.schema:r.schema.properties[r.pagination]}}for(let t in e.path){let r=e.path[t];if(r.pagination)return {in:r.location,name:r.pagination===!0?t:`${t}.${r.pagination}`,schema:r.pagination===!0?r.schema:r.schema.properties[r.pagination]}}for(let t in e.query){let r=e.query[t];if(r.pagination)return {in:r.location,name:r.pagination===!0?t:`${t}.${r.pagination}`,schema:r.pagination===!0?r.schema:r.schema.properties[r.pagination]}}}};var Me=({schema:e})=>{if(!e.items)return e;let t=[],r=[];for(let o of e.items){if(!o.type&&o.items){t.push(o);continue}if(!o.type||o.type==="boolean"||o.type==="null"||o.type==="number"||o.type==="string"||o.type==="unknown"||o.type==="void"){let n=`${o.$ref??""}${o.type??""}${o.const!==void 0?`const-${o.const}`:""}`;r.includes(n)||(r.push(n),t.push(o));continue}t.push(o);}if(e.items=t,e.items.length<=1&&e.type!=="array"&&e.type!=="enum"&&e.type!=="tuple"){let o=e.items[0];delete e.logicalOperator,delete e.items,e={...e,...o};}return e.type==="unknown"?{}:e};var Vt=e=>!!(Ei(e.parameters)||e.body?.required),ki=({context:e,operation:t})=>{if(t.body?.pagination){if(typeof t.body.pagination=="boolean")return {in:"body",name:"body",schema:t.body.schema};let r=t.body.schema.$ref?e.resolveIrRef(t.body.schema.$ref):t.body.schema;return {in:"body",name:t.body.pagination,schema:r.properties[t.body.pagination]}}return vi(t.parameters)},Mm=({statusCode:e})=>{switch(e){case"1XX":return "1XX";case"2XX":return "2XX";case"3XX":return "3XX";case"4XX":return "4XX";case"5XX":return "5XX";case"default":return "default";default:return `${e[0]}XX`}},vt=e=>{let t={};if(!e.responses)return t;let r={properties:{},type:"object"},o={properties:{},type:"object"},n;for(let a in e.responses){let c=e.responses[a];switch(Mm({statusCode:a})){case"1XX":case"3XX":break;case"2XX":o.properties[a]=c.schema;break;case"4XX":case"5XX":r.properties[a]=c.schema;break;case"default":n=c;break}}if(n){let a=!1;Object.keys(o.properties).length||(o.properties.default=n.schema,a=!0);let c=(n.schema.description??"").toLocaleLowerCase(),m=(n.schema.$ref??"").toLocaleLowerCase();["success"].some(f=>c.includes(f)||m.includes(f))&&(o.properties.default=n.schema,a=!0),["error","problem"].some(f=>c.includes(f)||m.includes(f))&&(r.properties.default=n.schema,a=!0),a||(r.properties.default=n.schema);}let s=Object.keys(r.properties);if(s.length){r.required=s,t.errors=r;let a=$({items:Object.values(r.properties),mutateSchemaOneItem:!0,schema:{}});a=Me({schema:a}),Object.keys(a).length&&a.type!=="unknown"&&(t.error=a);}let i=Object.keys(o.properties);if(i.length){o.required=i,t.responses=o;let a=$({items:Object.values(o.properties),mutateSchemaOneItem:!0,schema:{}});a=Me({schema:a}),Object.keys(a).length&&a.type!=="unknown"&&(t.response=a);}return t};function qi(e){return {...e,models:e.models.map(t=>Fm(t)),services:Lm(e.operations).map(Hm),types:{}}}var Fm=e=>({...e,$refs:e.$refs.filter((t,r,o)=>z(t,r,o)),enum:e.enum.filter((t,r,o)=>o.findIndex(n=>n.value===t.value)===r),enums:e.enums.filter((t,r,o)=>o.findIndex(n=>n.name===t.name)===r),imports:e.imports.filter((t,r,o)=>z(t,r,o)&&t!==e.name).sort(xt)}),Lm=e=>{let t=b(),r=new Map;return e.forEach(o=>{(o.tags?.length&&(t.plugins["@hey-api/sdk"]?.asClass||j(t))?o.tags.filter(z):["Default"]).forEach(s=>{let i={...o,service:Kt(s)},a=r.get(i.service)||Bm(i);a.$refs=[...a.$refs,...i.$refs],a.imports=[...a.imports,...i.imports],a.operations=[...a.operations,i],r.set(i.service,a);});}),Array.from(r.values())},Hm=e=>{let t={...e};return t.operations=_m(t),t.operations.forEach(r=>{t.imports.push(...r.imports);}),t.imports=t.imports.filter(z).sort(xt),t},_m=e=>{let t=new Map;return e.operations.map(r=>{let o={...r};o.imports.push(...o.parameters.flatMap(a=>a.imports));let n=o.responses.filter(a=>a.responseTypes.includes("success"));o.imports.push(...n.flatMap(a=>a.imports));let s=o.name,i=t.get(s)||0;return i>0&&(o.name=`${s}${i}`),t.set(s,i+1),o})},Bm=e=>({$refs:[],imports:[],name:e.service,operations:[]}),Kt=e=>I({input:Be(e),pascalCase:!0});var Dr=({id:e,type:t})=>{let r="";switch(t){case"data":r="DataResponseTransformer";break;case"error":r="ErrorResponseTransformer";break;case"response":r="ResponseTransformer";break}return `${Rt}${I({input:e,pascalCase:!1})}${r}`},Vm=({$ref:e,type:t})=>{let r="";switch(t){case"response":r="SchemaResponseTransformer";break}let o=e.split("/");return `${o.slice(0,o.length-1).join("/")}/${I({input:o[o.length-1],pascalCase:!1})}${r}`},ji=({$ref:e})=>Vm({$ref:e,type:"response"}),Ni="transformers",Mr="data",Wt=e=>e.map(t=>y__default.default.isStatement(t)?t:p.expressionToStatement({expression:t})),wi=({context:e,schema:t})=>{let r=p.identifier({text:Mr}),o=Ut({context:e,dataExpression:r,schema:t});return o.length&&o.push(p.returnStatement({expression:r})),o},Ut=({context:e,dataExpression:t,schema:r})=>{let o=e.file({id:Ni});if(r.$ref){let n=o.identifier({$ref:ji({$ref:r.$ref}),create:!0,namespace:"value"});if(n.created&&n.name){let s=e.resolveIrRef(r.$ref),i=wi({context:e,schema:s});if(i.length){let a=p.constVariable({expression:p.arrowFunction({async:!1,multiLine:!0,parameters:[{name:Mr,type:p.keywordTypeNode({keyword:"any"})}],statements:Wt(i)}),name:n.name});o.add(a);}else n=o.blockIdentifier({$ref:ji({$ref:r.$ref}),namespace:"value"});}if(n.name){let s=p.callExpression({functionName:n.name,parameters:[t]});if(typeof t=="string")return [s];if(t)return [p.assignment({left:t,right:s})]}return []}if(r.type==="array"){let n=r.items?Ut({context:e,schema:{...r,type:void 0}}):[];return n.length?t&&typeof t!="string"?[p.assignment({left:t,right:p.callExpression({functionName:p.propertyAccessExpression({expression:t,name:"map"}),parameters:[p.arrowFunction({multiLine:!0,parameters:[{name:"item"}],statements:n.length===1?y__default.default.isStatement(n[0])?[]:[p.returnStatement({expression:n[0]})]:Wt(n)})]})})]:[]:[]}if(r.type==="object"){let n=[],s=r.required??[];for(let i in r.properties){let a=r.properties[i],c=p.propertyAccessExpression({expression:Mr,name:i}),m=Ut({context:e,dataExpression:c,schema:a});m.length&&(s.includes(i)?n=n.concat(m):n.push(p.ifStatement({expression:c,thenStatement:p.block({statements:Wt(m)})})));}return n}if(r.type==="string"&&(r.format==="date"||r.format==="date-time")){let n=p.identifier({text:"Date"});return typeof t=="string"?[p.newExpression({argumentsArray:[p.identifier({text:t})],expression:n})]:t?[p.assignment({left:t,right:p.newExpression({argumentsArray:[t],expression:n})})]:[]}if(r.items){if(r.items.length===1)return Ut({context:e,dataExpression:"item",schema:r.items[0]});let n=[];if(r.items.length===2&&r.items.find(s=>s.type==="null"||s.type==="void")){for(let s of r.items){let i=Ut({context:e,dataExpression:"item",schema:s});if(i.length){let a=p.identifier({text:"item"});i.push(p.ifStatement({expression:a,thenStatement:p.block({statements:i.length===1?y__default.default.isStatement(i[0])?[]:[p.returnStatement({expression:i[0]})]:Wt(i)})}),p.returnStatement({expression:a}));}}return n}console.warn(`\u2757\uFE0F Transformers warning: schema ${JSON.stringify(r)} is too complex and won't be currently processed. This will likely produce an incomplete transformer which is not what you want. Please open an issue if you'd like this improved https://github.com/hey-api/openapi-ts/issues`);}return []},$i=({context:e,plugin:t})=>{let r=e.createFile({id:Ni,path:t.output});e.subscribe("operation",({method:o,operation:n,path:s})=>{let{response:i}=vt(n);if(!i)return;if(i.items&&i.items.length>1){e.config.debug&&console.warn(`\u2757\uFE0F Transformers warning: route ${`${o.toUpperCase()} ${s}`} has ${i.items.length} non-void success responses. This is currently not handled and we will not generate a response transformer. Please open an issue if you'd like this feature https://github.com/hey-api/openapi-ts/issues`);return}let a=e.file({id:"types"}).identifier({$ref:q({id:n.id,type:"response"}),namespace:"type"});if(!a.name)return;let c=r.identifier({$ref:Dr({id:n.id,type:"response"}),create:!0,namespace:"value"});if(!c.name)return;let m=wi({context:e,schema:i});if(m.length){r.import({asType:!0,module:r.relativePathToFile({context:e,id:"types"}),name:a.name});let l=p.constVariable({exportConst:!0,expression:p.arrowFunction({async:!0,multiLine:!0,parameters:[{name:Mr,type:p.keywordTypeNode({keyword:"any"})}],returnType:p.typeReferenceNode({typeArguments:[p.typeReferenceNode({typeName:a.name})],typeName:"Promise"}),statements:Wt(m)}),name:c.name});r.add(l);}else c=r.blockIdentifier({$ref:Dr({id:n.id,type:"response"}),namespace:"value"});});};var at=({meta:e,onImport:t,...r})=>{if(!e)return {created:!1,name:""};let{created:o,name:n}=me({meta:e,...r});return n&&t(n),{created:o,name:n}},Hi=e=>`${e}ModelResponseTransformer`,pt=e=>`${I({input:e,pascalCase:!0})}Data`,kt=e=>`${I({input:e,pascalCase:!0})}Error`,Cn=e=>`${e}Transformer`,Se=e=>`${I({input:e,pascalCase:!0})}Response`,ze=({importedType:e,throwOnError:t})=>{let r=te();return t?`${r}<${e||"unknown"}, ${t}>`:e?`${r}<${e}>`:r},Di=(e,t)=>{let r=b(),{name:o}=me({client:e,meta:{$ref:t.name,name:t.name},nameTransformer:pt}),n=Pe(t.parameters);if(!P(r))return [{isRequired:n,name:"options",type:ze({importedType:o,throwOnError:"ThrowOnError"})}];if(!t.parameters.length)return [];let s=i=>{if(i.default!==void 0)return JSON.stringify(i.default,null,4)};return r.useOptions?[{default:n?void 0:{},name:"data",type:o}]:t.parameters.map(i=>{let a=`${o}['${i.name}']`;return {default:i?.default,isRequired:(!i.isRequired&&!s(i)?"?":"")=="",name:i.name,type:a}})},Mi=(e,t)=>{let r=b(),o=p.typeNode("void");if(t.responses.filter(s=>s.responseTypes.includes("success")).length){let{name:s}=me({client:e,meta:{$ref:t.name,name:t.name},nameTransformer:Se});o=p.typeUnionNode({types:[s]});}return r.useOptions&&r.plugins["@hey-api/sdk"]?.response==="response"&&(o=p.typeNode("ApiResult",[o])),r.client.name==="legacy/angular"?o=p.typeNode("Observable",[o]):o=p.typeNode("CancelablePromise",[o]),o},Fi=e=>{let t=b();if(!P(t))return [e.deprecated&&"@deprecated",e.summary&&A(e.summary),e.description&&A(e.description)];let r=[];e.parameters.length&&(t.useOptions?r=["@param data The data for the request.",...e.parameters.map(s=>`@param data.${s.name} ${s.description?A(s.description):""}`)]:r=e.parameters.map(s=>`@param ${s.name} ${s.description?A(s.description):""}`));let o=e.responses.filter(s=>s.responseTypes.includes("success"));return [e.deprecated&&"@deprecated",e.summary&&A(e.summary),e.description&&A(e.description),...r,...o.map(s=>`@returns ${s.type} ${s.description?A(s.description):""}`),"@throws ApiError"]},Km=(e,t,r,o)=>{let n=b(),s=Se(t.name),{name:i}=me({client:e,meta:{$ref:`transformers/${s}`,name:s},nameTransformer:Cn});if(i&&r(i),!P(n)){let l=[{spread:"options"}],f=t.parameters.filter(g=>g.in==="body"||g.in==="formData").map(g=>g.mediaType||(g.in==="formData"?"multipart/form-data":void 0)).filter(Boolean).filter(z);return f.length===1&&(f[0]==="multipart/form-data"&&(l=[...l,{spread:"formDataBodySerializer"},{key:"headers",value:[{key:"Content-Type",value:null},{spread:"options?.headers"}]}],o?.("formDataBodySerializer")),f[0]==="application/x-www-form-urlencoded"&&(l=[...l,{spread:"urlSearchParamsBodySerializer"},{key:"headers",value:[{key:"Content-Type",value:f[0]},{spread:"options?.headers"}]}],o?.("urlSearchParamsBodySerializer"))),l=[...l,{key:"url",value:t.path}],i&&(l=[...l,{key:"responseTransformer",value:i}]),p.objectExpression({identifiers:["responseTransformer"],obj:l})}let a=l=>l.reduce((u,f)=>{let g=f.prop,x=n.useOptions?`data.${f.name}`:f.name;return g===x?u[g]=g:se(g)===g?u[g]=x:u[`'${g}'`]=x,u},{}),c={method:t.method,url:t.path};t.parametersPath.length&&(c.path=a(t.parametersPath)),t.parametersCookie.length&&(c.cookies=a(t.parametersCookie)),t.parametersHeader.length&&(c.headers=a(t.parametersHeader)),t.parametersQuery.length&&(c.query=a(t.parametersQuery)),t.parametersForm.length&&(c.formData=a(t.parametersForm)),t.parametersBody&&(t.parametersBody.in==="formData"&&(n.useOptions?c.formData=`data.${t.parametersBody.name}`:c.formData=t.parametersBody.name),t.parametersBody.in==="body"&&(n.useOptions?c.body=`data.${t.parametersBody.name}`:c.body=t.parametersBody.name)),t.parametersBody?.mediaType&&(c.mediaType=t.parametersBody?.mediaType),t.responseHeader&&(c.responseHeader=t.responseHeader),i&&(c.responseTransformer=i);let m=t.responses.filter(l=>l.responseTypes.includes("error"));if(m.length>0){let l={};m.forEach(u=>{l[u.code]=u.description??"";}),c.errors=l;}return p.objectExpression({identifiers:["body","cookies","formData","headers","path","query","responseTransformer"],obj:c,shorthand:!0})},D=({config:e,handleIllegal:t,id:r,operation:o})=>e.plugins["@hey-api/sdk"]?.methodNameBuilder?e.plugins["@hey-api/sdk"].methodNameBuilder(o):t&&r.match(oe)?`${r}_`:r,Li=(e,t,r,o)=>{let n=b(),s=Km(e,t,r,o);if(!P(n)){let i=me({client:e,meta:{$ref:t.name,name:t.name},nameTransformer:kt}).name,c=t.responses.filter(m=>m.responseTypes.includes("success")).length?me({client:e,meta:{$ref:t.name,name:t.name},nameTransformer:Se}).name:"void";return [p.returnFunctionCall({args:[s],name:`(options?.client ?? client).${t.method.toLocaleLowerCase()}`,types:i&&c?[c,i,"ThrowOnError"]:i?["unknown",i,"ThrowOnError"]:c?[c,"unknown","ThrowOnError"]:[]})]}return j(n)?[p.returnFunctionCall({args:[s],name:"this.httpRequest.request"})]:n.client.name==="legacy/angular"?[p.returnFunctionCall({args:["OpenAPI","this.http",s],name:"__request"})]:[p.returnFunctionCall({args:["OpenAPI",s],name:"__request"})]},Wm=({client:e,onClientImport:t,onImport:r,onNode:o,service:n})=>{let s=b(),i=P(s);for(let l of n.operations)l.parameters.length&&at({client:e,meta:{$ref:l.name,name:l.name},nameTransformer:pt,onImport:r}),i||at({client:e,meta:{$ref:l.name,name:l.name},nameTransformer:kt,onImport:r}),l.responses.filter(f=>f.responseTypes.includes("success")).length&&at({client:e,meta:{$ref:l.name,name:l.name},nameTransformer:Se,onImport:r});let a={default:!1,extends:"boolean",name:"ThrowOnError"};if(!s.plugins["@hey-api/sdk"]?.asClass&&!j(s)){for(let l of n.operations){let u={parameters:Di(e,l),returnType:i?Mi(e,l):void 0,statements:Li(e,l,r,t),types:i?void 0:[a]},f=s.client.name==="legacy/angular"?p.anonymousFunction(u):p.arrowFunction(u),g=p.constVariable({comment:Fi(l),exportConst:!0,expression:f,name:D({config:s,handleIllegal:!0,id:l.name,operation:l})});o(g);}return}let c=n.operations.map(l=>p.methodDeclaration({accessLevel:"public",comment:Fi(l),isStatic:j(s)===void 0&&s.client.name!=="legacy/angular",name:D({config:s,id:l.name,operation:l}),parameters:Di(e,l),returnType:i?Mi(e,l):void 0,statements:Li(e,l,r,t),types:i?void 0:[a]}));if(!c.length)return;j(s)?c=[p.constructorDeclaration({multiLine:!1,parameters:[{accessLevel:"public",isReadOnly:!0,name:"httpRequest",type:"BaseHttpRequest"}]}),...c]:s.client.name==="legacy/angular"&&(c=[p.constructorDeclaration({multiLine:!1,parameters:[{accessLevel:"public",isReadOnly:!0,name:"http",type:"HttpClient"}]}),...c]);let m=p.classDeclaration({decorator:s.client.name==="legacy/angular"?{args:[{providedIn:"root"}],name:"Injectable"}:void 0,members:c,name:Te({config:s,name:n.name})});o(m);},_i=({client:e,files:t})=>{let r=b();if(!r.client.name)throw new Error("\u{1F6AB} client needs to be set to generate SDKs - which HTTP client do you want to use?");let o=P(r),n="sdk";if(t.sdk=new Z({dir:r.output.path,name:`${n}.ts`}),o?(r.client.name==="legacy/angular"?(t.sdk.import({module:"@angular/core",name:"Injectable"}),j(r)||t.sdk.import({module:"@angular/common/http",name:"HttpClient"}),t.sdk.import({asType:!0,module:"rxjs",name:"Observable"})):t.sdk.import({asType:!0,module:"./core/CancelablePromise",name:"CancelablePromise"}),r.plugins["@hey-api/sdk"]?.response==="response"&&t.sdk.import({asType:!0,module:"./core/ApiResult",name:"ApiResult"}),j(r)?t.sdk.import({asType:r.client.name!=="legacy/angular",module:"./core/BaseHttpRequest",name:"BaseHttpRequest"}):(t.sdk.import({module:"./core/OpenAPI",name:"OpenAPI"}),t.sdk.import({alias:"__request",module:"./core/request",name:"request"}))):(t.sdk.import({module:Q({config:r,sourceOutput:n}),name:"createClient"}),t.sdk.import({module:Q({config:r,sourceOutput:n}),name:"createConfig"}),t.sdk.import({asType:!0,module:Q({config:r,sourceOutput:n}),name:te()})),!o){let s=p.constVariable({exportConst:!0,expression:p.callExpression({functionName:"createClient",parameters:[p.callExpression({functionName:"createConfig"})]}),name:"client"});t.sdk.add(s);}for(let s of e.services)Wm({client:e,onClientImport:i=>{t.sdk.import({module:Q({config:r,sourceOutput:n}),name:i});},onImport:i=>{t.sdk.import({asType:!i.endsWith("Transformer"),module:`./${t.types.nameWithoutExtension()}`,name:i});},onNode:i=>{t.sdk.add(i);},service:s});};var q=({id:e,type:t})=>{let r="";switch(t){case"data":r="Data";break;case"error":r="Error";break;case"errors":r="Errors";break;case"response":r="Response";break;case"responses":r="Responses";break}return `${Rt}${I({input:e,pascalCase:!0})}${r}`},Fr="sdk",Bi=({context:e,operation:t,path:r})=>{let o=e.file({id:Fr}),n=o.nameWithoutExtension(),s=[{spread:"options"}];if(t.body){switch(t.body.type){case"form-data":s.push({spread:"formDataBodySerializer"}),o.import({module:Q({config:e.config,sourceOutput:n}),name:"formDataBodySerializer"});break;case"json":break;case"url-search-params":s.push({spread:"urlSearchParamsBodySerializer"}),o.import({module:Q({config:e.config,sourceOutput:n}),name:"urlSearchParamsBodySerializer"});break}s.push({key:"headers",value:[{key:"Content-Type",value:t.body.type==="form-data"?null:t.body.mediaType},{spread:"options?.headers"}]});}s.push({key:"url",value:r});let i=e.file({id:"transformers"});if(i){let a=i.identifier({$ref:Dr({id:t.id,type:"response"}),namespace:"value"});a.name&&(o.import({module:o.relativePathToFile({context:e,id:"transformers"}),name:a.name}),s.push({key:"responseTransformer",value:a.name}));}for(let a in t.parameters?.query){let c=t.parameters.query[a];if((c.schema.type==="array"||c.schema.type==="tuple")&&(c.style!=="form"||!c.explode)){e.config.client.name==="@hey-api/client-fetch"&&s.push({key:"querySerializer",value:[{key:"array",value:[{key:"explode",value:!1},{key:"style",value:"form"}]}]});break}}return p.objectExpression({identifiers:["responseTransformer"],obj:s})},Um=({context:e})=>{let t=e.file({id:Fr}),r=t.relativePathToFile({context:e,id:"types"}),o=new Map;e.subscribe("operation",({method:n,operation:s,path:i})=>{let a=e.file({id:"types"}).identifier({$ref:q({id:s.id,type:"data"}),namespace:"type"});a.name&&t.import({asType:!0,module:r,name:a.name});let c=e.file({id:"types"}).identifier({$ref:q({id:s.id,type:"error"}),namespace:"type"});c.name&&t.import({asType:!0,module:r,name:c.name});let m=e.file({id:"types"}).identifier({$ref:q({id:s.id,type:"response"}),namespace:"type"});m.name&&t.import({asType:!0,module:r,name:m.name});let l=p.methodDeclaration({accessLevel:"public",comment:[s.deprecated&&"@deprecated",s.summary&&A(s.summary),s.description&&A(s.description)],isStatic:!0,name:D({config:e.config,handleIllegal:!1,id:s.id,operation:s}),parameters:[{isRequired:Vt(s),name:"options",type:ze({importedType:a.name,throwOnError:"ThrowOnError"})}],returnType:void 0,statements:[p.returnFunctionCall({args:[Bi({context:e,operation:s,path:i})],name:`(options?.client ?? client).${n}`,types:[m.name||"unknown",c.name||"unknown","ThrowOnError"]})],types:[{default:!1,extends:"boolean",name:"ThrowOnError"}]}),u=Array.from(new Set(s.tags));u.length||u.push("default");for(let f of u){let g=Kt(f),x=o.get(g)??[];x.push(l),o.set(g,x);}}),e.subscribe("after",()=>{for(let[n,s]of o){let i=p.classDeclaration({decorator:void 0,members:s,name:Te({config:e.config,name:n})});t.add(i);}});},Qm=({context:e})=>{let t=e.file({id:Fr}),r=t.relativePathToFile({context:e,id:"types"});e.subscribe("operation",({method:o,operation:n,path:s})=>{let i=e.file({id:"types"}).identifier({$ref:q({id:n.id,type:"data"}),namespace:"type"});i.name&&t.import({asType:!0,module:r,name:i.name});let a=e.file({id:"types"}).identifier({$ref:q({id:n.id,type:"error"}),namespace:"type"});a.name&&t.import({asType:!0,module:r,name:a.name});let c=e.file({id:"types"}).identifier({$ref:q({id:n.id,type:"response"}),namespace:"type"});c.name&&t.import({asType:!0,module:r,name:c.name});let m=p.constVariable({comment:[n.deprecated&&"@deprecated",n.summary&&A(n.summary),n.description&&A(n.description)],exportConst:!0,expression:p.arrowFunction({parameters:[{isRequired:Vt(n),name:"options",type:ze({importedType:i.name,throwOnError:"ThrowOnError"})}],returnType:void 0,statements:[p.returnFunctionCall({args:[Bi({context:e,operation:n,path:s})],name:`(options?.client ?? client).${o}`,types:[c.name||"unknown",a.name||"unknown","ThrowOnError"]})],types:[{default:!1,extends:"boolean",name:"ThrowOnError"}]}),name:D({config:e.config,handleIllegal:!0,id:n.id,operation:n})});t.add(m);});},Vi=({context:e,plugin:t})=>{if(!e.config.client.name)throw new Error("\u{1F6AB} client needs to be set to generate SDKs - which HTTP client do you want to use?");let r=e.createFile({id:Fr,path:t.output}),o=r.nameWithoutExtension();r.import({module:Q({config:e.config,sourceOutput:o}),name:"createClient"}),r.import({module:Q({config:e.config,sourceOutput:o}),name:"createConfig"}),r.import({asType:!0,module:Q({config:e.config,sourceOutput:o}),name:te()});let n=p.constVariable({exportConst:!0,expression:p.callExpression({functionName:"createClient",parameters:[p.callExpression({functionName:"createConfig"})]}),name:"client"});r.add(n),e.config.plugins["@hey-api/sdk"]?.asClass?Um({context:e}):Qm({context:e});};var Pn={_dependencies:["@hey-api/typescript"],_handler:Vi,_handlerLegacy:_i,_optionalDependencies:["@hey-api/transformers"],asClass:!1,name:"@hey-api/sdk",operationId:!0,output:"sdk",response:"body",serviceNameBuilder:"{{name}}Service"};var Xm="$OpenApiTs",V={$refs:[],base:"",description:null,enum:[],enums:[],export:"interface",imports:[],in:"",isDefinition:!1,isNullable:!1,isReadOnly:!1,isRequired:!1,link:null,name:"",properties:[],template:null,type:""},zm=({comments:e,leadingComment:t,meta:r,obj:o,onNode:n,...s})=>{if(!r)return;let{created:i,name:a}=me({create:!0,meta:r,...s});if(i){let c=p.enumDeclaration({comments:e,leadingComment:t,name:a,obj:o});n(c);}},Fe=({comment:e,meta:t,onCreated:r,onNode:o,type:n,...s})=>{if(!t)return {created:!1,name:""};let i=me({create:!0,meta:t,...s}),{created:a,name:c}=i;if(a){let m=p.typeAliasDeclaration({comment:e,exportType:!0,name:c,type:n});o(m),r?.(c);}return i},Gm=e=>{let t=b(),r=[];Wi(e),e.model.enums.forEach(o=>t.plugins["@hey-api/typescript"]?.enums!=="typescript+namespace"?Ki({...e,model:o}):Jm({...e,model:o,onNode:n=>{r.push(n);}})),r.length&&e.onNode(p.namespaceDeclaration({name:e.model.name,statements:r}));},Ki=({client:e,model:t,onNode:r})=>{let o=b(),n={},s={};t.enum.forEach(a=>{let{key:c,value:m}=gr(a);n[c]=m;let l=a.customDescription||a.description;l&&(s[c]=[A(l)]);});let i=[t.description&&A(t.description),t.deprecated&&"@deprecated"];if(o.plugins["@hey-api/typescript"]?.enums==="typescript"||o.plugins["@hey-api/typescript"]?.enums==="typescript+namespace"){zm({client:e,comments:s,leadingComment:i,meta:t.meta,obj:n,onNode:r});return}Fe({client:e,comment:i,meta:t.meta,onCreated:a=>{if(o.plugins["@hey-api/typescript"]?.enums==="javascript"){let c=p.objectExpression({multiLine:!0,obj:Object.entries(n).map(([l,u])=>({comments:s[l],key:l,value:u})),unescape:!0}),m=p.constVariable({assertion:"const",comment:i,exportConst:!0,expression:c,name:a});r(m);}},onNode:r,type:Qs(t.enum)});},Jm=({model:e,onNode:t})=>{let r={},o={};e.enum.forEach(n=>{let{key:s,value:i}=gr(n);r[s]=i;let a=n.customDescription||n.description;a&&(o[s]=[A(a)]);}),t(p.enumDeclaration({comments:o,leadingComment:[e.description&&A(e.description),e.deprecated&&"@deprecated"],name:e.meta?.name||e.name,obj:r}));},Wi=({client:e,model:t,onNode:r})=>{Fe({client:e,comment:[t.description&&A(t.description),t.deprecated&&"@deprecated"],meta:t.meta,onNode:r,type:ce(t)});},Zm=e=>{switch(e.model.export){case"all-of":case"any-of":case"one-of":case"interface":return Gm(e);case"enum":return Ki(e);default:return Wi(e)}},Ym=({client:e,onNode:t})=>{let r={},o=b();if(!o.plugins["@hey-api/sdk"]&&!o.plugins["@hey-api/typescript"]?.tree)return;let n=P(o);for(let i of e.services)for(let a of i.operations){if(!a.parameters.length&&!a.responses.length)continue;r[a.path]||(r[a.path]={});let c=r[a.path];c[a.method]||(c[a.method]={});let m=c[a.method];if(m.$ref=a.name,a.responses.length>0){if(m.res||(m.res={}),Array.isArray(m.res))continue;a.responses.forEach(u=>{m.res[u.code]=u;});}if(a.parameters.length>0){let u={mediaType:null,...V,in:"body",name:"body",prop:"body"},f=a.parameters.filter(O=>O.in==="body");f.length||(f=a.parameters.filter(O=>O.in==="formData")),f.length===1?u={...V,...f[0],in:"body",isRequired:f[0].isRequired,name:"body",prop:"body"}:f.length>1&&(u={...V,in:"body",isRequired:f.some(O=>O.isRequired),mediaType:"multipart/form-data",name:"body",prop:"body",properties:f});let g={...V,in:"header",isRequired:Pe(a.parameters.filter(O=>O.in==="header")),mediaType:null,name:n?"header":"headers",prop:n?"header":"headers",properties:a.parameters.filter(O=>O.in==="header").sort(Ft)},x={...V,in:"path",isRequired:Pe(a.parameters.filter(O=>O.in==="path")),mediaType:null,name:"path",prop:"path",properties:a.parameters.filter(O=>O.in==="path").sort(Ft)},h={...V,in:"query",isRequired:Pe(a.parameters.filter(O=>O.in==="query")),mediaType:null,name:"query",prop:"query",properties:a.parameters.filter(O=>O.in==="query").sort(Ft)},C=n?Lt([...a.parameters]):[u,g,x,h].filter(O=>O.properties.length||O.$refs.length||O.mediaType);m.req=C,Fe({client:e,meta:{$ref:a.name,name:a.name},nameTransformer:pt,onNode:t,type:ce({...V,isRequired:!0,properties:C})});}let l=a.responses.filter(u=>u.responseTypes.includes("success"));if(l.length>0){Fe({client:e,meta:{$ref:a.name,name:a.name},nameTransformer:Se,onNode:t,type:ce({...V,export:"any-of",isRequired:!0,properties:l})});let u=a.responses.filter(f=>f.responseTypes.includes("error"));n||Fe({client:e,meta:{$ref:a.name,name:a.name},nameTransformer:kt,onNode:t,type:ce(u.length?{...V,export:"one-of",isRequired:!0,properties:u}:{...V,base:"unknown",isRequired:!0,type:"unknown"})});}}let s=Object.entries(r).map(([i,a])=>{let c=Object.entries(a).map(([l,u])=>{let f=l,g=[];if(u.req){let h=u.$ref,{name:C}=me({client:e,meta:{$ref:h,name:h},nameTransformer:pt}),O={...V,base:C,export:"reference",isRequired:!0,name:"req",properties:[],type:C};g=[...g,O];}if(u.res){let h=Object.entries(u.res).map(([O,le])=>({...V,...le,isRequired:!0,name:String(O)})),C={...V,isRequired:!0,name:"res",properties:h};g=[...g,C];}return {...V,isRequired:!0,name:f.toLocaleLowerCase(),properties:g}}).filter(Boolean);return {...V,isRequired:!0,name:`'${i}'`,properties:c}});o.plugins["@hey-api/typescript"]?.tree&&Fe({client:e,meta:{$ref:"@hey-api/openapi-ts",name:Xm},onNode:t,type:ce({...V,properties:s})});},Ui=({client:e,files:t})=>{let r=b();t.types=new Z({dir:r.output.path,name:"types.ts"});let o=n=>{t.types?.add(n);};for(let n of e.models)Zm({client:e,model:n,onNode:o});Ym({client:e,onNode:o});};var Ge="data",el=e=>e.base==="unknown"&&e.export==="generic"&&e.type==="unknown",Qi=({client:e,model:t})=>t.$refs.map(o=>{let n=e.models.find(s=>s.meta?.$ref===o);if(!n)throw new Error(`Ref ${o} could not be found. Transformers cannot be generated without having access to all refs.`);return n}),Xi=e=>{let t=e.model.meta.name,{name:r}=Fe({...e,meta:{$ref:`transformers/${t}`,name:t},nameTransformer:Hi,onCreated:n=>{let s=Lr({...e,meta:{$ref:`transformers/${t}`,name:n},path:[Ge]});zi({...e,async:!1,name:n,statements:s});},type:`(${Ge}: any) => ${t}`});return {created:!!e.client.types[r],name:r}},tl=e=>{let{model:t}=e,r=Qi(e);if(r.length===1){let{created:o,name:n}=Xi({...e,model:r[0]});return o?[p.transformArrayMutation({path:e.path,transformerName:n})]:[]}return Bt(t)||t.link&&!Array.isArray(t.link)&&t.link.export==="any-of"&&t.link.properties.find(o=>Bt(o))?[p.transformArrayMap({path:e.path,transformExpression:p.conditionalExpression({condition:p.identifier({text:"item"}),whenFalse:p.identifier({text:"item"}),whenTrue:p.transformNewDate({parameterName:"item"})})})]:[]},rl=e=>{let{model:t}=e,r=[...e.path,t.name];return t.type==="string"&&t.export!=="array"&&Bt(t)?[p.transformDateMutation({path:r})]:Lr({...e,model:t,path:r})},Lr=e=>{let{model:t}=e;switch(t.export){case"array":return tl(e);case"interface":return t.properties.flatMap(r=>rl({...e,model:r}));case"reference":{if(t.$refs.length!==1)return [];let r=Qi(e),{created:o,name:n}=Xi({...e,model:r[0]});return o?t.in==="response"?[p.expressionToStatement({expression:p.callExpression({functionName:n,parameters:[Ge]})})]:p.transformFunctionMutation({path:e.path,transformerName:n}):[]}default:return []}},zi=({async:e,client:t,name:r,onNode:o,onRemoveNode:n,statements:s})=>{let i={created:!1,name:r};if(!s.length)return Gs({client:t,name:r}),n?.(),i;let a=p.arrowFunction({async:e,multiLine:!0,parameters:[{name:Ge}],statements:[...s,p.returnVariable({expression:Ge})]}),c=p.constVariable({exportConst:!0,expression:a,name:r,typeName:r});return o(c),{created:!0,name:r}},Gi=({client:e,files:t})=>{let r=b(),o=s=>{t.types?.add(s);},n=()=>{t.types?.removeNode();};for(let s of e.services)for(let i of s.operations){let a=i.responses.filter(l=>l.responseTypes.includes("success"));if(!a.length)continue;let c=a.filter(l=>!el(l));if(!c.length)continue;if(c.length>1){r.debug&&console.warn(`\u2757\uFE0F Transformers warning: route ${Ue(i)} has ${c.length} non-void success responses. This is currently not handled and we will not generate a response transformer. Please open an issue if you'd like this feature https://github.com/hey-api/openapi-ts/issues`);continue}let m=Se(i.name);Fe({client:e,meta:{$ref:`transformers/${m}`,name:m},nameTransformer:Cn,onCreated:l=>{let u=a.length>1?a.flatMap(f=>{let g=Lr({client:e,meta:{$ref:`transformers/${m}`,name:m},model:f,onNode:o,onRemoveNode:n,path:[Ge]});return g.length?[p.ifStatement({expression:p.safeAccessExpression(["data"]),thenStatement:p.block({statements:g})})]:[]}):Lr({client:e,meta:{$ref:`transformers/${m}`,name:m},model:a[0],onNode:o,onRemoveNode:n,path:[Ge]});zi({async:!0,client:e,name:l,onNode:o,onRemoveNode:n,statements:u});},onNode:o,type:`(${Ge}: any) => Promise<${m}>`});}};var Sn={_dependencies:["@hey-api/typescript"],_handler:$i,_handlerLegacy:Gi,dates:!1,name:"@hey-api/transformers",output:"transformers"};var M="types",Ji=/^\d+$/,qt=({schema:e})=>[e.description&&A(e.description),e.deprecated&&"@deprecated"],nl=({$ref:e,context:t,schema:r})=>{let o=t.file({id:M}).identifier({$ref:e,create:!0,namespace:"value"});if(!o.created)return;let n=ea({schema:r}),s=p.objectExpression({multiLine:!0,obj:n.obj});return p.constVariable({assertion:"const",comment:qt({schema:r}),exportConst:!0,expression:s,name:o.name||""})},ea=({schema:e})=>{let t=[];return {obj:(e.items??[]).map(o=>{let n=typeof o.const;t.includes(n)||t.push(n);let s;return o.title?s=o.title:n==="number"?s=`_${o.const}`:n==="boolean"?s=(n?"true":"false").toLocaleUpperCase():typeof o.const=="string"&&(s=o.const.replace(/(\p{Lowercase})(\p{Uppercase}+)/gu,"$1_$2"),s=s.toLocaleUpperCase()),{comments:qt({schema:o}),key:s,value:o.const}}),typeofItems:t}},In=({$ref:e,context:t,plugin:r,schema:o})=>{let n=t.file({id:M}).identifier({$ref:e,create:!0,namespace:"type"});return !n.created&&!Tt(e)&&r.enums!=="typescript+namespace"?void 0:p.typeAliasDeclaration({comment:qt({schema:o}),exportType:!0,name:n.name||"",type:G({context:t,plugin:r,schema:{...o,type:void 0}})})},Zi=({$ref:e,context:t,plugin:r,schema:o})=>{let n=t.file({id:M}).identifier({$ref:e,create:!0,namespace:"value"});if(!n.created&&r.enums!=="typescript+namespace")return;let s=ea({schema:o});return s.typeofItems.filter(a=>a!=="number"&&a!=="string").length?In({$ref:e,context:t,plugin:r,schema:o}):p.enumDeclaration({leadingComment:qt({schema:o}),name:n.name||"",obj:s.obj})},ol=({context:e,namespace:t,plugin:r,schema:o})=>{if(!o.items)return p.typeArrayNode(p.keywordTypeNode({keyword:"unknown"}));o=Me({schema:o});let n=o.items.map(s=>G({context:e,namespace:t,plugin:r,schema:s}));return n.length===1?p.typeArrayNode(n[0]):o.logicalOperator==="and"?p.typeArrayNode(p.typeIntersectionNode({types:n})):p.typeArrayNode(p.typeUnionNode({types:n}))},sl=({schema:e})=>e.const!==void 0?p.literalTypeNode({literal:p.ots.boolean(e.const)}):p.keywordTypeNode({keyword:"boolean"}),il=({$ref:e,context:t,namespace:r,plugin:o,schema:n})=>{let s=e?Tt(e):!1,i=s||!!o.exportInlineEnums;if(e&&i){if(!o.enums){let c=In({$ref:e,context:t,plugin:o,schema:n});c&&t.file({id:M}).add(c);}if(o.enums==="javascript"){let c=In({$ref:e,context:t,plugin:o,schema:n});c&&t.file({id:M}).add(c);let m=nl({$ref:e,context:t,schema:n});m&&t.file({id:M}).add(m);}if(o.enums==="typescript"){let c=Zi({$ref:e,context:t,plugin:o,schema:n});c&&t.file({id:M}).add(c);}if(o.enums==="typescript+namespace"){let c=Zi({$ref:e,context:t,plugin:o,schema:n});c&&(s?t.file({id:M}).add(c):r.push(c));}}return G({context:t,plugin:o,schema:{...n,type:void 0}})},al=({schema:e})=>e.const!==void 0?p.literalTypeNode({literal:p.ots.number(e.const)}):p.keywordTypeNode({keyword:"number"}),pl=({context:e,namespace:t,plugin:r,schema:o})=>{let n,s=[],i=[],a=o.required??[],c=!1;for(let m in o.properties){let l=o.properties[m],u=a.includes(m);Ji.lastIndex=0,s.push({comment:qt({schema:l}),isReadOnly:l.accessScope==="read",isRequired:u,name:Ji.test(m)?y__default.default.factory.createNumericLiteral(m):m,type:G({$ref:`${Rt}${m}`,context:e,namespace:t,plugin:r,schema:l})}),i.push(l),u||(c=!0);}return o.additionalProperties&&(o.additionalProperties.type!=="never"||!i.length)&&(o.additionalProperties.type==="never"?i=[o.additionalProperties]:i.unshift(o.additionalProperties),c&&i.push({type:"undefined"}),n={isRequired:!0,name:"key",type:G({context:e,namespace:t,plugin:r,schema:i.length===1?i[0]:{items:i,logicalOperator:"or"}})}),p.typeInterfaceNode({indexProperty:n,properties:s,useLegacyResolution:!1})},cl=({context:e,schema:t})=>{if(t.const!==void 0)return p.literalTypeNode({literal:p.stringLiteral({text:t.const})});if(t.format){if(t.format==="binary")return p.typeUnionNode({types:[p.typeReferenceNode({typeName:"Blob"}),p.typeReferenceNode({typeName:"File"})]});if((t.format==="date-time"||t.format==="date")&&e.config.plugins["@hey-api/transformers"]?.dates)return p.typeReferenceNode({typeName:"Date"})}return p.keywordTypeNode({keyword:"string"})},ml=({context:e,namespace:t,plugin:r,schema:o})=>{let n=[];for(let s of o.items??[])n.push(G({context:e,namespace:t,plugin:r,schema:s}));return p.typeTupleNode({types:n})},Yi=({$ref:e,context:t,namespace:r,plugin:o,schema:n})=>{switch(n.type){case"array":return ol({context:t,namespace:r,plugin:o,schema:n});case"boolean":return sl({context:t,namespace:r,schema:n});case"enum":return il({$ref:e,context:t,namespace:r,plugin:o,schema:n});case"never":return p.keywordTypeNode({keyword:"never"});case"null":return p.literalTypeNode({literal:p.null()});case"number":return al({context:t,namespace:r,schema:n});case"object":return pl({context:t,namespace:r,plugin:o,schema:n});case"string":return cl({context:t,namespace:r,schema:n});case"tuple":return ml({context:t,namespace:r,plugin:o,schema:n});case"undefined":return p.keywordTypeNode({keyword:"undefined"});case"unknown":return p.keywordTypeNode({keyword:"unknown"});case"void":return p.keywordTypeNode({keyword:"void"})}},An=({parameters:e})=>{let t={type:"object"};if(e){let r={},o=[];for(let n in e){let s=e[n];r[n]=Me({schema:s.schema}),s.required&&o.push(n);}t.properties=r,o.length&&(t.required=o);}return t},ll=({context:e,operation:t,plugin:r})=>{let o={type:"object"},n=[],s=!1;if(o.properties||(o.properties={}),t.body?(s=!0,o.properties.body=t.body.schema,t.body.required&&n.push("body")):o.properties.body={type:"never"},t.parameters&&(t.parameters.header&&(s=!0,o.properties.headers=An({parameters:t.parameters.header}),o.properties.headers.required&&n.push("headers")),t.parameters.path?(s=!0,o.properties.path=An({parameters:t.parameters.path}),o.properties.path.required&&n.push("path")):o.properties.path={type:"never"},t.parameters.query?(s=!0,o.properties.query=An({parameters:t.parameters.query}),o.properties.query.required&&n.push("query")):o.properties.query={type:"never"}),o.required=n,s){let i=e.file({id:M}).identifier({$ref:q({id:t.id,type:"data"}),create:!0,namespace:"type"}),a=p.typeAliasDeclaration({exportType:!0,name:i.name||"",type:G({context:e,plugin:r,schema:o})});e.file({id:M}).add(a);}},ul=({context:e,operation:t,plugin:r})=>{ll({context:e,operation:t,plugin:r});let o=e.file({id:M}),{error:n,errors:s,response:i,responses:a}=vt(t);if(s){let c=o.identifier({$ref:q({id:t.id,type:"errors"}),create:!0,namespace:"type"});if(c.name){let m=p.typeAliasDeclaration({exportType:!0,name:c.name,type:G({context:e,plugin:r,schema:s})});if(o.add(m),n){let l=o.identifier({$ref:q({id:t.id,type:"error"}),create:!0,namespace:"type"});if(l.name){let u=p.typeReferenceNode({typeName:c.name}),f=y__default.default.factory.createTypeOperatorNode(y__default.default.SyntaxKind.KeyOfKeyword,u),g=p.typeAliasDeclaration({exportType:!0,name:l.name,type:p.indexedAccessTypeNode({indexType:f,objectType:u})});o.add(g);}}}}if(a){let c=o.identifier({$ref:q({id:t.id,type:"responses"}),create:!0,namespace:"type"});if(c.name){let m=p.typeAliasDeclaration({exportType:!0,name:c.name,type:G({context:e,plugin:r,schema:a})});if(o.add(m),i){let l=o.identifier({$ref:q({id:t.id,type:"response"}),create:!0,namespace:"type"});if(l.name){let u=p.typeReferenceNode({typeName:c.name}),f=y__default.default.factory.createTypeOperatorNode(y__default.default.SyntaxKind.KeyOfKeyword,u),g=p.typeAliasDeclaration({exportType:!0,name:l.name,type:p.indexedAccessTypeNode({indexType:f,objectType:u})});o.add(g);}}}}},G=({$ref:e,context:t,namespace:r=[],plugin:o,schema:n})=>{let s;if(n.$ref){let i=t.file({id:M}).identifier({$ref:n.$ref,create:!0,namespace:"type"});s=p.typeReferenceNode({typeName:i.name||""});}else if(n.type)s=Yi({$ref:e,context:t,namespace:r,plugin:o,schema:n});else if(n.items)if(n=Me({schema:n}),n.items){let i=n.items.map(a=>G({context:t,namespace:r,plugin:o,schema:a}));s=n.logicalOperator==="and"?p.typeIntersectionNode({types:i}):p.typeUnionNode({types:i});}else s=G({context:t,namespace:r,plugin:o,schema:n});else s=Yi({context:t,namespace:r,plugin:o,schema:{type:"unknown"}});if(e&&Tt(e)){if(r.length){let i=t.file({id:M}).identifier({$ref:e,create:!0,namespace:"value"}),a=p.namespaceDeclaration({name:i.name||"",statements:r});t.file({id:M}).add(a);}if(n.type!=="enum"){let i=t.file({id:M}).identifier({$ref:e,create:!0,namespace:"type"}),a=p.typeAliasDeclaration({comment:qt({schema:n}),exportType:!0,name:i.name||"",type:s});t.file({id:M}).add(a);}}return s},ta=({context:e,plugin:t})=>{e.createFile({id:M,path:t.output}),e.subscribe("schema",({$ref:r,schema:o})=>{G({$ref:r,context:e,plugin:t,schema:o});}),e.subscribe("parameter",({$ref:r,parameter:o})=>{G({$ref:r,context:e,plugin:t,schema:o.schema});}),e.subscribe("operation",({operation:r})=>{ul({context:e,operation:r,plugin:t});});};var En={_handler:ta,_handlerLegacy:Ui,enums:!1,exportInlineEnums:!1,name:"@hey-api/typescript",output:"types",style:"preserve",tree:!1};var fl=({context:e,operation:t})=>`${D({config:e.config,id:t.id,operation:t})}InfiniteOptions`,dl=({context:e,operation:t})=>`${D({config:e.config,id:t.id,operation:t})}Mutation`,yl=({context:e,operation:t})=>`${D({config:e.config,id:t.id,operation:t})}Options`,Hr=({context:e,isInfinite:t,operation:r})=>`${D({config:e.config,id:r.id,operation:r})}${t?"Infinite":""}QueryKey`,gl="createInfiniteParams",ma="createQueryKey",ra="infiniteQueryOptions",na="mutationOptions",qn="QueryKey",oa="queryOptions",zt="TOptions",kn=()=>b().client.name==="@hey-api/client-axios"?"baseURL":"baseUrl",hl=({file:e})=>{let t=p.constVariable({expression:p.arrowFunction({multiLine:!0,parameters:[{name:"queryKey",type:p.typeReferenceNode({typeName:"QueryKey<Options>"})},{name:"page",type:p.typeReferenceNode({typeName:"K"})}],statements:[p.constVariable({expression:p.identifier({text:"queryKey[0]"}),name:"params"}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"page"}),name:p.identifier({text:"body"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"body"}),right:p.objectExpression({multiLine:!0,obj:[{assertion:"any",spread:"queryKey[0].body"},{assertion:"any",spread:"page.body"}]})})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"page"}),name:p.identifier({text:"headers"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"headers"}),right:p.objectExpression({multiLine:!0,obj:[{spread:"queryKey[0].headers"},{spread:"page.headers"}]})})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"page"}),name:p.identifier({text:"path"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"path"}),right:p.objectExpression({multiLine:!0,obj:[{spread:"queryKey[0].path"},{spread:"page.path"}]})})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"page"}),name:p.identifier({text:"query"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"query"}),right:p.objectExpression({multiLine:!0,obj:[{spread:"queryKey[0].query"},{spread:"page.query"}]})})})]})}),p.returnVariable({expression:y__default.default.factory.createAsExpression(y__default.default.factory.createAsExpression(p.identifier({text:"params"}),y__default.default.factory.createKeywordTypeNode(y__default.default.SyntaxKind.UnknownKeyword)),y__default.default.factory.createTypeQueryNode(p.identifier({text:"page"})))})],types:[{extends:p.typeReferenceNode({typeName:p.identifier({text:"Pick<QueryKey<Options>[0], 'body' | 'headers' | 'path' | 'query'>"})}),name:"K"}]}),name:gl});e.add(t);},sa=({file:e})=>{let t=p.indexedAccessTypeNode({indexType:p.literalTypeNode({literal:p.ots.number(0)}),objectType:p.typeReferenceNode({typeArguments:[p.typeReferenceNode({typeName:zt})],typeName:qn})}),r=p.identifier({text:"infinite"}),o=p.constVariable({expression:p.arrowFunction({multiLine:!0,parameters:[{name:"id",type:p.typeReferenceNode({typeName:"string"})},{isRequired:!1,name:"options",type:p.typeReferenceNode({typeName:zt})},{isRequired:!1,name:"infinite",type:p.typeReferenceNode({typeName:"boolean"})}],returnType:t,statements:[p.constVariable({assertion:t,expression:p.objectExpression({multiLine:!1,obj:[{key:"_id",value:p.identifier({text:"id"})},{key:kn(),value:p.identifier({text:`(options?.client ?? client).getConfig().${kn()}`})}]}),name:"params",typeName:t}),p.ifStatement({expression:r,thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"_infinite"}),right:r})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"options"}),isOptional:!0,name:p.identifier({text:"body"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"body"}),right:p.propertyAccessExpression({expression:"options",name:"body"})})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"options"}),isOptional:!0,name:p.identifier({text:"headers"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"headers"}),right:p.propertyAccessExpression({expression:"options",name:"headers"})})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"options"}),isOptional:!0,name:p.identifier({text:"path"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"path"}),right:p.propertyAccessExpression({expression:"options",name:"path"})})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"options"}),isOptional:!0,name:p.identifier({text:"query"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"query"}),right:p.propertyAccessExpression({expression:"options",name:"query"})})})]})}),p.returnVariable({expression:"params"})],types:[{extends:p.typeReferenceNode({typeName:p.identifier({text:te()})}),name:zt}]}),name:ma});e.add(o);},ia=({file:e})=>{let t=[{name:"_id",type:p.keywordTypeNode({keyword:"string"})},{isRequired:!1,name:"_infinite",type:p.keywordTypeNode({keyword:"boolean"})}],r=p.typeAliasDeclaration({name:qn,type:p.typeTupleNode({types:[p.typeIntersectionNode({types:[p.typeReferenceNode({typeName:`Pick<${zt}, '${kn()}' | 'body' | 'headers' | 'path' | 'query'>`}),p.typeInterfaceNode({properties:t,useLegacyResolution:!0})]})]}),typeParameters:[{extends:p.typeReferenceNode({typeName:p.identifier({text:te()})}),name:zt}]});e.add(r);},aa=({id:e,isInfinite:t})=>p.arrayLiteralExpression({elements:[p.callExpression({functionName:ma,parameters:[p.ots.string(e),"options",t?p.ots.boolean(!0):void 0]})],multiLine:!1}),vn=({context:e,operation:t,plugin:r})=>{let o=e.file({id:"types"}).identifier({$ref:q({id:t.id,type:"data"}),namespace:"type"});return o.name&&e.file({id:r.name}).import({asType:!0,module:e.file({id:r.name}).relativePathToFile({context:e,id:"types"}),name:o.name}),ze({importedType:o.name})},pa=({context:e,operation:t,plugin:r})=>{let o=e.file({id:r.name}),n=e.file({id:"types"}).identifier({$ref:q({id:t.id,type:"error"}),namespace:"type"});n.name&&o.import({asType:!0,module:e.file({id:r.name}).relativePathToFile({context:e,id:"types"}),name:n.name});let s={asType:!0,name:n.name||""};if(s.name||(s=o.import({asType:!0,module:r.name,name:"DefaultError"})),e.config.client.name==="@hey-api/client-axios"){let i=o.import({asType:!0,module:"axios",name:"AxiosError"});s={...i,name:`${i.name}<${s.name}>`};}return s},ca=({context:e,operation:t,plugin:r})=>{let o=e.file({id:"types"}).identifier({$ref:q({id:t.id,type:"response"}),namespace:"type"});return o.name&&e.file({id:r.name}).import({asType:!0,module:e.file({id:r.name}).relativePathToFile({context:e,id:"types"}),name:o.name}),o.name||"unknown"},Ae=({context:e,plugin:t})=>{let r=e.createFile({id:t.name,path:t.output});r.import({asType:!0,module:Q({config:e.config,sourceOutput:t.output}),name:te()});let o=t.name==="@tanstack/angular-query-experimental"||t.name==="@tanstack/svelte-query"||t.name==="@tanstack/solid-query"?"MutationOptions":"UseMutationOptions",n,s=!1,i=!1,a=!1,c=!1,m=!1;e.subscribe("operation",({method:l,operation:u})=>{let f=[e.config.plugins["@hey-api/sdk"]?.asClass&&Te({config:e.config,name:Kt(u.tags?.[0]||"default")}),D({config:e.config,handleIllegal:!e.config.plugins["@hey-api/sdk"]?.asClass,id:u.id,operation:u})].filter(Boolean).join("."),g=!1,x=Vt(u);if(t.queryOptions&&["get","post"].includes(l)){m||(m=!0,i||(ia({file:r}),sa({file:r}),i=!0),r.import({module:t.name,name:oa})),g=!0;let h=vn({context:e,operation:u,plugin:t}),C=p.constVariable({exportConst:!0,expression:p.arrowFunction({parameters:[{isRequired:x,name:"options",type:h}],statements:aa({id:u.id})}),name:Hr({context:e,operation:u})});r.add(C);let O=p.constVariable({comment:[],exportConst:!0,expression:p.arrowFunction({parameters:[{isRequired:x,name:"options",type:h}],statements:[p.returnFunctionCall({args:[p.objectExpression({obj:[{key:"queryFn",value:p.arrowFunction({async:!0,multiLine:!0,parameters:[{destructure:[{name:"queryKey"},{name:"signal"}]}],statements:[p.constVariable({destructure:!0,expression:p.awaitExpression({expression:p.callExpression({functionName:f,parameters:[p.objectExpression({multiLine:!0,obj:[{spread:"options"},{spread:"queryKey[0]"},{key:"signal",shorthand:!0,value:p.identifier({text:"signal"})},{key:"throwOnError",value:!0}]})]})}),name:"data"}),p.returnVariable({expression:"data"})]})},{key:"queryKey",value:p.callExpression({functionName:Hr({context:e,operation:u}),parameters:["options"]})}]})],name:oa})]}),name:yl({context:e,operation:u})});r.add(O);}if(t.infiniteQueryOptions&&["get","post"].includes(l)){let h=ki({context:e,operation:u});if(h){a||(a=!0,i||(ia({file:r}),sa({file:r}),i=!0),s||(hl({file:r}),s=!0),r.import({module:t.name,name:ra}),n=r.import({asType:!0,module:t.name,name:"InfiniteData"})),g=!0;let C=vn({context:e,operation:u,plugin:t}),O=pa({context:e,operation:u,plugin:t}),le=ca({context:e,operation:u,plugin:t}),Ee=`${qn}<${C}>`,K=`Pick<${Ee}[0], 'body' | 'headers' | 'path' | 'query'>`,ve=`${ge({node:G({context:e,plugin:e.config.plugins["@hey-api/typescript"],schema:h.schema}),unescape:!0})} | ${K}`,L=p.constVariable({exportConst:!0,expression:p.arrowFunction({parameters:[{isRequired:x,name:"options",type:C}],returnType:Ee,statements:aa({id:u.id,isInfinite:!0})}),name:Hr({context:e,isInfinite:!0,operation:u})});r.add(L);let ne=p.constVariable({comment:[],exportConst:!0,expression:p.arrowFunction({parameters:[{isRequired:x,name:"options",type:C}],statements:[p.returnFunctionCall({args:[p.objectExpression({comments:[{jsdoc:!1,lines:["@ts-ignore"]}],obj:[{key:"queryFn",value:p.arrowFunction({async:!0,multiLine:!0,parameters:[{destructure:[{name:"pageParam"},{name:"queryKey"},{name:"signal"}]}],statements:[p.constVariable({comment:[{jsdoc:!1,lines:["@ts-ignore"]}],expression:p.conditionalExpression({condition:p.binaryExpression({left:p.typeOfExpression({text:"pageParam"}),operator:"===",right:p.ots.string("object")}),whenFalse:p.objectExpression({multiLine:!0,obj:[{key:h.in,value:p.objectExpression({multiLine:!0,obj:[{key:h.name,value:p.identifier({text:"pageParam"})}]})}]}),whenTrue:p.identifier({text:"pageParam"})}),name:"page",typeName:K}),p.constVariable({expression:p.callExpression({functionName:"createInfiniteParams",parameters:["queryKey","page"]}),name:"params"}),p.constVariable({destructure:!0,expression:p.awaitExpression({expression:p.callExpression({functionName:f,parameters:[p.objectExpression({multiLine:!0,obj:[{spread:"options"},{spread:"params"},{key:"signal",shorthand:!0,value:p.identifier({text:"signal"})},{key:"throwOnError",value:!0}]})]})}),name:"data"}),p.returnVariable({expression:"data"})]})},{key:"queryKey",value:p.callExpression({functionName:Hr({context:e,isInfinite:!0,operation:u}),parameters:["options"]})}]})],name:ra,types:[le,O.name,`${typeof n=="string"?n:n.name}<${le}>`,Ee,ve]})]}),name:fl({context:e,operation:u})});r.add(ne);}}if(t.mutationOptions&&["delete","patch","post","put"].includes(l)){c||(c=!0,r.import({asType:!0,module:t.name,name:o})),g=!0;let h=vn({context:e,operation:u,plugin:t}),C=pa({context:e,operation:u,plugin:t}),O=ca({context:e,operation:u,plugin:t}),le=p.arrowFunction({parameters:[{isRequired:!1,name:"options",type:`Partial<${h}>`}],statements:[p.constVariable({expression:p.objectExpression({obj:[{key:"mutationFn",value:p.arrowFunction({async:!0,multiLine:!0,parameters:[{name:"localOptions"}],statements:[p.constVariable({destructure:!0,expression:p.awaitExpression({expression:p.callExpression({functionName:f,parameters:[p.objectExpression({multiLine:!0,obj:[{spread:"options"},{spread:"localOptions"},{key:"throwOnError",value:!0}]})]})}),name:"data"}),p.returnVariable({expression:"data"})]})}]}),name:na,typeName:`${o}<${O}, ${C.name}, ${h}>`}),p.returnVariable({expression:na})]}),Ee=p.constVariable({comment:[],exportConst:!0,expression:le,name:dl({context:e,operation:u})});r.add(Ee);}(m||a)&&r.import({module:e.file({id:t.name}).relativePathToFile({context:e,id:"sdk"}),name:"client"}),g&&r.import({module:e.file({id:t.name}).relativePathToFile({context:e,id:"sdk"}),name:f.split(".")[0]});});};var xl=e=>`${D({config:b(),id:e.name,operation:e})}InfiniteOptions`,bl=e=>`${D({config:b(),id:e.name,operation:e})}Mutation`,Ol=({config:e,id:t,operation:r})=>`${D({config:e,id:t,operation:r})}Options`,_r=({config:e,id:t,isInfinite:r,operation:o})=>`${D({config:e,id:t,operation:o})}${r?"Infinite":""}QueryKey`,Rl=e=>{switch(e.in){case"formData":return "body";case"header":return "headers";default:return e.in}},Tl="createInfiniteParams",ba="createQueryKey",la="infiniteQueryOptions",ua="mutationOptions",wn="QueryKey",fa="queryOptions",Jt="TOptions",Nn=()=>b().client.name==="@hey-api/client-axios"?"baseURL":"baseUrl",Cl=({file:e})=>{let t=p.constVariable({expression:p.arrowFunction({multiLine:!0,parameters:[{name:"queryKey",type:p.typeNode("QueryKey<Options>")},{name:"page",type:p.typeNode("K")}],statements:[p.constVariable({expression:p.identifier({text:"queryKey[0]"}),name:"params"}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"page"}),name:p.identifier({text:"body"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"body"}),right:p.objectExpression({multiLine:!0,obj:[{assertion:"any",spread:"queryKey[0].body"},{assertion:"any",spread:"page.body"}]})})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"page"}),name:p.identifier({text:"headers"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"headers"}),right:p.objectExpression({multiLine:!0,obj:[{spread:"queryKey[0].headers"},{spread:"page.headers"}]})})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"page"}),name:p.identifier({text:"path"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"path"}),right:p.objectExpression({multiLine:!0,obj:[{spread:"queryKey[0].path"},{spread:"page.path"}]})})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"page"}),name:p.identifier({text:"query"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"query"}),right:p.objectExpression({multiLine:!0,obj:[{spread:"queryKey[0].query"},{spread:"page.query"}]})})})]})}),p.returnVariable({expression:y__default.default.factory.createAsExpression(y__default.default.factory.createAsExpression(p.identifier({text:"params"}),y__default.default.factory.createKeywordTypeNode(y__default.default.SyntaxKind.UnknownKeyword)),y__default.default.factory.createTypeQueryNode(p.identifier({text:"page"})))})],types:[{extends:p.typeReferenceNode({typeName:p.identifier({text:"Pick<QueryKey<Options>[0], 'body' | 'headers' | 'path' | 'query'>"})}),name:"K"}]}),name:Tl});e.add(t);},da=({file:e})=>{let t=p.indexedAccessTypeNode({indexType:p.typeNode(0),objectType:p.typeNode(wn,[p.typeNode(Jt)])}),r=p.identifier({text:"infinite"}),o=p.constVariable({expression:p.arrowFunction({multiLine:!0,parameters:[{name:"id",type:p.typeNode("string")},{isRequired:!1,name:"options",type:p.typeNode(Jt)},{isRequired:!1,name:"infinite",type:p.typeNode("boolean")}],returnType:t,statements:[p.constVariable({assertion:t,expression:p.objectExpression({multiLine:!1,obj:[{key:"_id",value:p.identifier({text:"id"})},{key:Nn(),value:p.identifier({text:`(options?.client ?? client).getConfig().${Nn()}`})}]}),name:"params",typeName:t}),p.ifStatement({expression:r,thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"_infinite"}),right:r})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"options"}),isOptional:!0,name:p.identifier({text:"body"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"body"}),right:p.propertyAccessExpression({expression:"options",name:"body"})})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"options"}),isOptional:!0,name:p.identifier({text:"headers"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"headers"}),right:p.propertyAccessExpression({expression:"options",name:"headers"})})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"options"}),isOptional:!0,name:p.identifier({text:"path"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"path"}),right:p.propertyAccessExpression({expression:"options",name:"path"})})})]})}),p.ifStatement({expression:p.propertyAccessExpression({expression:p.identifier({text:"options"}),isOptional:!0,name:p.identifier({text:"query"})}),thenStatement:p.block({statements:[p.expressionToStatement({expression:p.binaryExpression({left:p.propertyAccessExpression({expression:"params",name:"query"}),right:p.propertyAccessExpression({expression:"options",name:"query"})})})]})}),p.returnVariable({expression:"params"})],types:[{extends:p.typeReferenceNode({typeName:p.identifier({text:te()})}),name:Jt}]}),name:ba});e.add(o);},ya=({file:e})=>{let t=[{name:"_id",type:p.keywordTypeNode({keyword:"string"})},{isRequired:!1,name:"_infinite",type:p.keywordTypeNode({keyword:"boolean"})}],r=p.typeAliasDeclaration({name:wn,type:p.typeTupleNode({types:[p.typeIntersectionNode({types:[p.typeReferenceNode({typeName:`Pick<${Jt}, '${Nn()}' | 'body' | 'headers' | 'path' | 'query'>`}),p.typeInterfaceNode({properties:t,useLegacyResolution:!0})]})]}),typeParameters:[{extends:p.typeReferenceNode({typeName:p.identifier({text:te()})}),name:Jt}]});e.add(r);},jn=({client:e,file:t,operation:r,typesModulePath:o})=>{let{name:n}=at({client:e,meta:r.parameters.length?{$ref:r.name,name:r.name}:void 0,nameTransformer:pt,onImport:i=>{t.import({asType:!0,module:o,name:i});}});return {typeData:ze({importedType:n})}},ga=({client:e,file:t,operation:r,pluginName:o,typesModulePath:n})=>{let s=b(),{name:i}=at({client:e,meta:{$ref:r.name,name:r.name},nameTransformer:kt,onImport:c=>{t.import({asType:!0,module:n,name:c});}}),a={asType:!0,name:i};if(a.name||(a=t.import({asType:!0,module:o,name:"DefaultError"})),s.client.name==="@hey-api/client-axios"){let c=t.import({asType:!0,module:"axios",name:"AxiosError"});a={...c,name:`${c.name}<${a.name}>`};}return {typeError:a}},ha=({client:e,file:t,operation:r,typesModulePath:o})=>{let{name:n}=at({client:e,meta:{$ref:r.name,name:r.name},nameTransformer:Se,onImport:i=>{t.import({asType:!0,module:o,name:i});}});return {typeResponse:n||"void"}},xa=({id:e,isInfinite:t})=>p.arrayLiteralExpression({elements:[p.callExpression({functionName:ba,parameters:[p.ots.string(e),"options",t?p.ots.boolean(!0):void 0]})],multiLine:!1}),Ie=({client:e,files:t,plugin:r})=>{let o=b();if(P(o))throw new Error("\u{1F6AB} TanStack Query plugin does not support legacy clients");let n=t[r.name];n.import({asType:!0,module:Q({config:o,sourceOutput:r.output}),name:te()});let s=Ht({moduleOutput:t.types.nameWithoutExtension(),sourceOutput:r.output}),i=r.name==="@tanstack/angular-query-experimental"||r.name==="@tanstack/svelte-query"||r.name==="@tanstack/solid-query"?"MutationOptions":"UseMutationOptions",a,c=!1,m=!1,l=!1,u=!1,f=!1,g=new Map;for(let x of e.services)for(let h of x.operations){let C=Ue(h);if(g.has(C))continue;g.set(C,!0);let O=[o.plugins["@hey-api/sdk"]?.asClass&&Te({config:o,name:x.name}),D({config:o,handleIllegal:!o.plugins["@hey-api/sdk"]?.asClass,id:h.name,operation:h})].filter(Boolean).join("."),le=!1;if(r.queryOptions&&["GET","POST"].includes(h.method)){f||(f=!0,m||(ya({file:n}),da({file:n}),m=!0),n.import({module:r.name,name:fa})),le=!0;let{typeData:K}=jn({client:e,file:n,operation:h,typesModulePath:s}),ve=Pe(h.parameters),L=p.constVariable({exportConst:!0,expression:p.arrowFunction({parameters:[{isRequired:ve,name:"options",type:K}],statements:xa({id:h.name})}),name:_r({config:o,id:h.name,operation:h})});n.add(L);let ne=p.constVariable({comment:[],exportConst:!0,expression:p.arrowFunction({parameters:[{isRequired:ve,name:"options",type:K}],statements:[p.returnFunctionCall({args:[p.objectExpression({obj:[{key:"queryFn",value:p.arrowFunction({async:!0,multiLine:!0,parameters:[{destructure:[{name:"queryKey"},{name:"signal"}]}],statements:[p.constVariable({destructure:!0,expression:p.awaitExpression({expression:p.callExpression({functionName:O,parameters:[p.objectExpression({multiLine:!0,obj:[{spread:"options"},{spread:"queryKey[0]"},{key:"signal",shorthand:!0,value:p.identifier({text:"signal"})},{key:"throwOnError",value:!0}]})]})}),name:"data"}),p.returnVariable({expression:"data"})]})},{key:"queryKey",value:p.callExpression({functionName:_r({config:o,id:h.name,operation:h}),parameters:["options"]})}]})],name:fa})]}),name:Ol({config:o,id:h.name,operation:h})});n.add(ne);}if(r.infiniteQueryOptions&&["GET","POST"].includes(h.method)){let K,ve=h.parameters.find(L=>{if(w.lastIndex=0,w.test(L.name))return K=L,!0;if(L.in==="body"){if(L.export==="reference"){let ne=L.$refs[0];return e.models.find(Je=>Je.meta?.$ref===ne)?.properties.find(Je=>{if(w.lastIndex=0,w.test(Je.name))return K=Je,!0})}return L.properties.find(ne=>{if(w.lastIndex=0,w.test(ne.name))return K=ne,!0})}});if(ve&&K){l||(l=!0,m||(ya({file:n}),da({file:n}),m=!0),c||(Cl({file:n}),c=!0),n.import({module:r.name,name:la}),a=n.import({asType:!0,module:r.name,name:"InfiniteData"})),le=!0;let{typeData:L}=jn({client:e,file:n,operation:h,typesModulePath:s}),{typeError:ne}=ga({client:e,file:n,operation:h,pluginName:r.name,typesModulePath:s}),{typeResponse:Nt}=ha({client:e,file:n,operation:h,typesModulePath:s}),Je=Pe(h.parameters),Kr=`${wn}<${L}>`,Wn=`Pick<${Kr}[0], 'body' | 'headers' | 'path' | 'query'>`,Tp=`${K.base} | ${Wn}`,Cp=p.constVariable({exportConst:!0,expression:p.arrowFunction({parameters:[{isRequired:Je,name:"options",type:L}],returnType:Kr,statements:xa({id:h.name,isInfinite:!0})}),name:_r({config:o,id:h.name,isInfinite:!0,operation:h})});n.add(Cp);let Pp=p.constVariable({comment:[],exportConst:!0,expression:p.arrowFunction({parameters:[{isRequired:Je,name:"options",type:L}],statements:[p.returnFunctionCall({args:[p.objectExpression({comments:[{jsdoc:!1,lines:["@ts-ignore"]}],obj:[{key:"queryFn",value:p.arrowFunction({async:!0,multiLine:!0,parameters:[{destructure:[{name:"pageParam"},{name:"queryKey"},{name:"signal"}]}],statements:[p.constVariable({comment:[{jsdoc:!1,lines:["@ts-ignore"]}],expression:p.conditionalExpression({condition:p.binaryExpression({left:p.typeOfExpression({text:"pageParam"}),operator:"===",right:p.ots.string("object")}),whenFalse:p.objectExpression({multiLine:!0,obj:[{key:Rl(ve),value:p.objectExpression({multiLine:!0,obj:[{key:K.name,value:p.identifier({text:"pageParam"})}]})}]}),whenTrue:p.identifier({text:"pageParam"})}),name:"page",typeName:Wn}),p.constVariable({expression:p.callExpression({functionName:"createInfiniteParams",parameters:["queryKey","page"]}),name:"params"}),p.constVariable({destructure:!0,expression:p.awaitExpression({expression:p.callExpression({functionName:O,parameters:[p.objectExpression({multiLine:!0,obj:[{spread:"options"},{spread:"params"},{key:"signal",shorthand:!0,value:p.identifier({text:"signal"})},{key:"throwOnError",value:!0}]})]})}),name:"data"}),p.returnVariable({expression:"data"})]})},{key:"queryKey",value:p.callExpression({functionName:_r({config:o,id:h.name,isInfinite:!0,operation:h}),parameters:["options"]})}]})],name:la,types:[Nt,ne.name,`${typeof a=="string"?a:a.name}<${Nt}>`,Kr,Tp]})]}),name:xl(h)});n.add(Pp);}}if(r.mutationOptions&&["DELETE","PATCH","POST","PUT"].includes(h.method)){u||(u=!0,n.import({asType:!0,module:r.name,name:i})),le=!0;let{typeData:K}=jn({client:e,file:n,operation:h,typesModulePath:s}),{typeError:ve}=ga({client:e,file:n,operation:h,pluginName:r.name,typesModulePath:s}),{typeResponse:L}=ha({client:e,file:n,operation:h,typesModulePath:s}),ne=p.arrowFunction({parameters:[{isRequired:!1,name:"options",type:`Partial<${K}>`}],statements:[p.constVariable({expression:p.objectExpression({obj:[{key:"mutationFn",value:p.arrowFunction({async:!0,multiLine:!0,parameters:[{name:"localOptions"}],statements:[p.constVariable({destructure:!0,expression:p.awaitExpression({expression:p.callExpression({functionName:O,parameters:[p.objectExpression({multiLine:!0,obj:[{spread:"options"},{spread:"localOptions"},{key:"throwOnError",value:!0}]})]})}),name:"data"}),p.returnVariable({expression:"data"})]})}]}),name:ua,typeName:`${i}<${L}, ${ve.name}, ${K}>`}),p.returnVariable({expression:ua})]}),Nt=p.constVariable({comment:[],exportConst:!0,expression:ne,name:bl(h)});n.add(Nt);}let Ee=Ht({moduleOutput:t.sdk.nameWithoutExtension(),sourceOutput:r.output});(f||l)&&n.import({module:Ee,name:"client"}),le&&n.import({module:Ee,name:O.split(".")[0]});}};var $n={_dependencies:["@hey-api/sdk","@hey-api/typescript"],_handler:Ae,_handlerLegacy:Ie,infiniteQueryOptions:!0,mutationOptions:!0,name:"@tanstack/angular-query-experimental",output:"@tanstack/angular-query-experimental",queryOptions:!0};var Dn={_dependencies:["@hey-api/sdk","@hey-api/typescript"],_handler:Ae,_handlerLegacy:Ie,infiniteQueryOptions:!0,mutationOptions:!0,name:"@tanstack/react-query",output:"@tanstack/react-query",queryOptions:!0};var Mn={_dependencies:["@hey-api/sdk","@hey-api/typescript"],_handler:Ae,_handlerLegacy:Ie,infiniteQueryOptions:!0,mutationOptions:!0,name:"@tanstack/solid-query",output:"@tanstack/solid-query",queryOptions:!0};var Fn={_dependencies:["@hey-api/sdk","@hey-api/typescript"],_handler:Ae,_handlerLegacy:Ie,infiniteQueryOptions:!0,mutationOptions:!0,name:"@tanstack/svelte-query",output:"@tanstack/svelte-query",queryOptions:!0};var Ln={_dependencies:["@hey-api/sdk","@hey-api/typescript"],_handler:Ae,_handlerLegacy:Ie,infiniteQueryOptions:!0,mutationOptions:!0,name:"@tanstack/vue-query",output:"@tanstack/vue-query",queryOptions:!0};var Oa="fastify",Pl=({context:e,operation:t})=>{let r=e.file({id:Oa}),o=e.file({id:"types"}),n=[],s=o.identifier({$ref:q({id:t.id,type:"data"}),namespace:"type"});s.name&&(t.body&&(r.import({asType:!0,module:r.relativePathToFile({context:e,id:"types"}),name:s.name}),n.push({isRequired:t.body.required,name:"Body",type:`${s.name}['body']`})),t.parameters&&(t.parameters.header&&(r.import({asType:!0,module:r.relativePathToFile({context:e,id:"types"}),name:s.name}),n.push({isRequired:Xe(t.parameters.header),name:"Headers",type:`${s.name}['headers']`})),t.parameters.path&&(r.import({asType:!0,module:r.relativePathToFile({context:e,id:"types"}),name:s.name}),n.push({isRequired:Xe(t.parameters.path),name:"Params",type:`${s.name}['path']`})),t.parameters.query&&(r.import({asType:!0,module:r.relativePathToFile({context:e,id:"types"}),name:s.name}),n.push({isRequired:Xe(t.parameters.query),name:"Querystring",type:`${s.name}['query']`}))));let{errors:i,responses:a}=vt(t),c,m=o.identifier({$ref:q({id:t.id,type:"errors"}),namespace:"type"});if(m.name&&i&&i.properties){let x=Object.keys(i.properties);if(x.length){if(!x.includes("default"))r.import({asType:!0,module:r.relativePathToFile({context:e,id:"types"}),name:m.name}),c=p.typeReferenceNode({typeName:m.name});else if(x.length>1){r.import({asType:!0,module:r.relativePathToFile({context:e,id:"types"}),name:m.name});let C=p.typeReferenceNode({typeName:m.name}),O=p.literalTypeNode({literal:p.stringLiteral({text:"default"})});c=p.typeReferenceNode({typeArguments:[C,O],typeName:"Omit"});}}}let l,u=o.identifier({$ref:q({id:t.id,type:"responses"}),namespace:"type"});if(u.name&&a&&a.properties){let x=Object.keys(a.properties);if(x.length){if(!x.includes("default"))r.import({asType:!0,module:r.relativePathToFile({context:e,id:"types"}),name:u.name}),l=p.typeReferenceNode({typeName:u.name});else if(x.length>1){r.import({asType:!0,module:r.relativePathToFile({context:e,id:"types"}),name:u.name});let C=p.typeReferenceNode({typeName:u.name}),O=p.literalTypeNode({literal:p.stringLiteral({text:"default"})});l=p.typeReferenceNode({typeArguments:[C,O],typeName:"Omit"});}}}let f=[c,l].filter(Boolean);return f.length&&n.push({name:"Reply",type:p.typeIntersectionNode({types:f})}),n.length?{name:t.id,type:p.typeNode("RouteHandler",[p.typeInterfaceNode({properties:n,useLegacyResolution:!1})])}:void 0},Ra=({context:e,plugin:t})=>{let r=e.createFile({id:Oa,path:t.output}),o=[];e.subscribe("operation",({operation:n})=>{let s=Pl({context:e,operation:n});s&&o.push(s);}),e.subscribe("after",()=>{let n=r.identifier({$ref:"RouteHandlers",create:!0,namespace:"type"});n.name&&(o.length&&r.import({asType:!0,module:"fastify",name:"RouteHandler"}),r.add(p.typeAliasDeclaration({exportType:!0,name:n.name,type:p.typeInterfaceNode({properties:o,useLegacyResolution:!1})})));});};var Hn={_dependencies:["@hey-api/typescript"],_handler:Ra,_handlerLegacy:()=>{},name:"fastify",output:"fastify"};var Sa="zod",Ca=/^\d+$/,Sl=p.identifier({text:"default"}),Al=p.identifier({text:"optional"}),Il=p.identifier({text:"readonly"}),de=p.identifier({text:"z"}),El=({context:e,namespace:t,schema:r})=>{let o=p.propertyAccessExpression({expression:de,name:p.identifier({text:r.type})}),n;if(!r.items)n=p.callExpression({functionName:o,parameters:[jt({context:e,namespace:t,schema:{type:"unknown"}})]});else {r=Me({schema:r});let s=r.items.map(i=>Br({context:e,namespace:t,schema:i}));s.length===1?n=p.callExpression({functionName:o,parameters:s}):(r.logicalOperator,n=p.callExpression({functionName:o,parameters:[jt({context:e,namespace:t,schema:{type:"unknown"}})]}));}return r.minItems===r.maxItems&&r.minItems!==void 0?n=p.callExpression({functionName:p.propertyAccessExpression({expression:n,name:p.identifier({text:"length"})}),parameters:[p.valueToExpression({value:r.minItems})]}):(r.minItems!==void 0&&(n=p.callExpression({functionName:p.propertyAccessExpression({expression:n,name:p.identifier({text:"min"})}),parameters:[p.valueToExpression({value:r.minItems})]})),r.maxItems!==void 0&&(n=p.callExpression({functionName:p.propertyAccessExpression({expression:n,name:p.identifier({text:"max"})}),parameters:[p.valueToExpression({value:r.maxItems})]}))),n},vl=({schema:e})=>(e.const,p.callExpression({functionName:p.propertyAccessExpression({expression:de,name:p.identifier({text:e.type})})})),kl=({context:e,namespace:t,schema:r})=>{let o=[];for(let s of r.items??[])s.type==="string"&&typeof s.const=="string"&&o.push(p.stringLiteral({text:s.const}));return o.length?p.callExpression({functionName:p.propertyAccessExpression({expression:de,name:p.identifier({text:r.type})}),parameters:[p.arrayLiteralExpression({elements:o,multiLine:!1})]}):jt({context:e,namespace:t,schema:{type:"unknown"}})},ql=({schema:e})=>p.callExpression({functionName:p.propertyAccessExpression({expression:de,name:p.identifier({text:e.type})})}),jl=({schema:e})=>p.callExpression({functionName:p.propertyAccessExpression({expression:de,name:p.identifier({text:e.type})})}),Nl=({schema:e})=>{let t=p.callExpression({functionName:p.propertyAccessExpression({expression:de,name:p.identifier({text:e.type})})});return e.const,e.exclusiveMinimum!==void 0?t=p.callExpression({functionName:p.propertyAccessExpression({expression:t,name:p.identifier({text:"gt"})}),parameters:[p.valueToExpression({value:e.exclusiveMinimum})]}):e.minimum!==void 0&&(t=p.callExpression({functionName:p.propertyAccessExpression({expression:t,name:p.identifier({text:"gte"})}),parameters:[p.valueToExpression({value:e.minimum})]})),e.exclusiveMaximum!==void 0?t=p.callExpression({functionName:p.propertyAccessExpression({expression:t,name:p.identifier({text:"lt"})}),parameters:[p.valueToExpression({value:e.exclusiveMaximum})]}):e.maximum!==void 0&&(t=p.callExpression({functionName:p.propertyAccessExpression({expression:t,name:p.identifier({text:"lte"})}),parameters:[p.valueToExpression({value:e.maximum})]})),t},wl=({context:e,schema:t})=>{let r=[],o=t.required??[];for(let s in t.properties){let i=t.properties[s],a=o.includes(s),c=Br({context:e,schema:i});if(i.accessScope==="read"&&(c=p.callExpression({functionName:p.propertyAccessExpression({expression:c,name:Il})})),a||(c=p.callExpression({functionName:p.propertyAccessExpression({expression:c,name:Al})})),i.default!==void 0){let l=p.valueToExpression({value:i.default});l&&(c=p.callExpression({functionName:p.propertyAccessExpression({expression:c,name:Sl}),parameters:[l]}));}Ca.lastIndex=0;let m=Ca.test(s)?y__default.default.factory.createNumericLiteral(s):s;(s.match(/^[0-9]/)&&s.match(/\D+/g)||s.match(/\W/g))&&!s.startsWith("'")&&!s.endsWith("'")&&(m=`'${s}'`),r.push(p.propertyAssignment({initializer:c,name:m}));}return p.callExpression({functionName:p.propertyAccessExpression({expression:de,name:p.identifier({text:t.type})}),parameters:[y__default.default.factory.createObjectLiteralExpression(r,!0)]})},$l=({schema:e})=>{let t=p.callExpression({functionName:p.propertyAccessExpression({expression:de,name:p.identifier({text:e.type})})});if(e.const,e.format)switch(e.format){case"date-time":t=p.callExpression({functionName:p.propertyAccessExpression({expression:t,name:p.identifier({text:"datetime"})})});break;case"ipv4":case"ipv6":t=p.callExpression({functionName:p.propertyAccessExpression({expression:t,name:p.identifier({text:"ip"})})});break;case"uri":t=p.callExpression({functionName:p.propertyAccessExpression({expression:t,name:p.identifier({text:"url"})})});break;case"date":case"email":case"time":case"uuid":t=p.callExpression({functionName:p.propertyAccessExpression({expression:t,name:p.identifier({text:e.format})})});break}return e.minLength===e.maxLength&&e.minLength!==void 0?t=p.callExpression({functionName:p.propertyAccessExpression({expression:t,name:p.identifier({text:"length"})}),parameters:[p.valueToExpression({value:e.minLength})]}):(e.minLength!==void 0&&(t=p.callExpression({functionName:p.propertyAccessExpression({expression:t,name:p.identifier({text:"min"})}),parameters:[p.valueToExpression({value:e.minLength})]})),e.maxLength!==void 0&&(t=p.callExpression({functionName:p.propertyAccessExpression({expression:t,name:p.identifier({text:"max"})}),parameters:[p.valueToExpression({value:e.maxLength})]}))),t},Dl=({schema:e})=>p.callExpression({functionName:p.propertyAccessExpression({expression:de,name:p.identifier({text:e.type})})}),jt=({schema:e})=>p.callExpression({functionName:p.propertyAccessExpression({expression:de,name:p.identifier({text:e.type})})}),Ml=({schema:e})=>p.callExpression({functionName:p.propertyAccessExpression({expression:de,name:p.identifier({text:e.type})})}),Pa=({context:e,namespace:t,schema:r})=>{switch(r.type){case"array":return El({context:e,namespace:t,schema:r});case"boolean":return vl({context:e,namespace:t,schema:r});case"enum":return kl({context:e,namespace:t,schema:r});case"never":return ql({context:e,namespace:t,schema:r});case"null":return jl({context:e,namespace:t,schema:r});case"number":return Nl({context:e,namespace:t,schema:r});case"object":return wl({context:e,namespace:t,schema:r});case"string":return $l({context:e,namespace:t,schema:r});case"tuple":return jt({context:e,namespace:t,schema:{type:"unknown"}});case"undefined":return Dl({context:e,namespace:t,schema:r});case"unknown":return jt({context:e,namespace:t,schema:r});case"void":return Ml({context:e,namespace:t,schema:r})}},Br=({$ref:e,context:t,namespace:r=[],schema:o})=>{let n=t.file({id:Sa}),s;if(o.$ref){let i=n.identifier({$ref:o.$ref,namespace:"value"});if(i.name)s=p.identifier({text:`z${i.name||""}`});else {let a=t.resolveIrRef(o.$ref);s=Br({context:t,schema:a});}}else o.type?s=Pa({$ref:e,context:t,namespace:r,schema:o}):o.items?s=jt({context:t,namespace:r,schema:{type:"unknown"}}):s=Pa({context:t,namespace:r,schema:{type:"unknown"}});if(e&&Tt(e)){let i=n.identifier({$ref:e,create:!0,namespace:"value"}),a=p.constVariable({exportConst:!0,expression:s,name:`z${i.name||""}`});n.add(a);}return s},Aa=({context:e,plugin:t})=>{e.createFile({id:Sa,path:t.output}).import({module:"zod",name:"z"}),e.subscribe("schema",({$ref:o,schema:n})=>{Br({$ref:o,context:e,schema:n});});};var _n={_handler:Aa,_handlerLegacy:()=>{},name:"zod",output:"zod"};var Bn={"@hey-api/schemas":Rn,"@hey-api/sdk":Pn,"@hey-api/transformers":Sn,"@hey-api/typescript":En,"@tanstack/angular-query-experimental":$n,"@tanstack/react-query":Dn,"@tanstack/solid-query":Mn,"@tanstack/svelte-query":Fn,"@tanstack/vue-query":Ln,fastify:Hn,zod:_n};var Ia=["@hey-api/client-axios","@hey-api/client-fetch","legacy/angular","legacy/axios","legacy/fetch","legacy/node","legacy/xhr"];var Ea={1:function(e,t,r,o,n){return `import { NgModule} from '@angular/core';
import { HttpClientModule } from '@angular/common/http';

import { AngularHttpRequest } from './core/AngularHttpRequest';
import { BaseHttpRequest } from './core/BaseHttpRequest';
import type { OpenAPIConfig } from './core/OpenAPI';
import { OpenAPI } from './core/OpenAPI';
import { Interceptors } from './core/OpenAPI';
`},3:function(e,t,r,o,n){var s,i=e.strict,a=e.lambda;return `import type { BaseHttpRequest } from './core/BaseHttpRequest';
import type { OpenAPIConfig } from './core/OpenAPI';
import { Interceptors } from './core/OpenAPI';
import { `+((s=a(i(t,"httpRequest",{start:{line:14,column:12},end:{line:14,column:23}}),t))!=null?s:"")+" } from './core/"+((s=a(i(t,"httpRequest",{start:{line:14,column:45},end:{line:14,column:56}}),t))!=null?s:"")+`';
`},5:function(e,t,r,o,n){var s,i=e.lookupProperty||function(a,c){if(Object.prototype.hasOwnProperty.call(a,c))return a[c]};return (s=i(r,"each").call(t??(e.nullContext||{}),i(t,"services"),{name:"each",hash:{},fn:e.program(6,n,0),inverse:e.noop,data:n,loc:{start:{line:18,column:0},end:{line:20,column:9}}}))!=null?s:""},6:function(e,t,r,o,n){var s,i=e.lookupProperty||function(a,c){if(Object.prototype.hasOwnProperty.call(a,c))return a[c]};return "import { "+((s=i(r,"transformServiceName").call(t??(e.nullContext||{}),i(t,"name"),{name:"transformServiceName",hash:{},data:n,loc:{start:{line:19,column:9},end:{line:19,column:40}}}))!=null?s:"")+` } from './sdk.gen';
`},8:function(e,t,r,o,n){var s,i=e.strict,a=e.lambda,c=e.lookupProperty||function(m,l){if(Object.prototype.hasOwnProperty.call(m,l))return m[l]};return `@NgModule({
	imports: [HttpClientModule],
	providers: [
		{
			provide: OpenAPI,
			useValue: {
				BASE: OpenAPI?.BASE ?? '`+((s=a(i(t,"server",{start:{line:30,column:31},end:{line:30,column:37}}),t))!=null?s:"")+`',
				VERSION: OpenAPI?.VERSION ?? '`+((s=a(i(t,"version",{start:{line:31,column:37},end:{line:31,column:44}}),t))!=null?s:"")+`',
				WITH_CREDENTIALS: OpenAPI?.WITH_CREDENTIALS ?? false,
				CREDENTIALS: OpenAPI?.CREDENTIALS ?? 'include',
				TOKEN: OpenAPI?.TOKEN,
				USERNAME: OpenAPI?.USERNAME,
				PASSWORD: OpenAPI?.PASSWORD,
				HEADERS: OpenAPI?.HEADERS,
				ENCODE_PATH: OpenAPI?.ENCODE_PATH,
				interceptors: {
					response: OpenAPI?.interceptors?.response ?? new Interceptors(),
				},
			} as OpenAPIConfig,
		},
		{
			provide: BaseHttpRequest,
			useClass: AngularHttpRequest,
		},
`+((s=c(r,"each").call(t??(e.nullContext||{}),c(t,"services"),{name:"each",hash:{},fn:e.program(9,n,0),inverse:e.noop,data:n,loc:{start:{line:48,column:2},end:{line:50,column:11}}}))!=null?s:"")+`	]
})
export class `+((s=a(i(c(c(n,"root"),"$config"),"name",{start:{line:53,column:16},end:{line:53,column:34}}),t))!=null?s:"")+` {}
`},9:function(e,t,r,o,n){var s,i=e.lookupProperty||function(a,c){if(Object.prototype.hasOwnProperty.call(a,c))return a[c]};return "		"+((s=i(r,"transformServiceName").call(t??(e.nullContext||{}),i(t,"name"),{name:"transformServiceName",hash:{},data:n,loc:{start:{line:49,column:2},end:{line:49,column:33}}}))!=null?s:"")+`,
`},11:function(e,t,r,o,n){var s,i=e.strict,a=e.lambda,c=t??(e.nullContext||{}),m=e.lookupProperty||function(l,u){if(Object.prototype.hasOwnProperty.call(l,u))return l[u]};return `type HttpRequestConstructor = new (config: OpenAPIConfig) => BaseHttpRequest;

export class `+((s=a(i(m(m(n,"root"),"$config"),"name",{start:{line:57,column:16},end:{line:57,column:34}}),t))!=null?s:"")+` {

`+((s=m(r,"each").call(c,m(t,"services"),{name:"each",hash:{},fn:e.program(12,n,0),inverse:e.noop,data:n,loc:{start:{line:59,column:1},end:{line:61,column:10}}}))!=null?s:"")+`
	public readonly request: BaseHttpRequest;

	constructor(config?: Partial<OpenAPIConfig>, HttpRequest: HttpRequestConstructor = `+((s=a(i(t,"httpRequest",{start:{line:65,column:87},end:{line:65,column:98}}),t))!=null?s:"")+`) {
		this.request = new HttpRequest({
			BASE: config?.BASE ?? '`+((s=a(i(t,"server",{start:{line:67,column:29},end:{line:67,column:35}}),t))!=null?s:"")+`',
			VERSION: config?.VERSION ?? '`+((s=a(i(t,"version",{start:{line:68,column:35},end:{line:68,column:42}}),t))!=null?s:"")+`',
			WITH_CREDENTIALS: config?.WITH_CREDENTIALS ?? false,
			CREDENTIALS: config?.CREDENTIALS ?? 'include',
			TOKEN: config?.TOKEN,
			USERNAME: config?.USERNAME,
			PASSWORD: config?.PASSWORD,
			HEADERS: config?.HEADERS,
			ENCODE_PATH: config?.ENCODE_PATH,
			interceptors: {
				request: config?.interceptors?.request ?? new Interceptors(),
				response: config?.interceptors?.response ?? new Interceptors(),
      },
		});

`+((s=m(r,"each").call(c,m(t,"services"),{name:"each",hash:{},fn:e.program(14,n,0),inverse:e.noop,data:n,loc:{start:{line:82,column:2},end:{line:84,column:11}}}))!=null?s:"")+`	}
}
`},12:function(e,t,r,o,n){var s,i=t??(e.nullContext||{}),a=e.lookupProperty||function(c,m){if(Object.prototype.hasOwnProperty.call(c,m))return c[m]};return "	public readonly "+((s=a(r,"camelCase").call(i,a(t,"name"),{name:"camelCase",hash:{},data:n,loc:{start:{line:60,column:17},end:{line:60,column:37}}}))!=null?s:"")+": "+((s=a(r,"transformServiceName").call(i,a(t,"name"),{name:"transformServiceName",hash:{},data:n,loc:{start:{line:60,column:39},end:{line:60,column:70}}}))!=null?s:"")+`;
`},14:function(e,t,r,o,n){var s,i=t??(e.nullContext||{}),a=e.lookupProperty||function(c,m){if(Object.prototype.hasOwnProperty.call(c,m))return c[m]};return "		this."+((s=a(r,"camelCase").call(i,a(t,"name"),{name:"camelCase",hash:{},data:n,loc:{start:{line:83,column:7},end:{line:83,column:27}}}))!=null?s:"")+" = new "+((s=a(r,"transformServiceName").call(i,a(t,"name"),{name:"transformServiceName",hash:{},data:n,loc:{start:{line:83,column:34},end:{line:83,column:65}}}))!=null?s:"")+`(this.request);
`},compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){var s,i=t??(e.nullContext||{}),a=e.lookupProperty||function(c,m){if(Object.prototype.hasOwnProperty.call(c,m))return c[m]};return ((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"equals",hash:{},fn:e.program(1,n,0),inverse:e.program(3,n,0),data:n,loc:{start:{line:1,column:0},end:{line:15,column:11}}}))!=null?s:"")+`
`+((s=a(r,"if").call(i,a(t,"services"),{name:"if",hash:{},fn:e.program(5,n,0),inverse:e.noop,data:n,loc:{start:{line:17,column:0},end:{line:21,column:7}}}))!=null?s:"")+`
`+((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"equals",hash:{},fn:e.program(8,n,0),inverse:e.program(11,n,0),data:n,loc:{start:{line:23,column:0},end:{line:87,column:11}}}))!=null?s:"")},useData:!0};var va={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getHeaders = <T>(config: OpenAPIConfig, options: ApiRequestOptions<T>): Observable<HttpHeaders> => {
	return forkJoin({
		// @ts-ignore
		token: resolve(options, config.TOKEN),
		// @ts-ignore
		username: resolve(options, config.USERNAME),
		// @ts-ignore
		password: resolve(options, config.PASSWORD),
		// @ts-ignore
		additionalHeaders: resolve(options, config.HEADERS),
	}).pipe(
		map(({ token, username, password, additionalHeaders }) => {
			const headers = Object.entries({
				Accept: 'application/json',
				...additionalHeaders,
				...options.headers,
			})
				.filter(([, value]) => value !== undefined && value !== null)
				.reduce((headers, [key, value]) => ({
					...headers,
					[key]: String(value),
				}), {} as Record<string, string>);

			if (isStringWithValue(token)) {
				headers['Authorization'] = \`Bearer \${token}\`;
			}

			if (isStringWithValue(username) && isStringWithValue(password)) {
				const credentials = base64(\`\${username}:\${password}\`);
				headers['Authorization'] = \`Basic \${credentials}\`;
			}

			if (options.body !== undefined) {
				if (options.mediaType) {
					headers['Content-Type'] = options.mediaType;
				} else if (isBlob(options.body)) {
					headers['Content-Type'] = options.body.type || 'application/octet-stream';
				} else if (isString(options.body)) {
					headers['Content-Type'] = 'text/plain';
				} else if (!isFormData(options.body)) {
					headers['Content-Type'] = 'application/json';
				}
			}

			return new HttpHeaders(headers);
		}),
	);
};`},useData:!0};var ka={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getRequestBody = (options: ApiRequestOptions): unknown => {
	if (options.body) {
		if (options.mediaType?.includes('application/json') || options.mediaType?.includes('+json')) {
			return JSON.stringify(options.body);
		} else if (isString(options.body) || isBlob(options.body) || isFormData(options.body)) {
			return options.body;
		} else {
			return JSON.stringify(options.body);
		}
	}
	return undefined;
};`},useData:!0};var qa={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getResponseBody = <T>(response: HttpResponse<T>): T | undefined => {
	if (response.status !== 204 && response.body !== null) {
		return response.body;
	}
	return undefined;
};`},useData:!0};var ja={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getResponseHeader = <T>(response: HttpResponse<T>, responseHeader?: string): string | undefined => {
	if (responseHeader) {
		const value = response.headers.get(responseHeader);
		if (isString(value)) {
			return value;
		}
	}
	return undefined;
};`},useData:!0};var Na={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){var s,i=e.lookupProperty||function(a,c){if(Object.prototype.hasOwnProperty.call(a,c))return a[c]};return `import { HttpClient, HttpHeaders } from '@angular/common/http';
import type { HttpResponse, HttpErrorResponse } from '@angular/common/http';
import { forkJoin, of, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import type { Observable } from 'rxjs';

import { ApiError } from './ApiError';
import type { ApiRequestOptions } from './ApiRequestOptions';
import type { ApiResult } from './ApiResult';
import type { OpenAPIConfig } from './OpenAPI';

`+((s=e.invokePartial(i(o,"functions/isString"),t,{name:"functions/isString",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"functions/isStringWithValue"),t,{name:"functions/isStringWithValue",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"functions/isBlob"),t,{name:"functions/isBlob",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"functions/isFormData"),t,{name:"functions/isFormData",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"functions/base64"),t,{name:"functions/base64",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"functions/getQueryString"),t,{name:"functions/getQueryString",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"functions/getUrl"),t,{name:"functions/getUrl",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"functions/getFormData"),t,{name:"functions/getFormData",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"functions/resolve"),t,{name:"functions/resolve",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"angular/getHeaders"),t,{name:"angular/getHeaders",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"angular/getRequestBody"),t,{name:"angular/getRequestBody",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"angular/sendRequest"),t,{name:"angular/sendRequest",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"angular/getResponseHeader"),t,{name:"angular/getResponseHeader",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"angular/getResponseBody"),t,{name:"angular/getResponseBody",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(i(o,"functions/catchErrorCodes"),t,{name:"functions/catchErrorCodes",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

/**
 * Request method
 * @param config The OpenAPI configuration object
 * @param http The Angular HTTP client
 * @param options The request options from the service
 * @returns Observable<T>
 * @throws ApiError
 */
export const request = <T>(config: OpenAPIConfig, http: HttpClient, options: ApiRequestOptions<T>): Observable<T> => {
	const url = getUrl(config, options);
	const formData = getFormData(options);
	const body = getRequestBody(options);

	return getHeaders(config, options).pipe(
		switchMap(headers => {
			return sendRequest<T>(config, options, http, url, body, formData, headers);
		}),
		switchMap(async response => {
			for (const fn of config.interceptors.response._fns) {
				response = await fn(response);
			}
			const responseBody = getResponseBody(response);
			const responseHeader = getResponseHeader(response, options.responseHeader);

			let transformedBody = responseBody;
			if (options.responseTransformer && response.ok) {
				transformedBody = await options.responseTransformer(responseBody)
			}

			return {
				url,
				ok: response.ok,
				status: response.status,
				statusText: response.statusText,
				body: responseHeader ?? transformedBody,
			} as ApiResult;
		}),
		catchError((error: HttpErrorResponse) => {
			if (!error.status) {
				return throwError(() => error);
			}
			return of({
				url,
				ok: error.ok,
				status: error.status,
				statusText: error.statusText,
				body: error.error ?? error.statusText,
			} as ApiResult);
		}),
		map(result => {
			catchErrorCodes(options, result);
			return result.body as T;
		}),
		catchError((error: ApiError) => {
			return throwError(() => error);
		}),
	);
};`},usePartial:!0,useData:!0};var wa={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const sendRequest = <T>(
	config: OpenAPIConfig,
	options: ApiRequestOptions<T>,
	http: HttpClient,
	url: string,
	body: unknown,
	formData: FormData | undefined,
	headers: HttpHeaders
): Observable<HttpResponse<T>> => {
	return http.request<T>(options.method, url, {
		headers,
		body: body ?? formData,
		withCredentials: config.WITH_CREDENTIALS,
		observe: 'response',
	});
};`},useData:!0};var $a={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `import type { ApiRequestOptions } from './ApiRequestOptions';
import type { ApiResult } from './ApiResult';

export class ApiError extends Error {
	public readonly url: string;
	public readonly status: number;
	public readonly statusText: string;
	public readonly body: unknown;
	public readonly request: ApiRequestOptions;

	constructor(request: ApiRequestOptions, response: ApiResult, message: string) {
		super(message);

		this.name = 'ApiError';
		this.url = response.url;
		this.status = response.status;
		this.statusText = response.statusText;
		this.body = response.body;
		this.request = request;
	}
}`},useData:!0};var Da={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export type ApiRequestOptions<T = unknown> = {
	readonly body?: any;
	readonly cookies?: Record<string, unknown>;
	readonly errors?: Record<number | string, string>;
	readonly formData?: Record<string, unknown> | any[] | Blob | File;
	readonly headers?: Record<string, unknown>;
	readonly mediaType?: string;
	readonly method:
		| 'DELETE'
		| 'GET'
		| 'HEAD'
		| 'OPTIONS'
		| 'PATCH'
		| 'POST'
		| 'PUT';
	readonly path?: Record<string, unknown>;
	readonly query?: Record<string, unknown>;
	readonly responseHeader?: string;
	readonly responseTransformer?: (data: unknown) => Promise<T>;
	readonly url: string;
};`},useData:!0};var Ma={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export type ApiResult<TData = any> = {
	readonly body: TData;
	readonly ok: boolean;
	readonly status: number;
	readonly statusText: string;
	readonly url: string;
};`},useData:!0};var Fa={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getHeaders = async <T>(config: OpenAPIConfig, options: ApiRequestOptions<T>): Promise<Record<string, string>> => {
	const [token, username, password, additionalHeaders] = await Promise.all([
		// @ts-ignore
		resolve(options, config.TOKEN),
		// @ts-ignore
		resolve(options, config.USERNAME),
		// @ts-ignore
		resolve(options, config.PASSWORD),
		// @ts-ignore
		resolve(options, config.HEADERS),
	]);

	const headers = Object.entries({
		Accept: 'application/json',
		...additionalHeaders,
		...options.headers,
	})
	.filter(([, value]) => value !== undefined && value !== null)
	.reduce((headers, [key, value]) => ({
		...headers,
		[key]: String(value),
	}), {} as Record<string, string>);

	if (isStringWithValue(token)) {
		headers['Authorization'] = \`Bearer \${token}\`;
	}

	if (isStringWithValue(username) && isStringWithValue(password)) {
		const credentials = base64(\`\${username}:\${password}\`);
		headers['Authorization'] = \`Basic \${credentials}\`;
	}

	if (options.body !== undefined) {
		if (options.mediaType) {
			headers['Content-Type'] = options.mediaType;
		} else if (isBlob(options.body)) {
			headers['Content-Type'] = options.body.type || 'application/octet-stream';
		} else if (isString(options.body)) {
			headers['Content-Type'] = 'text/plain';
		} else if (!isFormData(options.body)) {
			headers['Content-Type'] = 'application/json';
		}
	} else if (options.formData !== undefined) {
		if (options.mediaType) {
			headers['Content-Type'] = options.mediaType;
		}
	}

	return headers;
};`},useData:!0};var La={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getRequestBody = (options: ApiRequestOptions): unknown => {
	if (options.body) {
		return options.body;
	}
	return undefined;
};`},useData:!0};var Ha={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getResponseBody = (response: AxiosResponse<unknown>): unknown => {
	if (response.status !== 204) {
		return response.data;
	}
	return undefined;
};`},useData:!0};var _a={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getResponseHeader = (response: AxiosResponse<unknown>, responseHeader?: string): string | undefined => {
	if (responseHeader) {
		const content = response.headers[responseHeader];
		if (isString(content)) {
			return content;
		}
	}
	return undefined;
};`},useData:!0};var Ba={1:function(e,t,r,o,n){return "ApiResult<T>"},3:function(e,t,r,o,n){return "T"},5:function(e,t,r,o,n){return "result.body"},7:function(e,t,r,o,n){return "result"},compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){var s,i=t??(e.nullContext||{}),a=e.lookupProperty||function(c,m){if(Object.prototype.hasOwnProperty.call(c,m))return c[m]};return `import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse, AxiosInstance } from 'axios';

import { ApiError } from './ApiError';
import type { ApiRequestOptions } from './ApiRequestOptions';
import type { ApiResult } from './ApiResult';
import { CancelablePromise } from './CancelablePromise';
import type { OnCancel } from './CancelablePromise';
import type { OpenAPIConfig } from './OpenAPI';

`+((s=e.invokePartial(a(o,"functions/isString"),t,{name:"functions/isString",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/isStringWithValue"),t,{name:"functions/isStringWithValue",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/isBlob"),t,{name:"functions/isBlob",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/isFormData"),t,{name:"functions/isFormData",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/isSuccess"),t,{name:"functions/isSuccess",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/base64"),t,{name:"functions/base64",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/getQueryString"),t,{name:"functions/getQueryString",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/getUrl"),t,{name:"functions/getUrl",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/getFormData"),t,{name:"functions/getFormData",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/resolve"),t,{name:"functions/resolve",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"axios/getHeaders"),t,{name:"axios/getHeaders",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"axios/getRequestBody"),t,{name:"axios/getRequestBody",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"axios/sendRequest"),t,{name:"axios/sendRequest",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"axios/getResponseHeader"),t,{name:"axios/getResponseHeader",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"axios/getResponseBody"),t,{name:"axios/getResponseBody",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/catchErrorCodes"),t,{name:"functions/catchErrorCodes",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

/**
 * Request method
 * @param config The OpenAPI configuration object
 * @param options The request options from the service
 * @param axiosClient The axios client instance to use
 * @returns CancelablePromise<`+((s=a(r,"ifServicesResponse").call(i,"response",{name:"ifServicesResponse",hash:{},fn:e.program(1,n,0),inverse:e.program(3,n,0),data:n,loc:{start:{line:64,column:30},end:{line:64,column:108}}}))!=null?s:"")+`>
 * @throws ApiError
 */
export const request = <T>(config: OpenAPIConfig, options: ApiRequestOptions<T>, axiosClient: AxiosInstance = axios): CancelablePromise<`+((s=a(r,"ifServicesResponse").call(i,"response",{name:"ifServicesResponse",hash:{},fn:e.program(1,n,0),inverse:e.program(3,n,0),data:n,loc:{start:{line:67,column:136},end:{line:67,column:214}}}))!=null?s:"")+`> => {
	return new CancelablePromise(async (resolve, reject, onCancel) => {
		try {
			const url = getUrl(config, options);
			const formData = getFormData(options);
			const body = getRequestBody(options);
			const headers = await getHeaders(config, options);

			if (!onCancel.isCancelled) {
				let response = await sendRequest<T>(config, options, url, body, formData, headers, onCancel, axiosClient);

				for (const fn of config.interceptors.response._fns) {
					response = await fn(response);
				}

				const responseBody = getResponseBody(response);
				const responseHeader = getResponseHeader(response, options.responseHeader);

				let transformedBody = responseBody;
				if (options.responseTransformer && isSuccess(response.status)) {
					transformedBody = await options.responseTransformer(responseBody)
				}

				const result: ApiResult = {
					url,
					ok: isSuccess(response.status),
					status: response.status,
					statusText: response.statusText,
					body: responseHeader ?? transformedBody,
				};

				catchErrorCodes(options, result);

				resolve(`+((s=a(r,"ifServicesResponse").call(i,"body",{name:"ifServicesResponse",hash:{},fn:e.program(5,n,0),inverse:e.program(7,n,0),data:n,loc:{start:{line:100,column:12},end:{line:100,column:90}}}))!=null?s:"")+`);
			}
		} catch (error) {
			reject(error);
		}
	});
};`},usePartial:!0,useData:!0};var Va={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const sendRequest = async <T>(
	config: OpenAPIConfig,
	options: ApiRequestOptions<T>,
	url: string,
	body: unknown,
	formData: FormData | undefined,
	headers: Record<string, string>,
	onCancel: OnCancel,
	axiosClient: AxiosInstance
): Promise<AxiosResponse<T>> => {
	const controller = new AbortController();

	let requestConfig: AxiosRequestConfig = {
		data: body ?? formData,
		headers,
		method: options.method,
		signal: controller.signal,
		url,
		withCredentials: config.WITH_CREDENTIALS,
	};

	onCancel(() => controller.abort());

	for (const fn of config.interceptors.request._fns) {
		requestConfig = await fn(requestConfig);
	}

	try {
		return await axiosClient.request(requestConfig);
	} catch (error) {
		const axiosError = error as AxiosError<T>;
		if (axiosError.response) {
			return axiosError.response;
		}
		throw error;
	}
};`},useData:!0};var Ka={1:function(e,t,r,o,n){return `import type { HttpClient } from '@angular/common/http';
import type { Observable } from 'rxjs';

import type { ApiRequestOptions } from './ApiRequestOptions';
import type { OpenAPIConfig } from './OpenAPI';
`},3:function(e,t,r,o,n){return `import type { ApiRequestOptions } from './ApiRequestOptions';
import type { CancelablePromise } from './CancelablePromise';
import type { OpenAPIConfig } from './OpenAPI';
`},5:function(e,t,r,o,n){return `	constructor(
		public readonly config: OpenAPIConfig,
		public readonly http: HttpClient,
	) {}
`},7:function(e,t,r,o,n){return `	constructor(public readonly config: OpenAPIConfig) {}
`},9:function(e,t,r,o,n){return `	public abstract request<T>(options: ApiRequestOptions<T>): Observable<T>;
`},11:function(e,t,r,o,n){return `	public abstract request<T>(options: ApiRequestOptions<T>): CancelablePromise<T>;
`},compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){var s,i=t??(e.nullContext||{}),a=e.lookupProperty||function(c,m){if(Object.prototype.hasOwnProperty.call(c,m))return c[m]};return ((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"equals",hash:{},fn:e.program(1,n,0),inverse:e.program(3,n,0),data:n,loc:{start:{line:1,column:0},end:{line:11,column:11}}}))!=null?s:"")+`
export abstract class BaseHttpRequest {

`+((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"equals",hash:{},fn:e.program(5,n,0),inverse:e.program(7,n,0),data:n,loc:{start:{line:15,column:1},end:{line:22,column:12}}}))!=null?s:"")+`
`+((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"equals",hash:{},fn:e.program(9,n,0),inverse:e.program(11,n,0),data:n,loc:{start:{line:24,column:1},end:{line:28,column:12}}}))!=null?s:"")+"}"},useData:!0};var Wa={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export class CancelError extends Error {
	constructor(message: string) {
		super(message);
		this.name = 'CancelError';
	}

	public get isCancelled(): boolean {
		return true;
	}
}

export interface OnCancel {
	readonly isResolved: boolean;
	readonly isRejected: boolean;
	readonly isCancelled: boolean;

	(cancelHandler: () => void): void;
}

export class CancelablePromise<T> implements Promise<T> {
	private _isResolved: boolean;
	private _isRejected: boolean;
	private _isCancelled: boolean;
	readonly cancelHandlers: (() => void)[];
	readonly promise: Promise<T>;
	private _resolve?: (value: T | PromiseLike<T>) => void;
	private _reject?: (reason?: unknown) => void;

	constructor(
		executor: (
			resolve: (value: T | PromiseLike<T>) => void,
			reject: (reason?: unknown) => void,
			onCancel: OnCancel
		) => void
	) {
		this._isResolved = false;
		this._isRejected = false;
		this._isCancelled = false;
		this.cancelHandlers = [];
		this.promise = new Promise<T>((resolve, reject) => {
			this._resolve = resolve;
			this._reject = reject;

			const onResolve = (value: T | PromiseLike<T>): void => {
				if (this._isResolved || this._isRejected || this._isCancelled) {
					return;
				}
				this._isResolved = true;
				if (this._resolve) this._resolve(value);
			};

			const onReject = (reason?: unknown): void => {
				if (this._isResolved || this._isRejected || this._isCancelled) {
					return;
				}
				this._isRejected = true;
				if (this._reject) this._reject(reason);
			};

			const onCancel = (cancelHandler: () => void): void => {
				if (this._isResolved || this._isRejected || this._isCancelled) {
					return;
				}
				this.cancelHandlers.push(cancelHandler);
			};

			Object.defineProperty(onCancel, 'isResolved', {
				get: (): boolean => this._isResolved,
			});

			Object.defineProperty(onCancel, 'isRejected', {
				get: (): boolean => this._isRejected,
			});

			Object.defineProperty(onCancel, 'isCancelled', {
				get: (): boolean => this._isCancelled,
			});

			return executor(onResolve, onReject, onCancel as OnCancel);
		});
	}

	get [Symbol.toStringTag]() {
		return "Cancellable Promise";
	}

	public then<TResult1 = T, TResult2 = never>(
		onFulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | null,
		onRejected?: ((reason: unknown) => TResult2 | PromiseLike<TResult2>) | null
	): Promise<TResult1 | TResult2> {
		return this.promise.then(onFulfilled, onRejected);
	}

	public catch<TResult = never>(
		onRejected?: ((reason: unknown) => TResult | PromiseLike<TResult>) | null
	): Promise<T | TResult> {
		return this.promise.catch(onRejected);
	}

	public finally(onFinally?: (() => void) | null): Promise<T> {
		return this.promise.finally(onFinally);
	}

	public cancel(): void {
		if (this._isResolved || this._isRejected || this._isCancelled) {
			return;
		}
		this._isCancelled = true;
		if (this.cancelHandlers.length) {
			try {
				for (const cancelHandler of this.cancelHandlers) {
					cancelHandler();
				}
			} catch (error) {
				console.warn('Cancellation threw an error', error);
				return;
			}
		}
		this.cancelHandlers.length = 0;
		if (this._reject) this._reject(new CancelError('Request aborted'));
	}

	public get isCancelled(): boolean {
		return this._isCancelled;
	}
}`},useData:!0};var Ua={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getHeaders = async <T>(config: OpenAPIConfig, options: ApiRequestOptions<T>): Promise<Headers> => {
	const [token, username, password, additionalHeaders] = await Promise.all([
		// @ts-ignore
		resolve(options, config.TOKEN),
		// @ts-ignore
		resolve(options, config.USERNAME),
		// @ts-ignore
		resolve(options, config.PASSWORD),
		// @ts-ignore
		resolve(options, config.HEADERS),
	]);

	const headers = Object.entries({
		Accept: 'application/json',
		...additionalHeaders,
		...options.headers,
	})
		.filter(([, value]) => value !== undefined && value !== null)
		.reduce((headers, [key, value]) => ({
			...headers,
			[key]: String(value),
		}), {} as Record<string, string>);

	if (isStringWithValue(token)) {
		headers['Authorization'] = \`Bearer \${token}\`;
	}

	if (isStringWithValue(username) && isStringWithValue(password)) {
		const credentials = base64(\`\${username}:\${password}\`);
		headers['Authorization'] = \`Basic \${credentials}\`;
	}

	if (options.body !== undefined) {
		if (options.mediaType) {
			headers['Content-Type'] = options.mediaType;
		} else if (isBlob(options.body)) {
			headers['Content-Type'] = options.body.type || 'application/octet-stream';
		} else if (isString(options.body)) {
			headers['Content-Type'] = 'text/plain';
		} else if (!isFormData(options.body)) {
			headers['Content-Type'] = 'application/json';
		}
	}

	return new Headers(headers);
};`},useData:!0};var Qa={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getRequestBody = (options: ApiRequestOptions): unknown => {
	if (options.body !== undefined) {
		if (options.mediaType?.includes('application/json') || options.mediaType?.includes('+json')) {
			return JSON.stringify(options.body);
		} else if (isString(options.body) || isBlob(options.body) || isFormData(options.body)) {
			return options.body;
		} else {
			return JSON.stringify(options.body);
		}
	}
	return undefined;
};`},useData:!0};var Xa={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getResponseBody = async (response: Response): Promise<unknown> => {
	if (response.status !== 204) {
		try {
			const contentType = response.headers.get('Content-Type');
			if (contentType) {
				const binaryTypes = ['application/octet-stream', 'application/pdf', 'application/zip', 'audio/', 'image/', 'video/'];
				if (contentType.includes('application/json') || contentType.includes('+json')) {
					return await response.json();
				} else if (binaryTypes.some(type => contentType.includes(type))) {
					return await response.blob();
				} else if (contentType.includes('multipart/form-data')) {
					return await response.formData();
				} else if (contentType.includes('text/')) {
					return await response.text();
				}
			}
		} catch (error) {
			console.error(error);
		}
	}
	return undefined;
};`},useData:!0};var za={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getResponseHeader = (response: Response, responseHeader?: string): string | undefined => {
	if (responseHeader) {
		const content = response.headers.get(responseHeader);
		if (isString(content)) {
			return content;
		}
	}
	return undefined;
};`},useData:!0};var Ga={1:function(e,t,r,o,n){return `import fetch, { FormData, Headers } from 'node-fetch';
import type { RequestInit, Response } from 'node-fetch';

`},3:function(e,t,r,o,n){return "ApiResult<T>"},5:function(e,t,r,o,n){return "T"},7:function(e,t,r,o,n){return "result.body"},9:function(e,t,r,o,n){return "result"},compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){var s,i=t??(e.nullContext||{}),a=e.lookupProperty||function(c,m){if(Object.prototype.hasOwnProperty.call(c,m))return c[m]};return ((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/node",{name:"equals",hash:{},fn:e.program(1,n,0),inverse:e.noop,data:n,loc:{start:{line:1,column:0},end:{line:5,column:11}}}))!=null?s:"")+`import { ApiError } from './ApiError';
import type { ApiRequestOptions } from './ApiRequestOptions';
import type { ApiResult } from './ApiResult';
import { CancelablePromise } from './CancelablePromise';
import type { OnCancel } from './CancelablePromise';
import type { OpenAPIConfig } from './OpenAPI';

`+((s=e.invokePartial(a(o,"functions/isString"),t,{name:"functions/isString",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/isStringWithValue"),t,{name:"functions/isStringWithValue",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/isBlob"),t,{name:"functions/isBlob",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/isFormData"),t,{name:"functions/isFormData",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/base64"),t,{name:"functions/base64",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/getQueryString"),t,{name:"functions/getQueryString",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/getUrl"),t,{name:"functions/getUrl",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/getFormData"),t,{name:"functions/getFormData",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/resolve"),t,{name:"functions/resolve",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"fetch/getHeaders"),t,{name:"fetch/getHeaders",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"fetch/getRequestBody"),t,{name:"fetch/getRequestBody",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"fetch/sendRequest"),t,{name:"fetch/sendRequest",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"fetch/getResponseHeader"),t,{name:"fetch/getResponseHeader",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"fetch/getResponseBody"),t,{name:"fetch/getResponseBody",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/catchErrorCodes"),t,{name:"functions/catchErrorCodes",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

/**
 * Request method
 * @param config The OpenAPI configuration object
 * @param options The request options from the service
 * @returns CancelablePromise<`+((s=a(r,"ifServicesResponse").call(i,"response",{name:"ifServicesResponse",hash:{},fn:e.program(3,n,0),inverse:e.program(5,n,0),data:n,loc:{start:{line:62,column:30},end:{line:62,column:108}}}))!=null?s:"")+`>
 * @throws ApiError
 */
export const request = <T>(config: OpenAPIConfig, options: ApiRequestOptions<T>): CancelablePromise<`+((s=a(r,"ifServicesResponse").call(i,"response",{name:"ifServicesResponse",hash:{},fn:e.program(3,n,0),inverse:e.program(5,n,0),data:n,loc:{start:{line:65,column:100},end:{line:65,column:178}}}))!=null?s:"")+`> => {
	return new CancelablePromise(async (resolve, reject, onCancel) => {
		try {
			const url = getUrl(config, options);
			const formData = getFormData(options);
			const body = getRequestBody(options);
			const headers = await getHeaders(config, options);

			if (!onCancel.isCancelled) {
				let response = await sendRequest(config, options, url, body, formData, headers, onCancel);

				for (const fn of config.interceptors.response._fns) {
					response = await fn(response);
				}

				const responseBody = await getResponseBody(response);
				const responseHeader = getResponseHeader(response, options.responseHeader);

				let transformedBody = responseBody;
				if (options.responseTransformer && response.ok) {
					transformedBody = await options.responseTransformer(responseBody)
				}

				const result: ApiResult = {
					url,
					ok: response.ok,
					status: response.status,
					statusText: response.statusText,
					body: responseHeader ?? transformedBody,
				};

				catchErrorCodes(options, result);

				resolve(`+((s=a(r,"ifServicesResponse").call(i,"body",{name:"ifServicesResponse",hash:{},fn:e.program(7,n,0),inverse:e.program(9,n,0),data:n,loc:{start:{line:98,column:12},end:{line:98,column:90}}}))!=null?s:"")+`);
			}
		} catch (error) {
			reject(error);
		}
	});
};`},usePartial:!0,useData:!0};var Ja={1:function(e,t,r,o,n){return `	if (config.WITH_CREDENTIALS) {
		request.credentials = config.CREDENTIALS;
	}
`},compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){var s,i=e.lookupProperty||function(a,c){if(Object.prototype.hasOwnProperty.call(a,c))return a[c]};return `export const sendRequest = async (
	config: OpenAPIConfig,
	options: ApiRequestOptions,
	url: string,
	body: any,
	formData: FormData | undefined,
	headers: Headers,
	onCancel: OnCancel
): Promise<Response> => {
	const controller = new AbortController();

	let request: RequestInit = {
		headers,
		body: body ?? formData,
		method: options.method,
		signal: controller.signal,
	};

`+((s=i(r,"equals").call(t??(e.nullContext||{}),i(i(i(i(n,"root"),"$config"),"client"),"name"),"legacy/fetch",{name:"equals",hash:{},fn:e.program(1,n,0),inverse:e.noop,data:n,loc:{start:{line:19,column:1},end:{line:23,column:12}}}))!=null?s:"")+`
	for (const fn of config.interceptors.request._fns) {
		request = await fn(request);
	}

	onCancel(() => controller.abort());

	return await fetch(url, request);
};`},useData:!0};var Za={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const base64 = (str: string): string => {
	try {
		return btoa(str);
	} catch (err) {
		// @ts-ignore
		return Buffer.from(str).toString('base64');
	}
};`},useData:!0};var Ya={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const catchErrorCodes = (options: ApiRequestOptions, result: ApiResult): void => {
	const errors: Record<number, string> = {
		400: 'Bad Request',
		401: 'Unauthorized',
		402: 'Payment Required',
		403: 'Forbidden',
		404: 'Not Found',
		405: 'Method Not Allowed',
		406: 'Not Acceptable',
		407: 'Proxy Authentication Required',
		408: 'Request Timeout',
		409: 'Conflict',
		410: 'Gone',
		411: 'Length Required',
		412: 'Precondition Failed',
		413: 'Payload Too Large',
		414: 'URI Too Long',
		415: 'Unsupported Media Type',
		416: 'Range Not Satisfiable',
		417: 'Expectation Failed',
		418: 'Im a teapot',
		421: 'Misdirected Request',
		422: 'Unprocessable Content',
		423: 'Locked',
		424: 'Failed Dependency',
		425: 'Too Early',
		426: 'Upgrade Required',
		428: 'Precondition Required',
		429: 'Too Many Requests',
		431: 'Request Header Fields Too Large',
		451: 'Unavailable For Legal Reasons',
		500: 'Internal Server Error',
		501: 'Not Implemented',
		502: 'Bad Gateway',
		503: 'Service Unavailable',
		504: 'Gateway Timeout',
		505: 'HTTP Version Not Supported',
		506: 'Variant Also Negotiates',
		507: 'Insufficient Storage',
		508: 'Loop Detected',
		510: 'Not Extended',
		511: 'Network Authentication Required',
		...options.errors,
	}

	const error = errors[result.status];
	if (error) {
		throw new ApiError(options, result, error);
	}

	if (!result.ok) {
		const errorStatus = result.status ?? 'unknown';
		const errorStatusText = result.statusText ?? 'unknown';
		const errorBody = (() => {
			try {
				return JSON.stringify(result.body, null, 2);
			} catch (e) {
				return undefined;
			}
		})();

		throw new ApiError(options, result,
			\`Generic Error: status: \${errorStatus}; status text: \${errorStatusText}; body: \${errorBody}\`
		);
	}
};`},useData:!0};var ep={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getFormData = (options: ApiRequestOptions): FormData | undefined => {
	if (options.formData) {
		const formData = new FormData();

		const process = (key: string, value: unknown) => {
			if (isString(value) || isBlob(value)) {
				formData.append(key, value);
			} else {
				formData.append(key, JSON.stringify(value));
			}
		};

		Object.entries(options.formData)
			.filter(([, value]) => value !== undefined && value !== null)
			.forEach(([key, value]) => {
				if (Array.isArray(value)) {
					value.forEach(v => process(key, v));
				} else {
					process(key, value);
				}
			});

		return formData;
	}
	return undefined;
};`},useData:!0};var tp={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getQueryString = (params: Record<string, unknown>): string => {
	const qs: string[] = [];

	const append = (key: string, value: unknown) => {
		qs.push(\`\${encodeURIComponent(key)}=\${encodeURIComponent(String(value))}\`);
	};

	const encodePair = (key: string, value: unknown) => {
		if (value === undefined || value === null) {
			return;
		}

		if (value instanceof Date) {
			append(key, value.toISOString());
		} else if (Array.isArray(value)) {
			value.forEach(v => encodePair(key, v));
		} else if (typeof value === 'object') {
			Object.entries(value).forEach(([k, v]) => encodePair(\`\${key}[\${k}]\`, v));
		} else {
			append(key, value);
		}
	};

	Object.entries(params).forEach(([key, value]) => encodePair(key, value));

	return qs.length ? \`?\${qs.join('&')}\` : '';
};`},useData:!0};var rp={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `const getUrl = (config: OpenAPIConfig, options: ApiRequestOptions): string => {
	const encoder = config.ENCODE_PATH || encodeURI;

	const path = options.url
		.replace('{api-version}', config.VERSION)
		.replace(/{(.*?)}/g, (substring: string, group: string) => {
			if (options.path?.hasOwnProperty(group)) {
				return encoder(String(options.path[group]));
			}
			return substring;
		});

	const url = config.BASE + path;
	return options.query ? url + getQueryString(options.query) : url;
};`},useData:!0};var np={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const isBlob = (value: any): value is Blob => {
	return value instanceof Blob;
};`},useData:!0};var op={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const isFormData = (value: unknown): value is FormData => {
	return value instanceof FormData;
};`},useData:!0};var sp={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const isString = (value: unknown): value is string => {
	return typeof value === 'string';
};`},useData:!0};var ip={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const isStringWithValue = (value: unknown): value is string => {
	return isString(value) && value !== '';
};`},useData:!0};var ap={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const isSuccess = (status: number): boolean => {
	return status >= 200 && status < 300;
};`},useData:!0};var pp={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `type Resolver<T> = (options: ApiRequestOptions<T>) => Promise<T>;

export const resolve = async <T>(options: ApiRequestOptions<T>, resolver?: T | Resolver<T>): Promise<T | undefined> => {
	if (typeof resolver === 'function') {
		return (resolver as Resolver<T>)(options);
	}
	return resolver;
};`},useData:!0};var cp={1:function(e,t,r,o,n){return `import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import type { Observable } from 'rxjs';

import type { ApiRequestOptions } from './ApiRequestOptions';
import { BaseHttpRequest } from './BaseHttpRequest';
import type { OpenAPIConfig } from './OpenAPI';
import { OpenAPI } from './OpenAPI';
import { request as __request } from './request';
`},3:function(e,t,r,o,n){return `import type { ApiRequestOptions } from './ApiRequestOptions';
import { BaseHttpRequest } from './BaseHttpRequest';
import type { CancelablePromise } from './CancelablePromise';
import type { OpenAPIConfig } from './OpenAPI';
import { request as __request } from './request';
`},5:function(e,t,r,o,n){return `@Injectable()
`},7:function(e,t,r,o,n){return `	constructor(
		@Inject(OpenAPI)
		config: OpenAPIConfig,
		http: HttpClient,
	) {
		super(config, http);
	}
`},9:function(e,t,r,o,n){return `	constructor(config: OpenAPIConfig) {
		super(config);
	}
`},11:function(e,t,r,o,n){return `	/**
	 * Request method
	 * @param options The request options from the service
	 * @returns Observable<T>
	 * @throws ApiError
	 */
	public override request<T>(options: ApiRequestOptions<T>): Observable<T> {
		return __request(this.config, this.http, options);
	}
`},13:function(e,t,r,o,n){return `	/**
	 * Request method
	 * @param options The request options from the service
	 * @returns CancelablePromise<T>
	 * @throws ApiError
	 */
	public override request<T>(options: ApiRequestOptions<T>): CancelablePromise<T> {
		return __request(this.config, options);
	}
`},compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){var s,i=t??(e.nullContext||{}),a=e.lookupProperty||function(c,m){if(Object.prototype.hasOwnProperty.call(c,m))return c[m]};return ((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"equals",hash:{},fn:e.program(1,n,0),inverse:e.program(3,n,0),data:n,loc:{start:{line:1,column:0},end:{line:17,column:11}}}))!=null?s:"")+`
`+((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"equals",hash:{},fn:e.program(5,n,0),inverse:e.noop,data:n,loc:{start:{line:19,column:0},end:{line:21,column:11}}}))!=null?s:"")+"export class "+((s=e.lambda(e.strict(t,"httpRequest",{start:{line:22,column:15},end:{line:22,column:26}}),t))!=null?s:"")+` extends BaseHttpRequest {

`+((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"equals",hash:{},fn:e.program(7,n,0),inverse:e.program(9,n,0),data:n,loc:{start:{line:24,column:1},end:{line:36,column:12}}}))!=null?s:"")+`
`+((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"equals",hash:{},fn:e.program(11,n,0),inverse:e.program(13,n,0),data:n,loc:{start:{line:38,column:1},end:{line:58,column:12}}}))!=null?s:"")+"}"},useData:!0};var mp={1:function(e,t,r,o,n){return `import type { HttpResponse } from '@angular/common/http';
`},3:function(e,t,r,o,n){return `import type { AxiosRequestConfig, AxiosResponse } from 'axios';
`},5:function(e,t,r,o,n){return `import type { RequestInit, Response } from 'node-fetch';
`},7:function(e,t,r,o,n){return `		response: Interceptors<HttpResponse<any>>;
`},9:function(e,t,r,o,n){return `		request: Interceptors<AxiosRequestConfig>;
		response: Interceptors<AxiosResponse>;
`},11:function(e,t,r,o,n){return `		request: Interceptors<RequestInit>;
		response: Interceptors<Response>;
`},13:function(e,t,r,o,n){return `		request: Interceptors<XMLHttpRequest>;
		response: Interceptors<XMLHttpRequest>;
`},15:function(e,t,r,o,n){return `		request: new Interceptors(),
`},compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){var s,i=t??(e.nullContext||{}),a=e.strict,c=e.lambda,m=e.lookupProperty||function(l,u){if(Object.prototype.hasOwnProperty.call(l,u))return l[u]};return ((s=m(r,"equals").call(i,m(m(m(m(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"equals",hash:{},fn:e.program(1,n,0),inverse:e.noop,data:n,loc:{start:{line:1,column:0},end:{line:3,column:11}}}))!=null?s:"")+((s=m(r,"equals").call(i,m(m(m(m(n,"root"),"$config"),"client"),"name"),"legacy/axios",{name:"equals",hash:{},fn:e.program(3,n,0),inverse:e.noop,data:n,loc:{start:{line:4,column:0},end:{line:6,column:11}}}))!=null?s:"")+((s=m(r,"equals").call(i,m(m(m(m(n,"root"),"$config"),"client"),"name"),"legacy/node",{name:"equals",hash:{},fn:e.program(5,n,0),inverse:e.noop,data:n,loc:{start:{line:7,column:0},end:{line:9,column:11}}}))!=null?s:"")+`import type { ApiRequestOptions } from './ApiRequestOptions';

type Headers = Record<string, string>;
type Middleware<T> = (value: T) => T | Promise<T>;
type Resolver<T> = (options: ApiRequestOptions<T>) => Promise<T>;

export class Interceptors<T> {
  _fns: Middleware<T>[];

  constructor() {
    this._fns = [];
  }

  eject(fn: Middleware<T>): void {
    const index = this._fns.indexOf(fn);
    if (index !== -1) {
      this._fns = [...this._fns.slice(0, index), ...this._fns.slice(index + 1)];
    }
  }

  use(fn: Middleware<T>): void {
    this._fns = [...this._fns, fn];
  }
}

export type OpenAPIConfig = {
	BASE: string;
	CREDENTIALS: 'include' | 'omit' | 'same-origin';
	ENCODE_PATH?: ((path: string) => string) | undefined;
	HEADERS?: Headers | Resolver<Headers> | undefined;
	PASSWORD?: string | Resolver<string> | undefined;
	TOKEN?: string | Resolver<string> | undefined;
	USERNAME?: string | Resolver<string> | undefined;
	VERSION: string;
	WITH_CREDENTIALS: boolean;
	interceptors: {
`+((s=m(r,"equals").call(i,m(m(m(m(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"equals",hash:{},fn:e.program(7,n,0),inverse:e.noop,data:n,loc:{start:{line:46,column:2},end:{line:48,column:13}}}))!=null?s:"")+((s=m(r,"equals").call(i,m(m(m(m(n,"root"),"$config"),"client"),"name"),"legacy/axios",{name:"equals",hash:{},fn:e.program(9,n,0),inverse:e.noop,data:n,loc:{start:{line:49,column:2},end:{line:52,column:13}}}))!=null?s:"")+((s=m(r,"equals").call(i,m(m(m(m(n,"root"),"$config"),"client"),"name"),"legacy/fetch",{name:"equals",hash:{},fn:e.program(11,n,0),inverse:e.noop,data:n,loc:{start:{line:53,column:2},end:{line:56,column:13}}}))!=null?s:"")+((s=m(r,"equals").call(i,m(m(m(m(n,"root"),"$config"),"client"),"name"),"legacy/node",{name:"equals",hash:{},fn:e.program(11,n,0),inverse:e.noop,data:n,loc:{start:{line:57,column:2},end:{line:60,column:13}}}))!=null?s:"")+((s=m(r,"equals").call(i,m(m(m(m(n,"root"),"$config"),"client"),"name"),"legacy/xhr",{name:"equals",hash:{},fn:e.program(13,n,0),inverse:e.noop,data:n,loc:{start:{line:61,column:2},end:{line:64,column:13}}}))!=null?s:"")+`	};
};

export const OpenAPI: OpenAPIConfig = {
	BASE: '`+((s=c(a(t,"server",{start:{line:69,column:11},end:{line:69,column:17}}),t))!=null?s:"")+`',
	CREDENTIALS: 'include',
	ENCODE_PATH: undefined,
	HEADERS: undefined,
	PASSWORD: undefined,
	TOKEN: undefined,
	USERNAME: undefined,
	VERSION: '`+((s=c(a(t,"version",{start:{line:76,column:14},end:{line:76,column:21}}),t))!=null?s:"")+`',
	WITH_CREDENTIALS: false,
	interceptors: {
`+((s=m(r,"notEquals").call(i,m(m(m(m(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"notEquals",hash:{},fn:e.program(15,n,0),inverse:e.noop,data:n,loc:{start:{line:79,column:2},end:{line:81,column:16}}}))!=null?s:"")+`		response: new Interceptors(),
	},
};`},useData:!0};var lp={1:function(e,t,r,o,n){var s,i=e.lookupProperty||function(a,c){if(Object.prototype.hasOwnProperty.call(a,c))return a[c]};return (s=e.invokePartial(i(o,"angular/request"),t,{name:"angular/request",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:""},3:function(e,t,r,o,n){var s,i=e.lookupProperty||function(a,c){if(Object.prototype.hasOwnProperty.call(a,c))return a[c]};return (s=e.invokePartial(i(o,"axios/request"),t,{name:"axios/request",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:""},5:function(e,t,r,o,n){var s,i=e.lookupProperty||function(a,c){if(Object.prototype.hasOwnProperty.call(a,c))return a[c]};return (s=e.invokePartial(i(o,"fetch/request"),t,{name:"fetch/request",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:""},7:function(e,t,r,o,n){var s,i=e.lookupProperty||function(a,c){if(Object.prototype.hasOwnProperty.call(a,c))return a[c]};return (s=e.invokePartial(i(o,"xhr/request"),t,{name:"xhr/request",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:""},compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){var s,i=t??(e.nullContext||{}),a=e.lookupProperty||function(c,m){if(Object.prototype.hasOwnProperty.call(c,m))return c[m]};return ((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/angular",{name:"equals",hash:{},fn:e.program(1,n,0),inverse:e.noop,data:n,loc:{start:{line:1,column:0},end:{line:1,column:87}}}))!=null?s:"")+((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/axios",{name:"equals",hash:{},fn:e.program(3,n,0),inverse:e.noop,data:n,loc:{start:{line:2,column:0},end:{line:2,column:83}}}))!=null?s:"")+((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/fetch",{name:"equals",hash:{},fn:e.program(5,n,0),inverse:e.noop,data:n,loc:{start:{line:3,column:0},end:{line:3,column:83}}}))!=null?s:"")+((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/node",{name:"equals",hash:{},fn:e.program(5,n,0),inverse:e.noop,data:n,loc:{start:{line:4,column:0},end:{line:4,column:82}}}))!=null?s:"")+((s=a(r,"equals").call(i,a(a(a(a(n,"root"),"$config"),"client"),"name"),"legacy/xhr",{name:"equals",hash:{},fn:e.program(7,n,0),inverse:e.noop,data:n,loc:{start:{line:5,column:0},end:{line:5,column:79}}}))!=null?s:"")},usePartial:!0,useData:!0};var up={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getHeaders = async <T>(config: OpenAPIConfig, options: ApiRequestOptions<T>): Promise<Headers> => {
	const [token, username, password, additionalHeaders] = await Promise.all([
		// @ts-ignore
		resolve(options, config.TOKEN),
		// @ts-ignore
		resolve(options, config.USERNAME),
		// @ts-ignore
		resolve(options, config.PASSWORD),
		// @ts-ignore
		resolve(options, config.HEADERS),
	]);

	const headers = Object.entries({
		Accept: 'application/json',
		...additionalHeaders,
		...options.headers,
	})
		.filter(([, value]) => value !== undefined && value !== null)
		.reduce((headers, [key, value]) => ({
			...headers,
			[key]: String(value),
		}), {} as Record<string, string>);

	if (isStringWithValue(token)) {
		headers['Authorization'] = \`Bearer \${token}\`;
	}

	if (isStringWithValue(username) && isStringWithValue(password)) {
		const credentials = base64(\`\${username}:\${password}\`);
		headers['Authorization'] = \`Basic \${credentials}\`;
	}

	if (options.body !== undefined) {
		if (options.mediaType) {
			headers['Content-Type'] = options.mediaType;
		} else if (isBlob(options.body)) {
			headers['Content-Type'] = options.body.type || 'application/octet-stream';
		} else if (isString(options.body)) {
			headers['Content-Type'] = 'text/plain';
		} else if (!isFormData(options.body)) {
			headers['Content-Type'] = 'application/json';
		}
	}

	return new Headers(headers);
};`},useData:!0};var fp={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getRequestBody = (options: ApiRequestOptions): unknown => {
	if (options.body !== undefined) {
		if (options.mediaType?.includes('application/json') || options.mediaType?.includes('+json')) {
			return JSON.stringify(options.body);
		} else if (isString(options.body) || isBlob(options.body) || isFormData(options.body)) {
			return options.body;
		} else {
			return JSON.stringify(options.body);
		}
	}
	return undefined;
};`},useData:!0};var dp={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getResponseBody = (xhr: XMLHttpRequest): unknown => {
	if (xhr.status !== 204) {
		try {
			const contentType = xhr.getResponseHeader('Content-Type');
			if (contentType) {
				if (contentType.includes('application/json') || contentType.includes('+json')) {
					return JSON.parse(xhr.responseText);
				} else {
					return xhr.responseText;
				}
			}
		} catch (error) {
			console.error(error);
		}
	}
	return undefined;
};`},useData:!0};var yp={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const getResponseHeader = (xhr: XMLHttpRequest, responseHeader?: string): string | undefined => {
	if (responseHeader) {
		const content = xhr.getResponseHeader(responseHeader);
		if (isString(content)) {
			return content;
		}
	}
	return undefined;
};`},useData:!0};var gp={1:function(e,t,r,o,n){return "ApiResult<T>"},3:function(e,t,r,o,n){return "T"},5:function(e,t,r,o,n){return "result.body"},7:function(e,t,r,o,n){return "result"},compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){var s,i=t??(e.nullContext||{}),a=e.lookupProperty||function(c,m){if(Object.prototype.hasOwnProperty.call(c,m))return c[m]};return `import { ApiError } from './ApiError';
import type { ApiRequestOptions } from './ApiRequestOptions';
import type { ApiResult } from './ApiResult';
import { CancelablePromise } from './CancelablePromise';
import type { OnCancel } from './CancelablePromise';
import type { OpenAPIConfig } from './OpenAPI';

`+((s=e.invokePartial(a(o,"functions/isString"),t,{name:"functions/isString",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/isStringWithValue"),t,{name:"functions/isStringWithValue",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/isBlob"),t,{name:"functions/isBlob",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/isFormData"),t,{name:"functions/isFormData",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/isSuccess"),t,{name:"functions/isSuccess",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/base64"),t,{name:"functions/base64",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/getQueryString"),t,{name:"functions/getQueryString",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/getUrl"),t,{name:"functions/getUrl",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/getFormData"),t,{name:"functions/getFormData",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/resolve"),t,{name:"functions/resolve",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"fetch/getHeaders"),t,{name:"fetch/getHeaders",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"xhr/getRequestBody"),t,{name:"xhr/getRequestBody",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"xhr/sendRequest"),t,{name:"xhr/sendRequest",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"xhr/getResponseHeader"),t,{name:"xhr/getResponseHeader",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"xhr/getResponseBody"),t,{name:"xhr/getResponseBody",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

`+((s=e.invokePartial(a(o,"functions/catchErrorCodes"),t,{name:"functions/catchErrorCodes",data:n,helpers:r,partials:o,decorators:e.decorators}))!=null?s:"")+`

/**
 * Request method
 * @param config The OpenAPI configuration object
 * @param options The request options from the service
 * @returns CancelablePromise<`+((s=a(r,"ifServicesResponse").call(i,"response",{name:"ifServicesResponse",hash:{},fn:e.program(1,n,0),inverse:e.program(3,n,0),data:n,loc:{start:{line:60,column:30},end:{line:60,column:108}}}))!=null?s:"")+`>
 * @throws ApiError
 */
export const request = <T>(config: OpenAPIConfig, options: ApiRequestOptions<T>): CancelablePromise<`+((s=a(r,"ifServicesResponse").call(i,"response",{name:"ifServicesResponse",hash:{},fn:e.program(1,n,0),inverse:e.program(3,n,0),data:n,loc:{start:{line:63,column:100},end:{line:63,column:178}}}))!=null?s:"")+`> => {
	return new CancelablePromise(async (resolve, reject, onCancel) => {
		try {
			const url = getUrl(config, options);
			const formData = getFormData(options);
			const body = getRequestBody(options);
			const headers = await getHeaders(config, options);

			if (!onCancel.isCancelled) {
				let response = await sendRequest(config, options, url, body, formData, headers, onCancel);

				for (const fn of config.interceptors.response._fns) {
					response = await fn(response);
				}

				const responseBody = getResponseBody(response);
				const responseHeader = getResponseHeader(response, options.responseHeader);

				let transformedBody = responseBody;
				if (options.responseTransformer && isSuccess(response.status)) {
					transformedBody = await options.responseTransformer(responseBody)
				}

				const result: ApiResult = {
					url,
					ok: isSuccess(response.status),
					status: response.status,
					statusText: response.statusText,
					body: responseHeader ?? transformedBody,
				};

				catchErrorCodes(options, result);

				resolve(`+((s=a(r,"ifServicesResponse").call(i,"body",{name:"ifServicesResponse",hash:{},fn:e.program(5,n,0),inverse:e.program(7,n,0),data:n,loc:{start:{line:96,column:12},end:{line:96,column:90}}}))!=null?s:"")+`);
			}
		} catch (error) {
			reject(error);
		}
	});
};`},usePartial:!0,useData:!0};var hp={compiler:[8,">= 4.3.0"],main:function(e,t,r,o,n){return `export const sendRequest = async (
	config: OpenAPIConfig,
	options: ApiRequestOptions,
	url: string,
	body: any,
	formData: FormData | undefined,
	headers: Headers,
	onCancel: OnCancel
): Promise<XMLHttpRequest> => {
	let xhr = new XMLHttpRequest();
	xhr.open(options.method, url, true);
	xhr.withCredentials = config.WITH_CREDENTIALS;

	headers.forEach((value, key) => {
		xhr.setRequestHeader(key, value);
	});

	return new Promise<XMLHttpRequest>(async (resolve, reject) => {
		xhr.onload = () => resolve(xhr);
		xhr.onabort = () => reject(new Error('Request aborted'));
		xhr.onerror = () => reject(new Error('Network error'));

		for (const fn of config.interceptors.request._fns) {
			xhr = await fn(xhr);
		}

		xhr.send(body ?? formData);

		onCancel(() => xhr.abort());
	});
};`},useData:!0};var Fl=()=>{d__default.default.registerHelper("camelCase",function(e){return I({input:e})}),d__default.default.registerHelper("equals",function(e,t,r){return e===t?r.fn(this):r.inverse(this)}),d__default.default.registerHelper("ifServicesResponse",function(e,t){return b().plugins["@hey-api/sdk"]?.response===e?t.fn(this):t.inverse(this)}),d__default.default.registerHelper("ifdef",function(...e){let t=e.pop();return e.every(r=>!r)?t.inverse(this):t.fn(this)}),d__default.default.registerHelper("notEquals",function(e,t,r){return e!==t?r.fn(this):r.inverse(this)}),d__default.default.registerHelper("transformServiceName",function(e){return Te({config:b(),name:e})});},xp=()=>{Fl();let e={client:d__default.default.template(Ea),core:{apiError:d__default.default.template($a),apiRequestOptions:d__default.default.template(Da),apiResult:d__default.default.template(Ma),baseHttpRequest:d__default.default.template(Ka),cancelablePromise:d__default.default.template(Wa),httpRequest:d__default.default.template(cp),request:d__default.default.template(lp),settings:d__default.default.template(mp)}};return d__default.default.registerPartial("functions/base64",d__default.default.template(Za)),d__default.default.registerPartial("functions/catchErrorCodes",d__default.default.template(Ya)),d__default.default.registerPartial("functions/getFormData",d__default.default.template(ep)),d__default.default.registerPartial("functions/getQueryString",d__default.default.template(tp)),d__default.default.registerPartial("functions/getUrl",d__default.default.template(rp)),d__default.default.registerPartial("functions/isBlob",d__default.default.template(np)),d__default.default.registerPartial("functions/isFormData",d__default.default.template(op)),d__default.default.registerPartial("functions/isString",d__default.default.template(sp)),d__default.default.registerPartial("functions/isStringWithValue",d__default.default.template(ip)),d__default.default.registerPartial("functions/isSuccess",d__default.default.template(ap)),d__default.default.registerPartial("functions/resolve",d__default.default.template(pp)),d__default.default.registerPartial("fetch/getHeaders",d__default.default.template(Ua)),d__default.default.registerPartial("fetch/getRequestBody",d__default.default.template(Qa)),d__default.default.registerPartial("fetch/getResponseBody",d__default.default.template(Xa)),d__default.default.registerPartial("fetch/getResponseHeader",d__default.default.template(za)),d__default.default.registerPartial("fetch/request",d__default.default.template(Ga)),d__default.default.registerPartial("fetch/sendRequest",d__default.default.template(Ja)),d__default.default.registerPartial("xhr/getHeaders",d__default.default.template(up)),d__default.default.registerPartial("xhr/getRequestBody",d__default.default.template(fp)),d__default.default.registerPartial("xhr/getResponseBody",d__default.default.template(dp)),d__default.default.registerPartial("xhr/getResponseHeader",d__default.default.template(yp)),d__default.default.registerPartial("xhr/request",d__default.default.template(gp)),d__default.default.registerPartial("xhr/sendRequest",d__default.default.template(hp)),d__default.default.registerPartial("axios/getHeaders",d__default.default.template(Fa)),d__default.default.registerPartial("axios/getRequestBody",d__default.default.template(La)),d__default.default.registerPartial("axios/getResponseBody",d__default.default.template(Ha)),d__default.default.registerPartial("axios/getResponseHeader",d__default.default.template(_a)),d__default.default.registerPartial("axios/request",d__default.default.template(Ba)),d__default.default.registerPartial("axios/sendRequest",d__default.default.template(Va)),d__default.default.registerPartial("angular/getHeaders",d__default.default.template(va)),d__default.default.registerPartial("angular/getRequestBody",d__default.default.template(ka)),d__default.default.registerPartial("angular/getResponseBody",d__default.default.template(qa)),d__default.default.registerPartial("angular/getResponseHeader",d__default.default.template(ja)),d__default.default.registerPartial("angular/request",d__default.default.template(Na)),d__default.default.registerPartial("angular/sendRequest",d__default.default.template(wa)),e};var bp=e=>`${e}-end`,Vn=e=>`${e}-length`,Op=e=>`${e}-start`,F={clear:()=>{performance.clearMarks(),performance.clearMeasures();},end:e=>performance.mark(bp(e)),getEntriesByName:e=>performance.getEntriesByName(Vn(e)),measure:e=>performance.measure(Vn(e),Op(e),bp(e)),start:e=>performance.mark(Op(e))},Vr=class{totalMeasure;constructor({totalMark:t}){this.totalMeasure=F.measure(t);}report({marks:t}){let r=Math.ceil(this.totalMeasure.duration*100)/100,o=this.totalMeasure.name;console.warn(`${o.substring(0,o.length-Vn("").length)}: ${r.toFixed(2)}ms`),t.forEach(n=>{let s=F.measure(n),i=Math.ceil(s.duration*100)/100,a=Math.ceil(s.duration/this.totalMeasure.duration*100*100)/100;console.warn(`${n}: ${i.toFixed(2)}ms (${a.toFixed(2)}%)`);});}};var Bl={biome:{args:e=>["format","--write",e],command:"biome",name:"Biome (Format)"},prettier:{args:e=>["--ignore-unknown",e,"--write","--ignore-path","./.prettierignore"],command:"prettier",name:"Prettier"}},Vl={biome:{args:e=>["lint","--apply",e],command:"biome",name:"Biome (Lint)"},eslint:{args:e=>[e,"--fix"],command:"eslint",name:"ESLint"},oxlint:{args:e=>["--fix",e],command:"oxlint",name:"oxlint"}},Kl=({config:e})=>{if(e.output.format){let t=Bl[e.output.format];console.log(`\u2728 Running ${t.name}`),(0, Kn.sync)(t.command,t.args(e.output.path));}if(e.output.lint){let t=Vl[e.output.lint];console.log(`\u2728 Running ${t.name}`),(0, Kn.sync)(t.command,t.args(e.output.path));}},Wl=({config:e})=>{switch(e.client.name){case"legacy/angular":return console.log("\u2728 Creating Angular client");case"@hey-api/client-axios":case"legacy/axios":return console.log("\u2728 Creating Axios client");case"@hey-api/client-fetch":case"legacy/fetch":return console.log("\u2728 Creating Fetch client");case"legacy/node":return console.log("\u2728 Creating Node.js client");case"legacy/xhr":return console.log("\u2728 Creating XHR client")}},Ul=e=>{let t={bundle:!1,name:""};return typeof e.client=="string"?t.name=e.client:e.client&&(t={...t,...e.client}),t},Ql=e=>{let t={path:""};return typeof e.input=="string"?t.path=e.input:e.input&&e.input.path?t={...t,...e.input}:t={...t,path:e.input},t},Xl=e=>{let t={format:!1,lint:!1,path:""};return typeof e.output=="string"?t.path=e.output:t={...t,...e.output},t},zl=({pluginConfigs:e,userPlugins:t})=>{let r=new Set,o=new Set,n=s=>{if(r.has(s))throw new Error(`Circular reference detected at '${s}'`);if(!o.has(s)){r.add(s);let i=e[s];if(!i)throw new Error(`\u{1F6AB} unknown plugin dependency "${s}" - do you need to register a custom plugin with this name?`);for(let a of i._dependencies||[])n(a);for(let a of i._optionalDependencies||[])t.includes(a)&&n(a);r.delete(s),o.add(s);}};for(let s of t)n(s);return Array.from(o)},Gl=e=>{let t={},r=(e.plugins??["@hey-api/typescript","@hey-api/schemas","@hey-api/sdk"]).map(s=>typeof s=="string"?s:(s.name&&(t[s.name]=s),s.name)).filter(Boolean),o=zl({pluginConfigs:{...t,...Bn},userPlugins:r}),n=o.reduce((s,i)=>{let a=Bn[i],c=t[i];if(c&&a){let m=Object.keys(c).find(l=>l.startsWith("_"));if(m)throw new Error(`\u{1F6AB} cannot register plugin "${c.name}" - attempting to override a native plugin option "${m}"`)}return s[i]={...a,...c},s},{});return {pluginOrder:o,plugins:n}},Jl=async({config:e})=>{let t=e.input.path;if(typeof e.input.path=="string"){let r=fs$1.existsSync(e.input.path)?ae__default.default.resolve(e.input.path):e.input.path;t=await Hl__default.default.bundle(r);}return t},Zl=async e=>{let t;if(e.configFile){let n=e.configFile.split(".");t=n.slice(0,n.length-1).join(".");}let{config:r}=await c12.loadConfig({configFile:t,name:"openapi-ts"});return (Array.isArray(e)?e:Array.isArray(r)?r.map(n=>({...n,...e})):[{...r??{},...e}]).map(n=>{let{base:s,configFile:i="",debug:a=!1,dryRun:c=!1,experimentalParser:m=!1,exportCore:l=!0,name:u,request:f,useOptions:g=!0}=n;a&&console.warn("userConfig:",n);let x=Ql(n),h=Xl(n);if(!x.path)throw new Error("\u{1F6AB} missing input - which OpenAPI specification should we use to generate your client?");if(!h.path)throw new Error("\u{1F6AB} missing output - where should we generate your client?");let C=Ul(n);if(C.name&&!Ia.includes(C.name))throw new Error("\u{1F6AB} invalid client - select a valid client value");g||console.warn("\u2757\uFE0F Deprecation warning: useOptions set to false. This setting will be removed in future versions. Please migrate useOptions to true https://heyapi.dev/openapi-ts/migrating.html#v0-27-38"),h.path=ae__default.default.resolve(process.cwd(),h.path);let O=Mo({...Gl(n),base:s,client:C,configFile:i,debug:a,dryRun:c,experimentalParser:m,exportCore:P(C)?l:!1,input:x,name:u,output:h,request:f,useOptions:g});return a&&console.warn("config:",O),O})};async function Yl(e){F.start("createClient"),F.start("config");let t=await Zl(e);F.end("config"),F.start("handlebars");let r=xp();F.end("handlebars");let o=i=>async()=>{F.start("spec");let a=await Jl({config:i});F.end("spec");let c,m;if(F.start("parser"),i.experimentalParser&&!P(i)&&!j(i)&&(m=bi({config:i,spec:a})),!m){let l=xi({openApi:a});c=qi(l);}return F.end("parser"),Wl({config:i}),F.start("generator"),m?await Ci({context:m}):c&&await Ti({client:c,openApi:a,templates:r}),F.end("generator"),F.start("postprocess"),i.dryRun||(Kl({config:i}),console.log("\u2728 Done! Your client is located in:",i.output.path)),F.end("postprocess"),m||c},n=[],s=t.map(i=>o(i));for(let i of s){let a=await i();a&&"version"in a&&n.push(a);}return F.end("createClient"),e.debug&&new Vr({totalMark:"createClient"}).report({marks:["config","openapi","handlebars","parser","generator","postprocess"]}),n}var eu=e=>e,iP={createClient:Yl,defineConfig:eu};

exports.createClient = Yl;
exports.default = iP;
exports.defineConfig = eu;
//# sourceMappingURL=index.cjs.map
//# sourceMappingURL=index.cjs.map