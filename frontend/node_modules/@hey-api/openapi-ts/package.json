{"name": "@hey-api/openapi-ts", "version": "0.57.0", "description": "🚀 The OpenAPI to TypeScript codegen. Generate clients, SDKs, validators, and more.", "homepage": "https://heyapi.dev/", "repository": {"type": "git", "url": "git+https://github.com/hey-api/openapi-ts.git"}, "bugs": {"url": "https://github.com/hey-api/openapi-ts/issues"}, "license": "FSL-1.1-MIT", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "https://lmen.us"}, "funding": "https://github.com/sponsors/hey-api", "keywords": ["angular", "axios", "codegen", "fetch", "generator", "http", "javascript", "json", "node", "openapi", "rest", "swagger", "typescript", "xhr", "yaml"], "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./package.json": "./package.json"}, "bin": {"openapi-ts": "bin/index.cjs"}, "files": ["bin", "dist", "LICENSE.md"], "engines": {"node": "^18.0.0 || >=20.0.0"}, "dependencies": {"@apidevtools/json-schema-ref-parser": "11.7.2", "c12": "2.0.1", "commander": "12.1.0", "handlebars": "4.7.8"}, "peerDependencies": {"typescript": "^5.x"}, "devDependencies": {"@angular-devkit/build-angular": "18.2.12", "@angular/animations": "18.2.12", "@angular/cli": "18.2.12", "@angular/common": "18.2.12", "@angular/compiler": "18.2.12", "@angular/compiler-cli": "18.2.12", "@angular/core": "18.2.12", "@angular/forms": "18.2.12", "@angular/platform-browser": "18.2.12", "@angular/platform-browser-dynamic": "18.2.12", "@angular/router": "18.2.12", "@tanstack/angular-query-experimental": "5.60.5", "@tanstack/react-query": "5.60.5", "@tanstack/solid-query": "5.51.21", "@tanstack/svelte-query": "5.60.5", "@tanstack/vue-query": "5.60.5", "@types/cross-spawn": "6.0.6", "@types/express": "4.17.21", "axios": "1.7.7", "cross-spawn": "7.0.5", "eslint": "9.6.0", "express": "4.21.0", "fastify": "5.1.0", "glob": "10.4.3", "node-fetch": "3.3.2", "prettier": "3.3.2", "puppeteer": "22.12.1", "rxjs": "7.8.1", "ts-node": "10.9.2", "tslib": "2.8.1", "typescript": "5.5.3", "zod": "3.23.8", "@hey-api/client-axios": "0.2.10", "@hey-api/client-fetch": "0.4.4"}, "scripts": {"build": "tsup && pnpm check-exports", "check-exports": "attw --pack .", "dev": "tsup --watch", "handlebars": "node src/legacy/handlebars/handlebars.cjs", "test:coverage": "vitest run --config vitest.config.unit.ts --coverage", "test:e2e": "vitest run --config vitest.config.e2e.ts", "test:sample": "node test/sample.cjs", "test:update": "vitest watch --config vitest.config.unit.ts --update", "test:watch": "vitest watch --config vitest.config.unit.ts", "test": "vitest run --config vitest.config.unit.ts", "typecheck": "tsc --noEmit"}}