<div align="center">
  <img width="150" height="150" src="https://heyapi.dev/logo.png" alt="Logo">
  <h1 align="center"><b>OpenAPI TypeScript</b></h1>
  <p align="center">🚀 The OpenAPI to TypeScript codegen. Generate clients, SDKs, validators, and more.</p>
</div>

[Live demo](https://stackblitz.com/edit/hey-api-example?file=openapi-ts.config.ts,src%2Fclient%2Fschemas.gen.ts,src%2Fclient%2Fsdk.gen.ts,src%2Fclient%2Ftypes.gen.ts)

## Features

- works with CLI, Node.js 18+, or npx
- supports OpenAPI 2.0, 3.0, and 3.1 specifications
- supports both JSON and YAML input files
- generates TypeScript interfaces, SDKs, and JSON Schemas
- Fetch API, Axios, Angular, Node.js, and XHR clients available
- plugin ecosystem to reduce third-party boilerplate

## Documentation

Please visit our [website](https://heyapi.dev/) for documentation, guides, migrating, and more.

## Sponsoring

Love Hey API? Please consider becoming a [sponsor](https://github.com/sponsors/hey-api).

## GitHub Integration (coming soon)

Automatically update your code when the APIs it depends on change. [Find out more](https://heyapi.dev/openapi-ts/integrations.html).

## Migrating from OpenAPI Typescript Codegen?

Please read our [migration guide](https://heyapi.dev/openapi-ts/migrating.html#openapi-typescript-codegen).

## Contributing

Want to get involved? Please refer to the [contributing guide](https://heyapi.dev/contributing.html).
