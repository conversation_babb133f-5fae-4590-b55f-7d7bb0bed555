{"name": "@biomejs/biome", "version": "1.6.1", "bin": "bin/biome", "scripts": {"postinstall": "node scripts/postinstall.js"}, "homepage": "https://biomejs.dev", "repository": {"type": "git", "url": "https://github.com/biomejs/biome.git", "directory": "packages/@biomejs/biome"}, "author": "<PERSON><PERSON>", "license": "MIT OR Apache-2.0", "bugs": "https://github.com/biomejs/biome/issues", "description": "Biome is a toolchain for the web: formatter, linter and more", "files": ["bin/biome", "scripts/postinstall.js", "configuration_schema.json", "README.md", "LICENSE-APACHE", "LICENSE-MIT", "ROME-LICENSE-MIT"], "keywords": ["JavaScript", "TypeScript", "format", "lint", "toolchain", "JSON"], "engines": {"node": ">=14.*"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/biome"}, "optionalDependencies": {"@biomejs/cli-win32-x64": "1.6.1", "@biomejs/cli-win32-arm64": "1.6.1", "@biomejs/cli-darwin-x64": "1.6.1", "@biomejs/cli-darwin-arm64": "1.6.1", "@biomejs/cli-linux-x64": "1.6.1", "@biomejs/cli-linux-arm64": "1.6.1", "@biomejs/cli-linux-x64-musl": "1.6.1", "@biomejs/cli-linux-arm64-musl": "1.6.1"}}