"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Ono = void 0;
const extend_error_1 = require("./extend-error");
const normalize_1 = require("./normalize");
const to_json_1 = require("./to-json");
const constructor = Ono;
exports.Ono = constructor;
/**
 * Creates an `Ono` instance for a specifc error type.
 */
// eslint-disable-next-line @typescript-eslint/naming-convention
function Ono(ErrorConstructor, options) {
    options = normalize_1.normalizeOptions(options);
    function ono(...args) {
        let { originalError, props, message } = normalize_1.normalizeArgs(args, options);
        // Create a new error of the specified type
        let newError = new ErrorConstructor(message);
        // Extend the error with the properties of the original error and the `props` object
        return extend_error_1.extendError(newError, originalError, props);
    }
    ono[Symbol.species] = ErrorConstructor;
    return ono;
}
/**
 * Returns an object containing all properties of the given Error object,
 * which can be used with `JSON.stringify()`.
 */
Ono.toJSON = function toJSON(error) {
    return to_json_1.toJSON.call(error);
};
/**
 * Extends the given Error object with enhanced Ono functionality, such as nested stack traces,
 * additional properties, and improved support for `JSON.stringify()`.
 */
Ono.extend = function extend(error, originalError, props) {
    if (props || originalError instanceof Error) {
        return extend_error_1.extendError(error, originalError, props);
    }
    else if (originalError) {
        return extend_error_1.extendError(error, undefined, originalError);
    }
    else {
        return extend_error_1.extendError(error);
    }
};
//# sourceMappingURL=constructor.js.map