{"version": 3, "sources": ["../../use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../use-sync-external-store/shim/index.js", "../../use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "../../use-sync-external-store/shim/with-selector.js", "../../@tanstack/history/src/index.ts", "../../tiny-invariant/dist/esm/tiny-invariant.js", "../../tiny-warning/dist/tiny-warning.esm.js", "../../@tanstack/react-router/src/routerContext.tsx", "../../@tanstack/react-router/src/useRouter.tsx", "../../@tanstack/react-store/src/index.ts", "../../@tanstack/store/src/index.ts", "../../@tanstack/react-router/src/CatchBoundary.tsx", "../../@tanstack/react-router/src/useRouterState.tsx", "../../@tanstack/react-router/src/utils.ts", "../../@tanstack/react-router/src/not-found.tsx", "../../@tanstack/react-router/src/redirects.ts", "../../@tanstack/react-router/src/Matches.tsx", "../../@tanstack/react-router/src/path.ts", "../../@tanstack/react-router/src/useParams.tsx", "../../@tanstack/react-router/src/useSearch.tsx", "../../@tanstack/react-router/src/route.ts", "../../@tanstack/react-router/src/qss.ts", "../../@tanstack/react-router/src/searchParams.ts", "../../@tanstack/react-router/src/RouterProvider.tsx", "../../@tanstack/react-router/src/router.ts", "../../@tanstack/react-router/src/defer.ts", "../../@tanstack/react-router/src/awaited.tsx", "../../@tanstack/react-router/src/fileRoute.ts", "../../@tanstack/react-router/src/lazyRouteComponent.tsx", "../../@tanstack/react-router/src/link.tsx", "../../@tanstack/react-router/src/scroll-restoration.tsx", "../../@tanstack/react-router/src/useBlocker.tsx", "../../@tanstack/react-router/src/useNavigate.tsx", "../../@tanstack/react-router/src/useRouteContext.ts", "../../@tanstack/react-router/src/createServerFn.ts"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n// dispatch for CommonJS interop named imports.\n\nvar useState = React.useState,\n    useEffect = React.useEffect,\n    useLayoutEffect = React.useLayoutEffect,\n    useDebugValue = React.useDebugValue;\nvar didWarnOld18Alpha = false;\nvar didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\n\nfunction useSyncExternalStore(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n// React do not expose a way to check if we're hydrating. So users of the shim\n// will need to track that themselves and return the correct value\n// from `getSnapshot`.\ngetServerSnapshot) {\n  {\n    if (!didWarnOld18Alpha) {\n      if (React.startTransition !== undefined) {\n        didWarnOld18Alpha = true;\n\n        error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n      }\n    }\n  } // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n\n\n  var value = getSnapshot();\n\n  {\n    if (!didWarnUncachedGetSnapshot) {\n      var cachedValue = getSnapshot();\n\n      if (!objectIs(value, cachedValue)) {\n        error('The result of getSnapshot should be cached to avoid an infinite loop');\n\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  } // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n\n\n  var _useState = useState({\n    inst: {\n      value: value,\n      getSnapshot: getSnapshot\n    }\n  }),\n      inst = _useState[0].inst,\n      forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n\n\n  useLayoutEffect(function () {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n  }, [subscribe, value, getSnapshot]);\n  useEffect(function () {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n\n    var handleStoreChange = function () {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({\n          inst: inst\n        });\n      }\n    }; // Subscribe to the store and return a clean-up function.\n\n\n    return subscribe(handleStoreChange);\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  var prevValue = inst.value;\n\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\nfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n\nvar isServerEnvironment = !canUseDOM;\n\nvar shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\nvar useSyncExternalStore$2 = React.useSyncExternalStore !== undefined ? React.useSyncExternalStore : shim;\n\nexports.useSyncExternalStore = useSyncExternalStore$2;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\nvar shim = require('use-sync-external-store/shim');\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\nvar useSyncExternalStore = shim.useSyncExternalStore;\n\n// for CommonJS interop.\n\nvar useRef = React.useRef,\n    useEffect = React.useEffect,\n    useMemo = React.useMemo,\n    useDebugValue = React.useDebugValue; // Same as useSyncExternalStore, but supports selector and isEqual arguments.\n\nfunction useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n  // Use this to track the rendered snapshot.\n  var instRef = useRef(null);\n  var inst;\n\n  if (instRef.current === null) {\n    inst = {\n      hasValue: false,\n      value: null\n    };\n    instRef.current = inst;\n  } else {\n    inst = instRef.current;\n  }\n\n  var _useMemo = useMemo(function () {\n    // Track the memoized state using closure variables that are local to this\n    // memoized instance of a getSnapshot function. Intentionally not using a\n    // useRef hook, because that state would be shared across all concurrent\n    // copies of the hook/component.\n    var hasMemo = false;\n    var memoizedSnapshot;\n    var memoizedSelection;\n\n    var memoizedSelector = function (nextSnapshot) {\n      if (!hasMemo) {\n        // The first time the hook is called, there is no memoized result.\n        hasMemo = true;\n        memoizedSnapshot = nextSnapshot;\n\n        var _nextSelection = selector(nextSnapshot);\n\n        if (isEqual !== undefined) {\n          // Even if the selector has changed, the currently rendered selection\n          // may be equal to the new selection. We should attempt to reuse the\n          // current value if possible, to preserve downstream memoizations.\n          if (inst.hasValue) {\n            var currentSelection = inst.value;\n\n            if (isEqual(currentSelection, _nextSelection)) {\n              memoizedSelection = currentSelection;\n              return currentSelection;\n            }\n          }\n        }\n\n        memoizedSelection = _nextSelection;\n        return _nextSelection;\n      } // We may be able to reuse the previous invocation's result.\n\n\n      // We may be able to reuse the previous invocation's result.\n      var prevSnapshot = memoizedSnapshot;\n      var prevSelection = memoizedSelection;\n\n      if (objectIs(prevSnapshot, nextSnapshot)) {\n        // The snapshot is the same as last time. Reuse the previous selection.\n        return prevSelection;\n      } // The snapshot has changed, so we need to compute a new selection.\n\n\n      // The snapshot has changed, so we need to compute a new selection.\n      var nextSelection = selector(nextSnapshot); // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n\n      // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n      if (isEqual !== undefined && isEqual(prevSelection, nextSelection)) {\n        return prevSelection;\n      }\n\n      memoizedSnapshot = nextSnapshot;\n      memoizedSelection = nextSelection;\n      return nextSelection;\n    }; // Assigning this to a constant so that Flow knows it can't change.\n\n\n    // Assigning this to a constant so that Flow knows it can't change.\n    var maybeGetServerSnapshot = getServerSnapshot === undefined ? null : getServerSnapshot;\n\n    var getSnapshotWithSelector = function () {\n      return memoizedSelector(getSnapshot());\n    };\n\n    var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? undefined : function () {\n      return memoizedSelector(maybeGetServerSnapshot());\n    };\n    return [getSnapshotWithSelector, getServerSnapshotWithSelector];\n  }, [getSnapshot, getServerSnapshot, selector, isEqual]),\n      getSelection = _useMemo[0],\n      getServerSelection = _useMemo[1];\n\n  var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);\n  useEffect(function () {\n    inst.hasValue = true;\n    inst.value = value;\n  }, [value]);\n  useDebugValue(value);\n  return value;\n}\n\nexports.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "// While the public API was clearly inspired by the \"history\" npm package,\n// This implementation attempts to be more lightweight by\n// making assumptions about the way TanStack Router works\n\nexport interface RouterHistory {\n  location: HistoryLocation\n  subscribe: (cb: () => void) => () => void\n  push: (path: string, state?: any) => void\n  replace: (path: string, state?: any) => void\n  go: (index: number) => void\n  back: () => void\n  forward: () => void\n  createHref: (href: string) => string\n  block: (blocker: BlockerFn) => () => void\n  flush: () => void\n  destroy: () => void\n  notify: () => void\n}\n\nexport interface HistoryLocation extends ParsedPath {\n  state: HistoryState\n}\n\nexport interface ParsedPath {\n  href: string\n  pathname: string\n  search: string\n  hash: string\n}\n\nexport interface HistoryState {\n  key?: string\n}\n\ntype ShouldAllowNavigation = any\n\nexport type BlockerFn = () =>\n  | Promise<ShouldAllowNavigation>\n  | ShouldAllowNavigation\n\nconst pushStateEvent = 'pushstate'\nconst popStateEvent = 'popstate'\nconst beforeUnloadEvent = 'beforeunload'\n\nconst beforeUnloadListener = (event: Event) => {\n  event.preventDefault()\n  // @ts-ignore\n  return (event.returnValue = '')\n}\n\nconst stopBlocking = () => {\n  removeEventListener(beforeUnloadEvent, beforeUnloadListener, {\n    capture: true,\n  })\n}\n\nexport function createHistory(opts: {\n  getLocation: () => HistoryLocation\n  pushState: (path: string, state: any) => void\n  replaceState: (path: string, state: any) => void\n  go: (n: number) => void\n  back: () => void\n  forward: () => void\n  createHref: (path: string) => string\n  flush?: () => void\n  destroy?: () => void\n  onBlocked?: (onUpdate: () => void) => void\n}): RouterHistory {\n  let location = opts.getLocation()\n  let subscribers = new Set<() => void>()\n  let blockers: BlockerFn[] = []\n\n  const onUpdate = () => {\n    location = opts.getLocation()\n    subscribers.forEach((subscriber) => subscriber())\n  }\n\n  const tryNavigation = async (task: () => void) => {\n    if (typeof document !== 'undefined' && blockers.length) {\n      for (let blocker of blockers) {\n        const allowed = await blocker()\n        if (!allowed) {\n          opts.onBlocked?.(onUpdate)\n          return\n        }\n      }\n    }\n\n    task()\n  }\n\n  return {\n    get location() {\n      return location\n    },\n    subscribe: (cb: () => void) => {\n      subscribers.add(cb)\n\n      return () => {\n        subscribers.delete(cb)\n      }\n    },\n    push: (path: string, state: any) => {\n      state = assignKey(state)\n      tryNavigation(() => {\n        opts.pushState(path, state)\n        onUpdate()\n      })\n    },\n    replace: (path: string, state: any) => {\n      state = assignKey(state)\n      tryNavigation(() => {\n        opts.replaceState(path, state)\n        onUpdate()\n      })\n    },\n    go: (index) => {\n      tryNavigation(() => {\n        opts.go(index)\n      })\n    },\n    back: () => {\n      tryNavigation(() => {\n        opts.back()\n      })\n    },\n    forward: () => {\n      tryNavigation(() => {\n        opts.forward()\n      })\n    },\n    createHref: (str) => opts.createHref(str),\n    block: (blocker) => {\n      blockers.push(blocker)\n\n      if (blockers.length === 1) {\n        addEventListener(beforeUnloadEvent, beforeUnloadListener, {\n          capture: true,\n        })\n      }\n\n      return () => {\n        blockers = blockers.filter((b) => b !== blocker)\n\n        if (!blockers.length) {\n          stopBlocking()\n        }\n      }\n    },\n    flush: () => opts.flush?.(),\n    destroy: () => opts.destroy?.(),\n    notify: onUpdate,\n  }\n}\n\nfunction assignKey(state: HistoryState) {\n  if (!state) {\n    state = {} as HistoryState\n  }\n  return {\n    ...state,\n    key: createRandomKey(),\n  }\n}\n\n/**\n * Creates a history object that can be used to interact with the browser's\n * navigation. This is a lightweight API wrapping the browser's native methods.\n * It is designed to work with TanStack Router, but could be used as a standalone API as well.\n * IMPORTANT: This API implements history throttling via a microtask to prevent\n * excessive calls to the history API. In some browsers, calling history.pushState or\n * history.replaceState in quick succession can cause the browser to ignore subsequent\n * calls. This API smooths out those differences and ensures that your application\n * state will *eventually* match the browser state. In most cases, this is not a problem,\n * but if you need to ensure that the browser state is up to date, you can use the\n * `history.flush` method to immediately flush all pending state changes to the browser URL.\n * @param opts\n * @param opts.getHref A function that returns the current href (path + search + hash)\n * @param opts.createHref A function that takes a path and returns a href (path + search + hash)\n * @returns A history instance\n */\nexport function createBrowserHistory(opts?: {\n  parseLocation?: () => HistoryLocation\n  createHref?: (path: string) => string\n  window?: any\n}): RouterHistory {\n  const win =\n    opts?.window ??\n    (typeof document !== 'undefined' ? window : (undefined as any))\n\n  const createHref = opts?.createHref ?? ((path) => path)\n  const parseLocation =\n    opts?.parseLocation ??\n    (() =>\n      parseHref(\n        `${win.location.pathname}${win.location.search}${win.location.hash}`,\n        win.history.state,\n      ))\n\n  let currentLocation = parseLocation()\n  let rollbackLocation: HistoryLocation | undefined\n\n  const getLocation = () => currentLocation\n\n  let next:\n    | undefined\n    | {\n        // This is the latest location that we were attempting to push/replace\n        href: string\n        // This is the latest state that we were attempting to push/replace\n        state: any\n        // This is the latest type that we were attempting to push/replace\n        isPush: boolean\n      }\n\n  // Because we are proactively updating the location\n  // in memory before actually updating the browser history,\n  // we need to track when we are doing this so we don't\n  // notify subscribers twice on the last update.\n  let tracking = true\n\n  // We need to track the current scheduled update to prevent\n  // multiple updates from being scheduled at the same time.\n  let scheduled: Promise<void> | undefined\n\n  // This function is a wrapper to prevent any of the callback's\n  // side effects from causing a subscriber notification\n  const untrack = (fn: () => void) => {\n    tracking = false\n    fn()\n    tracking = true\n  }\n\n  // This function flushes the next update to the browser history\n  const flush = () => {\n    // Do not notify subscribers about this push/replace call\n    untrack(() => {\n      if (!next) return\n      win.history[next.isPush ? 'pushState' : 'replaceState'](\n        next.state,\n        '',\n        next.href,\n      )\n      // Reset the nextIsPush flag and clear the scheduled update\n      next = undefined\n      scheduled = undefined\n      rollbackLocation = undefined\n    })\n  }\n\n  // This function queues up a call to update the browser history\n  const queueHistoryAction = (\n    type: 'push' | 'replace',\n    destHref: string,\n    state: any,\n  ) => {\n    const href = createHref(destHref)\n\n    if (!scheduled) {\n      rollbackLocation = currentLocation\n    }\n\n    // Update the location in memory\n    currentLocation = parseHref(destHref, state)\n\n    // Keep track of the next location we need to flush to the URL\n    next = {\n      href,\n      state,\n      isPush: next?.isPush || type === 'push',\n    }\n\n    if (!scheduled) {\n      // Schedule an update to the browser history\n      scheduled = Promise.resolve().then(() => flush())\n    }\n  }\n\n  const onPushPop = () => {\n    currentLocation = parseLocation()\n    history.notify()\n  }\n\n  var originalPushState = win.history.pushState\n  var originalReplaceState = win.history.replaceState\n\n  const history = createHistory({\n    getLocation,\n    pushState: (href, state) => queueHistoryAction('push', href, state),\n    replaceState: (href, state) => queueHistoryAction('replace', href, state),\n    back: () => win.history.back(),\n    forward: () => win.history.forward(),\n    go: (n) => win.history.go(n),\n    createHref: (href) => createHref(href),\n    flush,\n    destroy: () => {\n      win.history.pushState = originalPushState\n      win.history.replaceState = originalReplaceState\n      win.removeEventListener(pushStateEvent, onPushPop)\n      win.removeEventListener(popStateEvent, onPushPop)\n    },\n    onBlocked: (onUpdate) => {\n      // If a navigation is blocked, we need to rollback the location\n      // that we optimistically updated in memory.\n      if (rollbackLocation && currentLocation !== rollbackLocation) {\n        currentLocation = rollbackLocation\n        // Notify subscribers\n        onUpdate()\n      }\n    },\n  })\n\n  win.addEventListener(pushStateEvent, onPushPop)\n  win.addEventListener(popStateEvent, onPushPop)\n\n  win.history.pushState = function () {\n    let res = originalPushState.apply(win.history, arguments as any)\n    if (tracking) history.notify()\n    return res\n  }\n\n  win.history.replaceState = function () {\n    let res = originalReplaceState.apply(win.history, arguments as any)\n    if (tracking) history.notify()\n    return res\n  }\n\n  return history\n}\n\nexport function createHashHistory(opts?: { window?: any }): RouterHistory {\n  const win =\n    opts?.window ??\n    (typeof document !== 'undefined' ? window : (undefined as any))\n  return createBrowserHistory({\n    window: win,\n    parseLocation: () => {\n      const hashHref = win.location.hash.split('#').slice(1).join('#') ?? '/'\n      return parseHref(hashHref, win.history.state)\n    },\n    createHref: (href) =>\n      `${win.location.pathname}${win.location.search}#${href}`,\n  })\n}\n\nexport function createMemoryHistory(\n  opts: {\n    initialEntries: string[]\n    initialIndex?: number\n  } = {\n    initialEntries: ['/'],\n  },\n): RouterHistory {\n  const entries = opts.initialEntries\n  let index = opts.initialIndex ?? entries.length - 1\n  let currentState = {\n    key: createRandomKey(),\n  } as HistoryState\n\n  const getLocation = () => parseHref(entries[index]!, currentState)\n\n  return createHistory({\n    getLocation,\n\n    pushState: (path, state) => {\n      currentState = state\n      entries.push(path)\n      index++\n    },\n    replaceState: (path, state) => {\n      currentState = state\n      entries[index] = path\n    },\n    back: () => {\n      index--\n    },\n    forward: () => {\n      index = Math.min(index + 1, entries.length - 1)\n    },\n    go: (n) => {\n      index = Math.min(Math.max(index + n, 0), entries.length - 1)\n    },\n    createHref: (path) => path,\n  })\n}\n\nfunction parseHref(href: string, state: HistoryState): HistoryLocation {\n  let hashIndex = href.indexOf('#')\n  let searchIndex = href.indexOf('?')\n\n  return {\n    href,\n    pathname: href.substring(\n      0,\n      hashIndex > 0\n        ? searchIndex > 0\n          ? Math.min(hashIndex, searchIndex)\n          : hashIndex\n        : searchIndex > 0\n          ? searchIndex\n          : href.length,\n    ),\n    hash: hashIndex > -1 ? href.substring(hashIndex) : '',\n    search:\n      searchIndex > -1\n        ? href.slice(searchIndex, hashIndex === -1 ? undefined : hashIndex)\n        : '',\n    state: state || {},\n  }\n}\n\n// Thanks co-pilot!\nfunction createRandomKey() {\n  return (Math.random() + 1).toString(36).substring(7)\n}\n", "var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nexport { invariant as default };\n", "var isProduction = process.env.NODE_ENV === 'production';\nfunction warning(condition, message) {\n  if (!isProduction) {\n    if (condition) {\n      return;\n    }\n\n    var text = \"Warning: \" + message;\n\n    if (typeof console !== 'undefined') {\n      console.warn(text);\n    }\n\n    try {\n      throw Error(text);\n    } catch (x) {}\n  }\n}\n\nexport default warning;\n", "import * as React from 'react'\nimport { Router } from './router'\n\nlet routerContext = React.createContext<Router<any>>(null!)\n\nexport function getRouterContext() {\n  if (typeof document === 'undefined') {\n    return routerContext\n  }\n\n  if (window.__TSR_ROUTER_CONTEXT__) {\n    return window.__TSR_ROUTER_CONTEXT__\n  }\n\n  window.__TSR_ROUTER_CONTEXT__ = routerContext as any\n\n  return routerContext\n}\n", "import * as React from 'react'\nimport warning from 'tiny-warning'\nimport { AnyRoute } from './route'\nimport { RegisteredRouter, Router } from './router'\nimport { getRouterContext } from './routerContext'\n\nexport function useRouter<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n>(opts?: { warn?: boolean }): Router<TRouteTree> {\n  const value = React.useContext(getRouterContext())\n  warning(\n    !((opts?.warn ?? true) && !value),\n    'useRouter must be used inside a <RouterProvider> component!',\n  )\n  return value as any\n}\n", "import type { AnyUpdater, Store } from '@tanstack/store'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector.js'\n\nexport * from '@tanstack/store'\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport function useStore<\n  TState,\n  TSelected = NoInfer<TState>,\n  TUpdater extends AnyUpdater = AnyUpdater,\n>(\n  store: Store<TState, TUpdater>,\n  selector: (state: NoInfer<TState>) => TSelected = (d) => d as any,\n) {\n  const slice = useSyncExternalStoreWithSelector(\n    store.subscribe,\n    () => store.state,\n    () => store.state,\n    selector,\n    shallow,\n  )\n\n  return slice\n}\n\nexport function shallow<T>(objA: T, objB: T) {\n  if (Object.is(objA, objB)) {\n    return true\n  }\n\n  if (\n    typeof objA !== 'object' ||\n    objA === null ||\n    typeof objB !== 'object' ||\n    objB === null\n  ) {\n    return false\n  }\n\n  const keysA = Object.keys(objA)\n  if (keysA.length !== Object.keys(objB).length) {\n    return false\n  }\n\n  for (let i = 0; i < keysA.length; i++) {\n    if (\n      !Object.prototype.hasOwnProperty.call(objB, keysA[i] as string) ||\n      !Object.is(objA[keysA[i] as keyof T], objB[keysA[i] as keyof T])\n    ) {\n      return false\n    }\n  }\n  return true\n}\n", "export type AnyUpdater = (...args: any[]) => any\n\nexport type Listener = (opts: { priority: Priority }) => void\n\nexport type Priority = 'high' | 'low'\n\ninterface StoreOptions<\n  TState,\n  TUpdater extends AnyUpdater = (cb: TState) => TState,\n> {\n  updateFn?: (previous: TState) => (updater: TUpdater) => TState\n  onSubscribe?: (\n    listener: Listener,\n    store: Store<TState, TUpdater>,\n  ) => () => void\n  onUpdate?: (opts: { priority: Priority }) => void\n  defaultPriority?: Priority\n}\n\nexport class Store<\n  TState,\n  TUpdater extends AnyUpdater = (cb: TState) => TState,\n> {\n  listeners = new Set<Listener>()\n  state: TState\n  options?: StoreOptions<TState, TUpdater>\n  _batching = false\n  _flushing = 0\n  _nextPriority: null | Priority = null\n\n  constructor(initialState: TState, options?: StoreOptions<TState, TUpdater>) {\n    this.state = initialState\n    this.options = options\n  }\n\n  subscribe = (listener: Listener) => {\n    this.listeners.add(listener)\n    const unsub = this.options?.onSubscribe?.(listener, this)\n    return () => {\n      this.listeners.delete(listener)\n      unsub?.()\n    }\n  }\n\n  setState = (\n    updater: TUpdater,\n    opts?: {\n      priority: Priority\n    },\n  ) => {\n    const previous = this.state\n    this.state = this.options?.updateFn\n      ? this.options.updateFn(previous)(updater)\n      : (updater as any)(previous)\n\n    const priority = opts?.priority ?? this.options?.defaultPriority ?? 'high'\n    if (this._nextPriority === null) {\n      this._nextPriority = priority\n    } else if (this._nextPriority === 'high') {\n      this._nextPriority = priority\n    } else {\n      this._nextPriority = this.options?.defaultPriority ?? 'high'\n    }\n\n    // Always run onUpdate, regardless of batching\n    this.options?.onUpdate?.({\n      priority: this._nextPriority,\n    })\n\n    // Attempt to flush\n    this._flush()\n  }\n\n  _flush = () => {\n    if (this._batching) return\n    const flushId = ++this._flushing\n    this.listeners.forEach((listener) => {\n      if (this._flushing !== flushId) return\n      listener({\n        priority: this._nextPriority ?? 'high',\n      })\n    })\n  }\n\n  batch = (cb: () => void) => {\n    if (this._batching) return cb()\n    this._batching = true\n    cb()\n    this._batching = false\n    this._flush()\n  }\n}\n", "import * as React from 'react'\n\nexport function CatchBoundary(props: {\n  getResetKey: () => string\n  children: any\n  errorComponent?: any\n  onCatch?: (error: any) => void\n}) {\n  const errorComponent = props.errorComponent ?? ErrorComponent\n\n  return (\n    <CatchBoundaryImpl\n      getResetKey={props.getResetKey}\n      onCatch={props.onCatch}\n      children={({ error }) => {\n        if (error) {\n          return React.createElement(errorComponent, {\n            error,\n          })\n        }\n\n        return props.children\n      }}\n    />\n  )\n}\n\nexport class CatchBoundaryImpl extends React.Component<{\n  getResetKey?: () => string\n  children: (props: { error: any; reset: () => void }) => any\n  onCatch?: (error: any) => void\n}> {\n  state = { error: null } as any\n  static getDerivedStateFromProps(props: any) {\n    return { resetKey: props.getResetKey?.() }\n  }\n  static getDerivedStateFromError(error: any) {\n    return { error }\n  }\n  componentDidUpdate(\n    prevProps: Readonly<{\n      getResetKey?: () => string\n      children: (props: { error: any; reset: () => void }) => any\n      onCatch?: ((error: any, info: any) => void) | undefined\n    }>,\n    prevState: any,\n  ): void {\n    if (prevState.error && prevState.resetKey !== this.state.resetKey) {\n      this.setState({ error: null })\n    }\n  }\n  componentDidCatch(error: any) {\n    if (this.props.onCatch) {\n      this.props.onCatch?.(error)\n    } else {\n      console.error(error)\n    }\n  }\n  render() {\n    return this.props.children(this.state)\n  }\n}\n\nexport function ErrorComponent({ error }: { error: any }) {\n  const [show, setShow] = React.useState(process.env.NODE_ENV !== 'production')\n\n  return (\n    <div style={{ padding: '.5rem', maxWidth: '100%' }}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '.5rem' }}>\n        <strong style={{ fontSize: '1rem' }}>Something went wrong!</strong>\n        <button\n          style={{\n            appearance: 'none',\n            fontSize: '.6em',\n            border: '1px solid currentColor',\n            padding: '.1rem .2rem',\n            fontWeight: 'bold',\n            borderRadius: '.25rem',\n          }}\n          onClick={() => setShow((d) => !d)}\n        >\n          {show ? 'Hide Error' : 'Show Error'}\n        </button>\n      </div>\n      <div style={{ height: '.25rem' }} />\n      {show ? (\n        <div>\n          <pre\n            style={{\n              fontSize: '.7em',\n              border: '1px solid red',\n              borderRadius: '.25rem',\n              padding: '.3rem',\n              color: 'red',\n              overflow: 'auto',\n            }}\n          >\n            {error.message ? <code>{error.message}</code> : null}\n          </pre>\n        </div>\n      ) : null}\n    </div>\n  )\n}\n", "import { useStore } from '@tanstack/react-store'\nimport { AnyRoute } from './route'\nimport { RegisteredRouter, Router, RouterState } from './router'\nimport { useRouter } from './useRouter'\n\nexport function useRouterState<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TSelected = RouterState<TRouteTree>,\n>(opts?: {\n  router?: Router<TRouteTree>\n  select: (state: RouterState<RegisteredRouter['routeTree']>) => TSelected\n}): TSelected {\n  const contextRouter = useRouter<TRouteTree>({\n    warn: opts?.router === undefined,\n  })\n  return useStore((opts?.router || contextRouter).__store, opts?.select as any)\n}\n", "import * as React from 'react'\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\nexport type IsAny<T, Y, N = T> = 1 extends 0 & T ? Y : N\nexport type PickAsRequired<T, K extends keyof T> = Omit<T, K> &\n  Required<Pick<T, K>>\n\nexport type PickRequired<T> = {\n  [K in keyof T as undefined extends T[K] ? never : K]: T[K]\n}\n\n// from https://stackoverflow.com/a/76458160\nexport type WithoutEmpty<T> = T extends T ? ({} extends T ? never : T) : never\n\n// export type Expand<T> = T\nexport type Expand<T> = T extends object\n  ? T extends infer O\n    ? { [K in keyof O]: O[K] }\n    : never\n  : T\n\nexport type UnionToIntersection<U> = (\n  U extends any ? (k: U) => void : never\n) extends (k: infer I) => any\n  ? I\n  : never\n\nexport type DeepOptional<T, K extends keyof T> = Pick<DeepPartial<T>, K> &\n  Omit<T, K>\n\nexport type DeepPartial<T> = T extends object\n  ? {\n      [P in keyof T]?: DeepPartial<T[P]>\n    }\n  : T\n\nexport type MakeDifferenceOptional<T, U> = Omit<U, keyof T> &\n  Partial<Pick<U, keyof T & keyof U>> &\n  PickRequired<Omit<U, keyof PickRequired<T>>>\n\n// from https://stackoverflow.com/a/53955431\nexport type IsUnion<T, U extends T = T> = (\n  T extends any ? (U extends T ? false : true) : never\n) extends false\n  ? false\n  : true\n\n// type Compute<T> = { [K in keyof T]: T[K] } | never\n\n// type AllKeys<T> = T extends any ? keyof T : never\n\n// export type MergeUnion<T, Keys extends keyof T = keyof T> = Compute<\n//   {\n//     [K in Keys]: T[Keys]\n//   } & {\n//     [K in AllKeys<T>]?: T extends any\n//       ? K extends keyof T\n//         ? T[K]\n//         : never\n//       : never\n//   }\n// >\n\nexport type Assign<Left, Right> = Omit<Left, keyof Right> & Right\n\nexport type AssignAll<T extends any[]> = T extends [infer Left, ...infer Right]\n  ? Right extends any[]\n    ? Assign<Left, AssignAll<Right>>\n    : Left\n  : {}\n\n// // Sample types to merge\n// type TypeA = {\n//   shared: string\n//   onlyInA: string\n//   nested: {\n//     shared: string\n//     aProp: string\n//   }\n//   array: string[]\n// }\n\n// type TypeB = {\n//   shared: number\n//   onlyInB: number\n//   nested: {\n//     shared: number\n//     bProp: number\n//   }\n//   array: number[]\n// }\n\n// type TypeC = {\n//   shared: boolean\n//   onlyInC: boolean\n//   nested: {\n//     shared: boolean\n//     cProp: boolean\n//   }\n//   array: boolean[]\n// }\n\n// type Test = Expand<Assign<TypeA, TypeB>>\n\n// // Using DeepMerge to merge TypeA and TypeB\n// type MergedType = Expand<AssignAll<[TypeA, TypeB, TypeC]>>\n\nexport type Timeout = ReturnType<typeof setTimeout>\n\nexport type Updater<TPrevious, TResult = TPrevious> =\n  | TResult\n  | ((prev?: TPrevious) => TResult)\n\nexport type NonNullableUpdater<TPrevious, TResult = TPrevious> =\n  | TResult\n  | ((prev: TPrevious) => TResult)\n\n// from https://github.com/type-challenges/type-challenges/issues/737\ntype LastInUnion<U> =\n  UnionToIntersection<U extends unknown ? (x: U) => 0 : never> extends (\n    x: infer L,\n  ) => 0\n    ? L\n    : never\nexport type UnionToTuple<U, Last = LastInUnion<U>> = [U] extends [never]\n  ? []\n  : [...UnionToTuple<Exclude<U, Last>>, Last]\n\n//\n\nexport const isServer = typeof document === 'undefined'\n\nexport function last<T>(arr: T[]) {\n  return arr[arr.length - 1]\n}\n\nfunction isFunction(d: any): d is Function {\n  return typeof d === 'function'\n}\n\nexport function functionalUpdate<TResult>(\n  updater: Updater<TResult> | NonNullableUpdater<TResult>,\n  previous: TResult,\n): TResult {\n  if (isFunction(updater)) {\n    return updater(previous as TResult)\n  }\n\n  return updater\n}\n\nexport function pick<T, K extends keyof T>(parent: T, keys: K[]): Pick<T, K> {\n  return keys.reduce((obj: any, key: K) => {\n    obj[key] = parent[key]\n    return obj\n  }, {} as any)\n}\n\n/**\n * This function returns `prev` if `_next` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between immutable JSON values for example.\n * Do not use this with signals\n */\nexport function replaceEqualDeep<T>(prev: any, _next: T): T {\n  if (prev === _next) {\n    return prev\n  }\n\n  const next = _next as any\n\n  const array = isPlainArray(prev) && isPlainArray(next)\n\n  if (array || (isPlainObject(prev) && isPlainObject(next))) {\n    const prevItems = array ? prev : Object.keys(prev)\n    const prevSize = prevItems.length\n    const nextItems = array ? next : Object.keys(next)\n    const nextSize = nextItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < nextSize; i++) {\n      const key = array ? i : nextItems[i]\n      if (\n        !array &&\n        prev[key] === undefined &&\n        next[key] === undefined &&\n        prevItems.includes(key)\n      ) {\n        copy[key] = undefined\n        equalItems++\n      } else {\n        copy[key] = replaceEqualDeep(prev[key], next[key])\n        if (copy[key] === prev[key] && prev[key] !== undefined) {\n          equalItems++\n        }\n      }\n    }\n\n    return prevSize === nextSize && equalItems === prevSize ? prev : copy\n  }\n\n  return next\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any) {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any) {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isPlainArray(value: unknown) {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\nexport function deepEqual(a: any, b: any, partial: boolean = false): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (isPlainObject(a) && isPlainObject(b)) {\n    const aKeys = Object.keys(a)\n    const bKeys = Object.keys(b)\n\n    if (!partial && aKeys.length !== bKeys.length) {\n      return false\n    }\n\n    return !bKeys.some(\n      (key) => !(key in a) || !deepEqual(a[key], b[key], partial),\n    )\n  }\n\n  if (Array.isArray(a) && Array.isArray(b)) {\n    return !a.some((item, index) => !deepEqual(item, b[index], partial))\n  }\n\n  return false\n}\n\nexport function useStableCallback<T extends (...args: any[]) => any>(fn: T): T {\n  const fnRef = React.useRef(fn)\n  fnRef.current = fn\n\n  const ref = React.useRef((...args: any[]) => fnRef.current(...args))\n  return ref.current as T\n}\n\nexport function shallow<T>(objA: T, objB: T) {\n  if (Object.is(objA, objB)) {\n    return true\n  }\n\n  if (\n    typeof objA !== 'object' ||\n    objA === null ||\n    typeof objB !== 'object' ||\n    objB === null\n  ) {\n    return false\n  }\n\n  const keysA = Object.keys(objA)\n  if (keysA.length !== Object.keys(objB).length) {\n    return false\n  }\n\n  for (let i = 0; i < keysA.length; i++) {\n    if (\n      !Object.prototype.hasOwnProperty.call(objB, keysA[i] as string) ||\n      !Object.is(objA[keysA[i] as keyof T], objB[keysA[i] as keyof T])\n    ) {\n      return false\n    }\n  }\n  return true\n}\n\nexport type StringLiteral<T> = T extends string\n  ? string extends T\n    ? string\n    : T\n  : never\n\nexport type StrictOrFrom<TFrom, TReturnIntersection extends boolean = false> =\n  | {\n      from: StringLiteral<TFrom> | TFrom\n      strict?: true\n    }\n  | {\n      from?: never\n      strict: false\n      experimental_returnIntersection?: TReturnIntersection\n    }\n\nexport const useLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect\n\nexport function escapeJSON(jsonString: string) {\n  return jsonString\n    .replace(/\\\\/g, '\\\\\\\\') // Escape backslashes\n    .replace(/'/g, \"\\\\'\") // Escape single quotes\n    .replace(/\"/g, '\\\\\"') // Escape double quotes\n}\n", "import * as React from 'react'\nimport { CatchBoundary } from './CatchBoundary'\nimport { useRouterState } from './useRouterState'\nimport { RegisteredRouter } from './router'\nimport { RouteIds } from './routeInfo'\n\nexport type NotFoundError = {\n  /**\n    @deprecated\n    Use `routeId: rootRouteId` instead\n  */\n  global?: boolean\n  /**\n    @private\n    Do not use this. It's used internally to indicate a path matching error\n  */\n  _global?: boolean\n  data?: any\n  throw?: boolean\n  routeId?: RouteIds<RegisteredRouter['routeTree']>\n}\n\nexport function notFound(options: NotFoundError = {}) {\n  ;(options as any).isNotFound = true\n  if (options.throw) throw options\n  return options\n}\n\nexport function isNotFound(obj: any): obj is NotFoundError {\n  return !!obj?.isNotFound\n}\n\nexport function CatchNotFound(props: {\n  fallback?: (error: NotFoundError) => React.ReactElement\n  onCatch?: (error: any) => void\n  children: React.ReactNode\n}) {\n  // TODO: Some way for the user to programmatically reset the not-found boundary?\n  const resetKey = useRouterState({\n    select: (s) => `not-found-${s.location.pathname}-${s.status}`,\n  })\n\n  return (\n    <CatchBoundary\n      getResetKey={() => resetKey}\n      onCatch={(error) => {\n        if (isNotFound(error)) {\n          props.onCatch?.(error)\n        } else {\n          throw error\n        }\n      }}\n      errorComponent={({ error }: { error: NotFoundError }) =>\n        props.fallback?.(error)\n      }\n    >\n      {props.children}\n    </CatchBoundary>\n  )\n}\n\nexport function DefaultGlobalNotFound() {\n  return <p>Not Found</p>\n}\n", "import { NavigateOptions } from './link'\nimport { AnyRoute } from './route'\nimport { RoutePaths } from './routeInfo'\nimport { RegisteredRouter } from './router'\n\n// Detect if we're in the DOM\n\nexport type AnyRedirect = Redirect<any, any, any, any, any>\n\nexport type Redirect<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  T<PERSON>rom extends RoutePaths<TRouteTree> = '/',\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> = TFrom,\n  TMaskTo extends string = '',\n> = {\n  code?: number\n  throw?: any\n  href?: string\n} & NavigateOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo>\n\nexport function redirect<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> = '/',\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> = TFrom,\n  TMaskTo extends string = '',\n>(\n  opts: Redirect<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo>,\n): Redirect<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo> {\n  ;(opts as any).isRedirect = true\n  if (opts.throw ?? true) {\n    throw opts\n  }\n\n  return opts\n}\n\nexport function isRedirect(obj: any): obj is AnyRedirect {\n  return !!obj?.isRedirect\n}\n", "import * as React from 'react'\nimport invariant from 'tiny-invariant'\nimport warning from 'tiny-warning'\nimport { CatchBoundary, ErrorComponent } from './CatchBoundary'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\nimport { ResolveRelativePath, ToOptions } from './link'\nimport {\n  AnyRoute,\n  ReactNode,\n  RootSearchSchema,\n  StaticDataRouteOption,\n} from './route'\nimport {\n  AllParams,\n  FullSearchSchema,\n  ParseRoute,\n  RouteById,\n  RouteByPath,\n  RouteIds,\n  RoutePaths,\n} from './routeInfo'\nimport { AnyRouter, RegisteredRouter, RouterState } from './router'\nimport {\n  DeepPartial,\n  Expand,\n  NoInfer,\n  StrictOrFrom,\n  isServer,\n  pick,\n} from './utils'\nimport { CatchNotFound, DefaultGlobalNotFound, isNotFound } from './not-found'\nimport { isRedirect } from './redirects'\n\nexport const matchContext = React.createContext<string | undefined>(undefined)\n\nexport interface RouteMatch<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TRouteId extends RouteIds<TRouteTree> = ParseRoute<TRouteTree>['id'],\n  TReturnIntersection extends boolean = false,\n> {\n  id: string\n  routeId: TRouteId\n  pathname: string\n  params: TReturnIntersection extends false\n    ? RouteById<TRouteTree, TRouteId>['types']['allParams']\n    : Expand<Partial<AllParams<TRouteTree>>>\n  status: 'pending' | 'success' | 'error' | 'redirected' | 'notFound'\n  isFetching: boolean\n  showPending: boolean\n  error: unknown\n  paramsError: unknown\n  searchError: unknown\n  updatedAt: number\n  loadPromise?: Promise<void>\n  loaderData?: RouteById<TRouteTree, TRouteId>['types']['loaderData']\n  routeContext: RouteById<TRouteTree, TRouteId>['types']['routeContext']\n  context: RouteById<TRouteTree, TRouteId>['types']['allContext']\n  search: TReturnIntersection extends false\n    ? Exclude<\n        RouteById<TRouteTree, TRouteId>['types']['fullSearchSchema'],\n        RootSearchSchema\n      >\n    : Expand<\n        Partial<Omit<FullSearchSchema<TRouteTree>, keyof RootSearchSchema>>\n      >\n  fetchCount: number\n  abortController: AbortController\n  cause: 'preload' | 'enter' | 'stay'\n  loaderDeps: RouteById<TRouteTree, TRouteId>['types']['loaderDeps']\n  preload: boolean\n  invalid: boolean\n  pendingPromise?: Promise<void>\n  meta?: JSX.IntrinsicElements['meta'][]\n  links?: JSX.IntrinsicElements['link'][]\n  scripts?: JSX.IntrinsicElements['script'][]\n  headers?: Record<string, string>\n  globalNotFound?: boolean\n  staticData: StaticDataRouteOption\n}\n\nexport type AnyRouteMatch = RouteMatch<any, any>\n\nexport function Matches() {\n  const router = useRouter()\n  const matchId = useRouterState({\n    select: (s) => {\n      return getRenderedMatches(s)[0]?.id\n    },\n  })\n\n  return (\n    <matchContext.Provider value={matchId}>\n      <CatchBoundary\n        getResetKey={() => router.state.resolvedLocation.state?.key!}\n        errorComponent={ErrorComponent}\n        onCatch={(error) => {\n          warning(\n            false,\n            `The following error wasn't caught by any route! 👇 At the very least, consider setting an 'errorComponent' in your RootRoute!`,\n          )\n          console.error(error)\n        }}\n      >\n        {matchId ? <Match matchId={matchId} /> : null}\n      </CatchBoundary>\n    </matchContext.Provider>\n  )\n}\n\nfunction SafeFragment(props: any) {\n  return <>{props.children}</>\n}\n\nexport function Match({ matchId }: { matchId: string }) {\n  const router = useRouter()\n  const routeId = useRouterState({\n    select: (s) =>\n      getRenderedMatches(s).find((d) => d.id === matchId)?.routeId as string,\n  })\n\n  invariant(\n    routeId,\n    `Could not find routeId for matchId \"${matchId}\". Please file an issue!`,\n  )\n\n  const route = router.routesById[routeId]!\n\n  const PendingComponent = (route.options.pendingComponent ??\n    router.options.defaultPendingComponent) as any\n\n  const pendingElement = PendingComponent ? <PendingComponent /> : null\n\n  const routeErrorComponent =\n    route.options.errorComponent ??\n    router.options.defaultErrorComponent ??\n    ErrorComponent\n\n  const routeNotFoundComponent = route.isRoot\n    ? // If it's the root route, use the globalNotFound option, with fallback to the notFoundRoute's component\n      route.options.notFoundComponent ??\n      router.options.notFoundRoute?.options.component\n    : route.options.notFoundComponent\n\n  const ResolvedSuspenseBoundary =\n    route.options.wrapInSuspense ??\n    PendingComponent ??\n    route.options.component?.preload ??\n    route.options.pendingComponent?.preload ??\n    (route.options.errorComponent as any)?.preload\n      ? React.Suspense\n      : SafeFragment\n\n  const ResolvedCatchBoundary = routeErrorComponent\n    ? CatchBoundary\n    : SafeFragment\n\n  const ResolvedNotFoundBoundary = routeNotFoundComponent\n    ? CatchNotFound\n    : SafeFragment\n\n  return (\n    <matchContext.Provider value={matchId}>\n      <ResolvedSuspenseBoundary fallback={pendingElement}>\n        <ResolvedCatchBoundary\n          getResetKey={() => router.state.resolvedLocation.state?.key!}\n          errorComponent={routeErrorComponent}\n          onCatch={(error) => {\n            // Forward not found errors (we don't want to show the error component for these)\n            if (isNotFound(error)) throw error\n            warning(false, `Error in route match: ${matchId}`)\n            console.error(error)\n          }}\n        >\n          <ResolvedNotFoundBoundary\n            fallback={(error) => {\n              // If the current not found handler doesn't exist or it has a\n              // route ID which doesn't match the current route, rethrow the error\n              if (\n                !routeNotFoundComponent ||\n                (error.routeId && error.routeId !== routeId) ||\n                (!error.routeId && !route.isRoot)\n              )\n                throw error\n\n              return React.createElement(routeNotFoundComponent, error as any)\n            }}\n          >\n            <MatchInner matchId={matchId!} pendingElement={pendingElement} />\n          </ResolvedNotFoundBoundary>\n        </ResolvedCatchBoundary>\n      </ResolvedSuspenseBoundary>\n    </matchContext.Provider>\n  )\n}\n\nfunction MatchInner({\n  matchId,\n  pendingElement,\n}: {\n  matchId: string\n  pendingElement: any\n}): any {\n  const router = useRouter()\n  const routeId = useRouterState({\n    select: (s) =>\n      getRenderedMatches(s).find((d) => d.id === matchId)?.routeId as string,\n  })\n\n  const route = router.routesById[routeId]!\n\n  const match = useRouterState({\n    select: (s) =>\n      pick(getRenderedMatches(s).find((d) => d.id === matchId)!, [\n        'status',\n        'error',\n        'showPending',\n        'loadPromise',\n      ]),\n  })\n\n  const RouteErrorComponent =\n    (route.options.errorComponent ?? router.options.defaultErrorComponent) ||\n    ErrorComponent\n\n  if (match.status === 'notFound') {\n    invariant(isNotFound(match.error), 'Expected a notFound error')\n\n    return renderRouteNotFound(router, route, match.error.data)\n  }\n\n  if (match.status === 'redirected') {\n    // Redirects should be handled by the router transition. If we happen to\n    // encounter a redirect here, it's a bug. Let's warn, but render nothing.\n    invariant(isRedirect(match.error), 'Expected a redirect error')\n\n    warning(\n      false,\n      'Tried to render a redirected route match! This is a weird circumstance, please file an issue!',\n    )\n\n    return null\n  }\n\n  if (match.status === 'error') {\n    // If we're on the server, we need to use React's new and super\n    // wonky api for throwing errors from a server side render inside\n    // of a suspense boundary. This is the only way to get\n    // renderToPipeableStream to not hang indefinitely.\n    // We'll serialize the error and rethrow it on the client.\n    if (isServer) {\n      return (\n        <RouteErrorComponent\n          error={match.error}\n          info={{\n            componentStack: '',\n          }}\n        />\n      )\n    }\n\n    if (isServerSideError(match.error)) {\n      const deserializeError =\n        router.options.errorSerializer?.deserialize ?? defaultDeserializeError\n      throw deserializeError(match.error.data)\n    } else {\n      throw match.error\n    }\n  }\n\n  if (match.status === 'pending') {\n    if (match.showPending) {\n      return pendingElement\n    }\n    throw match.loadPromise\n  }\n\n  if (match.status === 'success') {\n    let Comp = route.options.component ?? router.options.defaultComponent\n\n    if (Comp) {\n      return <Comp />\n    }\n\n    return <Outlet />\n  }\n\n  invariant(\n    false,\n    'Idle routeMatch status encountered during rendering! You should never see this. File an issue!',\n  )\n}\n\nexport const Outlet = React.memo(function Outlet() {\n  const router = useRouter()\n  const matchId = React.useContext(matchContext)\n  const routeId = useRouterState({\n    select: (s) =>\n      getRenderedMatches(s).find((d) => d.id === matchId)?.routeId as string,\n  })\n\n  const route = router.routesById[routeId]!\n\n  const { parentGlobalNotFound } = useRouterState({\n    select: (s) => {\n      const matches = getRenderedMatches(s)\n      const parentMatch = matches.find((d) => d.id === matchId)\n      invariant(\n        parentMatch,\n        `Could not find parent match for matchId \"${matchId}\"`,\n      )\n      return {\n        parentGlobalNotFound: parentMatch.globalNotFound,\n      }\n    },\n  })\n\n  const childMatchId = useRouterState({\n    select: (s) => {\n      const matches = getRenderedMatches(s)\n      const index = matches.findIndex((d) => d.id === matchId)\n      return matches[index + 1]?.id\n    },\n  })\n\n  if (parentGlobalNotFound) {\n    return renderRouteNotFound(router, route, undefined)\n  }\n\n  if (!childMatchId) {\n    return null\n  }\n\n  return <Match matchId={childMatchId} />\n})\n\nfunction renderRouteNotFound(router: AnyRouter, route: AnyRoute, data: any) {\n  if (!route.options.notFoundComponent) {\n    if (router.options.defaultNotFoundComponent) {\n      return <router.options.defaultNotFoundComponent data={data} />\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      warning(\n        route.options.notFoundComponent,\n        `A notFoundError was encountered on the route with ID \"${route.id}\", but a notFoundComponent option was not configured, nor was a router level defaultNotFoundComponent configured. Consider configuring at least one of these to avoid TanStack Router's overly generic defaultNotFoundComponent (<div>Not Found<div>)`,\n      )\n    }\n\n    return <DefaultGlobalNotFound />\n  }\n\n  return <route.options.notFoundComponent data={data} />\n}\n\nexport interface MatchRouteOptions {\n  pending?: boolean\n  caseSensitive?: boolean\n  includeSearch?: boolean\n  fuzzy?: boolean\n}\n\nexport type UseMatchRouteOptions<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> = RoutePaths<TRouteTree>,\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> = TFrom,\n  TMaskTo extends string = '',\n  Options extends ToOptions<\n    TRouteTree,\n    TFrom,\n    TTo,\n    TMaskFrom,\n    TMaskTo\n  > = ToOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo>,\n  RelaxedOptions = Omit<Options, 'search' | 'params'> &\n    DeepPartial<Pick<Options, 'search' | 'params'>>,\n> = RelaxedOptions & MatchRouteOptions\n\nexport function useMatchRoute<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n>() {\n  useRouterState({ select: (s) => [s.location, s.resolvedLocation] })\n  const { matchRoute } = useRouter()\n\n  return React.useCallback(\n    <\n      TFrom extends RoutePaths<TRouteTree> = RoutePaths<TRouteTree>,\n      TTo extends string = '',\n      TMaskFrom extends RoutePaths<TRouteTree> = TFrom,\n      TMaskTo extends string = '',\n      TResolved extends string = ResolveRelativePath<TFrom, NoInfer<TTo>>,\n    >(\n      opts: UseMatchRouteOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo>,\n    ): false | RouteById<TRouteTree, TResolved>['types']['allParams'] => {\n      const { pending, caseSensitive, fuzzy, includeSearch, ...rest } = opts\n\n      return matchRoute(rest as any, {\n        pending,\n        caseSensitive,\n        fuzzy,\n        includeSearch,\n      })\n    },\n    [],\n  )\n}\n\nexport type MakeMatchRouteOptions<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> = RoutePaths<TRouteTree>,\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> = TFrom,\n  TMaskTo extends string = '',\n> = UseMatchRouteOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo> & {\n  // If a function is passed as a child, it will be given the `isActive` boolean to aid in further styling on the element it returns\n  children?:\n    | ((\n        params?: RouteByPath<\n          TRouteTree,\n          ResolveRelativePath<TFrom, NoInfer<TTo>>\n        >['types']['allParams'],\n      ) => ReactNode)\n    | React.ReactNode\n}\n\nexport function MatchRoute<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> = RoutePaths<TRouteTree>,\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> = TFrom,\n  TMaskTo extends string = '',\n>(\n  props: MakeMatchRouteOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo>,\n): any {\n  const matchRoute = useMatchRoute()\n  const params = matchRoute(props as any)\n\n  if (typeof props.children === 'function') {\n    return (props.children as any)(params)\n  }\n\n  return !!params ? props.children : null\n}\n\nexport function getRenderedMatches<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n>(state: RouterState<TRouteTree>) {\n  return state.pendingMatches?.some((d) => d.showPending)\n    ? state.pendingMatches\n    : state.matches\n}\n\nexport function useMatch<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RouteIds<TRouteTree> = RouteIds<TRouteTree>,\n  TReturnIntersection extends boolean = false,\n  TRouteMatchState = RouteMatch<TRouteTree, TFrom, TReturnIntersection>,\n  TSelected = TRouteMatchState,\n>(\n  opts: StrictOrFrom<TFrom, TReturnIntersection> & {\n    select?: (match: TRouteMatchState) => TSelected\n  },\n): TSelected {\n  const router = useRouter()\n  const nearestMatchId = React.useContext(matchContext)\n\n  const nearestMatchRouteId = getRenderedMatches(router.state).find(\n    (d) => d.id === nearestMatchId,\n  )?.routeId\n\n  const matchRouteId = (() => {\n    const matches = getRenderedMatches(router.state)\n    const match = opts?.from\n      ? matches.find((d) => d.routeId === opts?.from)\n      : matches.find((d) => d.id === nearestMatchId)\n    return match!.routeId\n  })()\n\n  if (opts?.strict ?? true) {\n    invariant(\n      nearestMatchRouteId == matchRouteId,\n      `useMatch(\"${\n        matchRouteId as string\n      }\") is being called in a component that is meant to render the '${nearestMatchRouteId}' route. Did you mean to 'useMatch(\"${\n        matchRouteId as string\n      }\", { strict: false })' or 'useRoute(\"${\n        matchRouteId as string\n      }\")' instead?`,\n    )\n  }\n\n  const matchSelection = useRouterState({\n    select: (state) => {\n      const match = getRenderedMatches(state).find((d) =>\n        opts?.from ? opts?.from === d.routeId : d.id === nearestMatchId,\n      )\n\n      invariant(\n        match,\n        `Could not find ${\n          opts?.from\n            ? `an active match from \"${opts.from}\"`\n            : 'a nearest match!'\n        }`,\n      )\n\n      return opts?.select ? opts.select(match as any) : match\n    },\n  })\n\n  return matchSelection as any\n}\n\nexport function useMatches<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TRouteId extends RouteIds<TRouteTree> = ParseRoute<TRouteTree>['id'],\n  TReturnIntersection extends boolean = false,\n  TRouteMatch = RouteMatch<TRouteTree, TRouteId, TReturnIntersection>,\n  T = TRouteMatch[],\n>(opts?: {\n  select?: (matches: TRouteMatch[]) => T\n  experimental_returnIntersection?: TReturnIntersection\n}): T {\n  return useRouterState({\n    select: (state) => {\n      const matches = getRenderedMatches(state)\n      return opts?.select\n        ? opts.select(matches as TRouteMatch[])\n        : (matches as T)\n    },\n  })\n}\n\nexport function useParentMatches<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TRouteId extends RouteIds<TRouteTree> = ParseRoute<TRouteTree>['id'],\n  TReturnIntersection extends boolean = false,\n  TRouteMatch = RouteMatch<TRouteTree, TRouteId, TReturnIntersection>,\n  T = TRouteMatch[],\n>(opts?: {\n  select?: (matches: TRouteMatch[]) => T\n  experimental_returnIntersection?: TReturnIntersection\n}): T {\n  const contextMatchId = React.useContext(matchContext)\n\n  return useMatches({\n    select: (matches) => {\n      matches = matches.slice(\n        0,\n        matches.findIndex((d) => d.id === contextMatchId),\n      )\n      return opts?.select\n        ? opts.select(matches as TRouteMatch[])\n        : (matches as T)\n    },\n  })\n}\n\nexport function useChildMatches<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TRouteId extends RouteIds<TRouteTree> = ParseRoute<TRouteTree>['id'],\n  TReturnIntersection extends boolean = false,\n  TRouteMatch = RouteMatch<TRouteTree, TRouteId, TReturnIntersection>,\n  T = TRouteMatch[],\n>(opts?: {\n  select?: (matches: TRouteMatch[]) => T\n  experimental_returnIntersection?: TReturnIntersection\n}): T {\n  const contextMatchId = React.useContext(matchContext)\n\n  return useMatches({\n    select: (matches) => {\n      matches = matches.slice(\n        matches.findIndex((d) => d.id === contextMatchId) + 1,\n      )\n      return opts?.select\n        ? opts.select(matches as TRouteMatch[])\n        : (matches as T)\n    },\n  })\n}\n\nexport function useLoaderDeps<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RouteIds<TRouteTree> = RouteIds<TRouteTree>,\n  TRouteMatch extends RouteMatch<TRouteTree, TFrom> = RouteMatch<\n    TRouteTree,\n    TFrom\n  >,\n  TSelected = Required<TRouteMatch>['loaderDeps'],\n>(\n  opts: StrictOrFrom<TFrom> & {\n    select?: (match: TRouteMatch) => TSelected\n  },\n): TSelected {\n  return useMatch({\n    ...opts,\n    select: (s) => {\n      return typeof opts.select === 'function'\n        ? opts.select(s?.loaderDeps)\n        : s?.loaderDeps\n    },\n  })\n}\n\nexport function useLoaderData<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RouteIds<TRouteTree> = RouteIds<TRouteTree>,\n  TRouteMatch extends RouteMatch<TRouteTree, TFrom> = RouteMatch<\n    TRouteTree,\n    TFrom\n  >,\n  TSelected = Required<TRouteMatch>['loaderData'],\n>(\n  opts: StrictOrFrom<TFrom> & {\n    select?: (match: TRouteMatch) => TSelected\n  },\n): TSelected {\n  return useMatch({\n    ...opts,\n    select: (s) => {\n      return typeof opts.select === 'function'\n        ? opts.select(s?.loaderData)\n        : s?.loaderData\n    },\n  })\n}\n\nexport function isServerSideError(error: unknown): error is {\n  __isServerError: true\n  data: Record<string, any>\n} {\n  if (!(typeof error === 'object' && error && 'data' in error)) return false\n  if (!('__isServerError' in error && error.__isServerError)) return false\n  if (!(typeof error.data === 'object' && error.data)) return false\n\n  return error.__isServerError === true\n}\n\nexport function defaultDeserializeError(serializedData: Record<string, any>) {\n  if ('name' in serializedData && 'message' in serializedData) {\n    const error = new Error(serializedData.message)\n    error.name = serializedData.name\n    return error\n  }\n\n  return serializedData.data\n}\n", "import { MatchLocation } from './RouterProvider'\nimport { AnyPathParams } from './route'\nimport { last } from './utils'\n\nexport interface Segment {\n  type: 'pathname' | 'param' | 'wildcard'\n  value: string\n}\n\nexport function joinPaths(paths: (string | undefined)[]) {\n  return cleanPath(paths.filter(Boolean).join('/'))\n}\n\nexport function cleanPath(path: string) {\n  // remove double slashes\n  return path.replace(/\\/{2,}/g, '/')\n}\n\nexport function trimPathLeft(path: string) {\n  return path === '/' ? path : path.replace(/^\\/{1,}/, '')\n}\n\nexport function trimPathRight(path: string) {\n  return path === '/' ? path : path.replace(/\\/{1,}$/, '')\n}\n\nexport function trimPath(path: string) {\n  return trimPathRight(trimPathLeft(path))\n}\n\nexport function resolvePath(basepath: string, base: string, to: string) {\n  base = base.replace(new RegExp(`^${basepath}`), '/')\n  to = to.replace(new RegExp(`^${basepath}`), '/')\n\n  let baseSegments = parsePathname(base)\n  const toSegments = parsePathname(to)\n\n  toSegments.forEach((toSegment, index) => {\n    if (toSegment.value === '/') {\n      if (!index) {\n        // Leading slash\n        baseSegments = [toSegment]\n      } else if (index === toSegments.length - 1) {\n        // Trailing Slash\n        baseSegments.push(toSegment)\n      } else {\n        // ignore inter-slashes\n      }\n    } else if (toSegment.value === '..') {\n      // Extra trailing slash? pop it off\n      if (baseSegments.length > 1 && last(baseSegments)?.value === '/') {\n        baseSegments.pop()\n      }\n      baseSegments.pop()\n    } else if (toSegment.value === '.') {\n      return\n    } else {\n      baseSegments.push(toSegment)\n    }\n  })\n\n  const joined = joinPaths([basepath, ...baseSegments.map((d) => d.value)])\n\n  return cleanPath(joined)\n}\n\nexport function parsePathname(pathname?: string): Segment[] {\n  if (!pathname) {\n    return []\n  }\n\n  pathname = cleanPath(pathname)\n\n  const segments: Segment[] = []\n\n  if (pathname.slice(0, 1) === '/') {\n    pathname = pathname.substring(1)\n    segments.push({\n      type: 'pathname',\n      value: '/',\n    })\n  }\n\n  if (!pathname) {\n    return segments\n  }\n\n  // Remove empty segments and '.' segments\n  const split = pathname.split('/').filter(Boolean)\n\n  segments.push(\n    ...split.map((part): Segment => {\n      if (part === '$' || part === '*') {\n        return {\n          type: 'wildcard',\n          value: part,\n        }\n      }\n\n      if (part.charAt(0) === '$') {\n        return {\n          type: 'param',\n          value: part,\n        }\n      }\n\n      return {\n        type: 'pathname',\n        value: part,\n      }\n    }),\n  )\n\n  if (pathname.slice(-1) === '/') {\n    pathname = pathname.substring(1)\n    segments.push({\n      type: 'pathname',\n      value: '/',\n    })\n  }\n\n  return segments\n}\n\ninterface InterpolatePathOptions {\n  path?: string\n  params: any\n  leaveWildcards?: boolean\n  leaveParams?: boolean\n}\nexport function interpolatePath({\n  path,\n  params,\n  leaveWildcards,\n  leaveParams,\n}: InterpolatePathOptions) {\n  const interpolatedPathSegments = parsePathname(path)\n\n  return joinPaths(\n    interpolatedPathSegments.map((segment) => {\n      if (segment.type === 'wildcard') {\n        const value = params._splat\n        if (leaveWildcards) return `${segment.value}${value ?? ''}`\n        return value\n      }\n\n      if (segment.type === 'param') {\n        if (leaveParams) {\n          const value = params[segment.value]\n          return `${segment.value}${value ?? ''}`\n        }\n        return params![segment.value.substring(1)] ?? 'undefined'\n      }\n\n      return segment.value\n    }),\n  )\n}\n\nexport function matchPathname(\n  basepath: string,\n  currentPathname: string,\n  matchLocation: Pick<MatchLocation, 'to' | 'fuzzy' | 'caseSensitive'>,\n): AnyPathParams | undefined {\n  const pathParams = matchByPath(basepath, currentPathname, matchLocation)\n  // const searchMatched = matchBySearch(location.search, matchLocation)\n\n  if (matchLocation.to && !pathParams) {\n    return\n  }\n\n  return pathParams ?? {}\n}\n\nexport function removeBasepath(basepath: string, pathname: string) {\n  return basepath != '/' ? pathname.replace(basepath, '') : pathname\n}\n\nexport function matchByPath(\n  basepath: string,\n  from: string,\n  matchLocation: Pick<MatchLocation, 'to' | 'caseSensitive' | 'fuzzy'>,\n): Record<string, string> | undefined {\n  // Remove the base path from the pathname\n  from = removeBasepath(basepath, from)\n  // Default to to $ (wildcard)\n  const to = removeBasepath(basepath, `${matchLocation.to ?? '$'}`)\n\n  // Parse the from and to\n  const baseSegments = parsePathname(from)\n  const routeSegments = parsePathname(to)\n\n  if (!from.startsWith('/')) {\n    baseSegments.unshift({\n      type: 'pathname',\n      value: '/',\n    })\n  }\n\n  if (!to.startsWith('/')) {\n    routeSegments.unshift({\n      type: 'pathname',\n      value: '/',\n    })\n  }\n\n  const params: Record<string, string> = {}\n\n  let isMatch = (() => {\n    for (\n      let i = 0;\n      i < Math.max(baseSegments.length, routeSegments.length);\n      i++\n    ) {\n      const baseSegment = baseSegments[i]\n      const routeSegment = routeSegments[i]\n\n      const isLastBaseSegment = i >= baseSegments.length - 1\n      const isLastRouteSegment = i >= routeSegments.length - 1\n\n      if (routeSegment) {\n        if (routeSegment.type === 'wildcard') {\n          if (baseSegment?.value) {\n            const _splat = decodeURI(\n              joinPaths(baseSegments.slice(i).map((d) => d.value)),\n            )\n            // TODO: Deprecate *\n            params['*'] = _splat\n            params['_splat'] = _splat\n            return true\n          }\n          return false\n        }\n\n        if (routeSegment.type === 'pathname') {\n          if (routeSegment.value === '/' && !baseSegment?.value) {\n            return true\n          }\n\n          if (baseSegment) {\n            if (matchLocation.caseSensitive) {\n              if (routeSegment.value !== baseSegment.value) {\n                return false\n              }\n            } else if (\n              routeSegment.value.toLowerCase() !==\n              baseSegment.value.toLowerCase()\n            ) {\n              return false\n            }\n          }\n        }\n\n        if (!baseSegment) {\n          return false\n        }\n\n        if (routeSegment.type === 'param') {\n          if (baseSegment?.value === '/') {\n            return false\n          }\n          if (baseSegment.value.charAt(0) !== '$') {\n            params[routeSegment.value.substring(1)] = decodeURI(\n              baseSegment.value,\n            )\n          }\n        }\n      }\n\n      if (!isLastBaseSegment && isLastRouteSegment) {\n        params['**'] = joinPaths(baseSegments.slice(i + 1).map((d) => d.value))\n        return !!matchLocation.fuzzy && routeSegment?.value !== '/'\n      }\n    }\n\n    return true\n  })()\n\n  return isMatch ? (params as Record<string, string>) : undefined\n}\n", "import { AnyRoute } from './route'\nimport { RouteIds, RouteById, AllParams } from './routeInfo'\nimport { RegisteredRouter } from './router'\nimport { Expand, last } from './utils'\nimport { useRouterState } from './useRouterState'\nimport { StrictOrFrom } from './utils'\nimport { getRenderedMatches } from './Matches'\n\nexport function useParams<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RouteIds<TRouteTree> = RouteIds<TRouteTree>,\n  TReturnIntersection extends boolean = false,\n  TParams = TReturnIntersection extends false\n    ? RouteById<TRouteTree, TFrom>['types']['allParams']\n    : Expand<Partial<AllParams<TRouteTree>>>,\n  TSelected = TParams,\n>(\n  opts: StrictOrFrom<TFrom, TReturnIntersection> & {\n    select?: (params: TParams) => TSelected\n  },\n): TSelected {\n  return useRouterState({\n    select: (state: any) => {\n      const params = (last(getRenderedMatches(state)) as any)?.params\n      return opts?.select ? opts.select(params) : params\n    },\n  })\n}\n", "import { AnyRoute, RootSearchSchema } from './route'\nimport { RouteIds, RouteById, FullSearchSchema } from './routeInfo'\nimport { RegisteredRouter } from './router'\nimport { RouteMatch } from './Matches'\nimport { useMatch } from './Matches'\nimport { Expand, StrictOrFrom } from './utils'\n\nexport function useSearch<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  T<PERSON>rom extends RouteIds<TRouteTree> = RouteIds<TRouteTree>,\n  TReturnIntersection extends boolean = false,\n  TSearch = TReturnIntersection extends false\n    ? Exclude<\n        RouteById<TRouteTree, TFrom>['types']['fullSearchSchema'],\n        RootSearchSchema\n      >\n    : Partial<Omit<FullSearchSchema<TRouteTree>, keyof RootSearchSchema>>,\n  TSelected = TSearch,\n>(\n  opts: StrictOrFrom<TFrom, TReturnIntersection> & {\n    select?: (search: TSearch) => TSelected\n  },\n): TSelected {\n  return useMatch({\n    ...opts,\n    select: (match: RouteMatch) => {\n      return opts?.select ? opts.select(match.search as TSearch) : match.search\n    },\n  })\n}\n", "import * as React from 'react'\nimport invariant from 'tiny-invariant'\nimport { useLoaderData, useLoaderDeps, useMatch, RouteMatch } from './Matches'\nimport { AnyRouteMatch } from './Matches'\nimport { NavigateOptions, ParsePathParams, ToSubOptions } from './link'\nimport { ParsedLocation } from './location'\nimport { joinPaths, trimPathLeft } from './path'\nimport { RouteById, RouteIds, RoutePaths } from './routeInfo'\nimport { AnyRouter, RegisteredRouter } from './router'\nimport { useParams } from './useParams'\nimport { useSearch } from './useSearch'\nimport {\n  Assign,\n  Expand,\n  IsAny,\n  NoInfer,\n  PickRequired,\n  UnionToIntersection,\n} from './utils'\nimport { BuildLocationFn, NavigateFn } from './RouterProvider'\nimport { NotFoundError, notFound } from './not-found'\nimport { LazyRoute } from './fileRoute'\n\nexport const rootRouteId = '__root__' as const\nexport type RootRouteId = typeof rootRouteId\nexport type AnyPathParams = {}\n\nexport type SearchSchemaInput = {\n  __TSearchSchemaInput__: 'TSearchSchemaInput'\n}\n\nexport type AnySearchSchema = {}\n\nexport type AnyContext = {}\n\nexport interface RouteContext {}\n\nexport type PreloadableObj = { preload?: () => Promise<void> }\n\nexport type RoutePathOptions<TCustomId, TPath> =\n  | {\n      path: TPath\n    }\n  | {\n      id: TCustomId\n    }\n\nexport interface StaticDataRouteOption {}\n\nexport type RoutePathOptionsIntersection<TCustomId, TPath> =\n  UnionToIntersection<RoutePathOptions<TCustomId, TPath>>\n\nexport type RouteOptions<\n  TParentRoute extends AnyRoute = AnyRoute,\n  TCustomId extends string = string,\n  TPath extends string = string,\n  TSearchSchemaInput extends Record<string, any> = {},\n  TSearchSchema extends Record<string, any> = {},\n  TSearchSchemaUsed extends Record<string, any> = {},\n  TFullSearchSchemaInput extends Record<string, any> = TSearchSchemaUsed,\n  TFullSearchSchema extends Record<string, any> = TSearchSchema,\n  TParams extends AnyPathParams = AnyPathParams,\n  TAllParams extends AnyPathParams = TParams,\n  TRouteContextReturn extends RouteContext = RouteContext,\n  TRouteContext extends RouteContext = RouteContext,\n  TRouterContext extends RouteConstraints['TRouterContext'] = AnyContext,\n  TAllContext extends Record<string, any> = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderDataReturn extends any = unknown,\n  TLoaderData extends any = [TLoaderDataReturn] extends [never]\n    ? undefined\n    : TLoaderDataReturn,\n> = BaseRouteOptions<\n  TParentRoute,\n  TCustomId,\n  TPath,\n  TSearchSchemaInput,\n  TSearchSchema,\n  TSearchSchemaUsed,\n  TFullSearchSchemaInput,\n  TFullSearchSchema,\n  TParams,\n  TAllParams,\n  TRouteContextReturn,\n  TRouteContext,\n  TRouterContext,\n  TAllContext,\n  TLoaderDeps,\n  TLoaderDataReturn,\n  TLoaderData\n> &\n  UpdatableRouteOptions<\n    NoInfer<TAllParams>,\n    NoInfer<TFullSearchSchema>,\n    NoInfer<TLoaderData>\n  >\n\nexport type ParamsFallback<\n  TPath extends string,\n  TParams,\n> = unknown extends TParams ? Record<ParsePathParams<TPath>, string> : TParams\n\nexport type BaseRouteOptions<\n  TParentRoute extends AnyRoute = AnyRoute,\n  TCustomId extends string = string,\n  TPath extends string = string,\n  TSearchSchemaInput extends Record<string, any> = {},\n  TSearchSchema extends Record<string, any> = {},\n  TSearchSchemaUsed extends Record<string, any> = {},\n  TFullSearchSchemaInput extends Record<string, any> = TSearchSchemaUsed,\n  TFullSearchSchema extends Record<string, any> = TSearchSchema,\n  TParams extends AnyPathParams = {},\n  TAllParams = ParamsFallback<TPath, TParams>,\n  TRouteContextReturn extends RouteContext = RouteContext,\n  TRouteContext extends RouteContext = RouteContext,\n  TRouterContext extends RouteConstraints['TRouterContext'] = AnyContext,\n  TAllContext extends Record<string, any> = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderDataReturn extends any = unknown,\n  TLoaderData extends any = [TLoaderDataReturn] extends [never]\n    ? undefined\n    : TLoaderDataReturn,\n> = RoutePathOptions<TCustomId, TPath> & {\n  getParentRoute: () => TParentRoute\n  validateSearch?: SearchSchemaValidator<TSearchSchemaInput, TSearchSchema>\n  shouldReload?:\n    | boolean\n    | ((\n        match: LoaderFnContext<\n          TAllParams,\n          TFullSearchSchema,\n          TAllContext,\n          TRouteContext\n        >,\n      ) => any)\n} & {\n  // This async function is called before a route is loaded.\n  // If an error is thrown here, the route's loader will not be called.\n  // If thrown during a navigation, the navigation will be cancelled and the error will be passed to the `onError` function.\n  // If thrown during a preload event, the error will be logged to the console.\n  beforeLoad?: BeforeLoadFn<\n    TFullSearchSchema,\n    TParentRoute,\n    TAllParams,\n    TRouteContextReturn,\n    TRouterContext\n  >\n} & {\n  loaderDeps?: (opts: { search: TFullSearchSchema }) => TLoaderDeps\n  loader?: RouteLoaderFn<\n    TAllParams,\n    NoInfer<TLoaderDeps>,\n    NoInfer<TAllContext>,\n    NoInfer<TRouteContext>,\n    TLoaderDataReturn\n  >\n} & (\n    | {\n        // Both or none\n        parseParams?: (\n          rawParams: IsAny<TPath, any, Record<ParsePathParams<TPath>, string>>,\n        ) => TParams extends Record<ParsePathParams<TPath>, any>\n          ? TParams\n          : 'parseParams must return an object'\n        stringifyParams?: (\n          params: NoInfer<ParamsFallback<TPath, TParams>>,\n        ) => Record<ParsePathParams<TPath>, string>\n      }\n    | {\n        stringifyParams?: never\n        parseParams?: never\n      }\n  )\n\ntype BeforeLoadFn<\n  TFullSearchSchema extends Record<string, any>,\n  TParentRoute extends AnyRoute,\n  TAllParams,\n  TRouteContextReturn extends RouteContext,\n  TRouterContext extends RouteConstraints['TRouterContext'] = AnyContext,\n  TContext = IsAny<TParentRoute['types']['allContext'], TRouterContext>,\n> = (opts: {\n  search: TFullSearchSchema\n  abortController: AbortController\n  preload: boolean\n  params: TAllParams\n  context: TContext\n  location: ParsedLocation\n  navigate: NavigateFn\n  buildLocation: BuildLocationFn<TParentRoute>\n  cause: 'preload' | 'enter' | 'stay'\n}) => Promise<TRouteContextReturn> | TRouteContextReturn | void\n\nexport type UpdatableRouteOptions<\n  TAllParams extends Record<string, any>,\n  TFullSearchSchema extends Record<string, any>,\n  TLoaderData extends any,\n> = {\n  // test?: (args: TAllContext) => void\n  // If true, this route will be matched as case-sensitive\n  caseSensitive?: boolean\n  // If true, this route will be forcefully wrapped in a suspense boundary\n  wrapInSuspense?: boolean\n  // The content to be rendered when the route is matched. If no component is provided, defaults to `<Outlet />`\n  component?: RouteComponent\n  errorComponent?: false | null | ErrorRouteComponent\n  notFoundComponent?: NotFoundRouteComponent\n  pendingComponent?: RouteComponent\n  pendingMs?: number\n  pendingMinMs?: number\n  staleTime?: number\n  gcTime?: number\n  preloadStaleTime?: number\n  preloadGcTime?: number\n  // Filter functions that can manipulate search params *before* they are passed to links and navigate\n  // calls that match this route.\n  preSearchFilters?: SearchFilter<TFullSearchSchema>[]\n  // Filter functions that can manipulate search params *after* they are passed to links and navigate\n  // calls that match this route.\n  postSearchFilters?: SearchFilter<TFullSearchSchema>[]\n  onError?: (err: any) => void\n  // These functions are called as route matches are loaded, stick around and leave the active\n  // matches\n  onEnter?: (match: AnyRouteMatch) => void\n  onStay?: (match: AnyRouteMatch) => void\n  onLeave?: (match: AnyRouteMatch) => void\n  meta?: (ctx: {\n    params: TAllParams\n    loaderData: TLoaderData\n  }) =>\n    | JSX.IntrinsicElements['meta'][]\n    | Promise<JSX.IntrinsicElements['meta'][]>\n  links?: () => JSX.IntrinsicElements['link'][]\n  scripts?: () => JSX.IntrinsicElements['script'][]\n  headers?: (ctx: {\n    loaderData: TLoaderData\n  }) => Promise<Record<string, string>> | Record<string, string>\n} & UpdatableStaticRouteOption\n\nexport type UpdatableStaticRouteOption =\n  {} extends PickRequired<StaticDataRouteOption>\n    ? {\n        staticData?: StaticDataRouteOption\n      }\n    : {\n        staticData: StaticDataRouteOption\n      }\n\nexport type MetaDescriptor =\n  | { charSet: 'utf-8' }\n  | { title: string }\n  | { name: string; content: string }\n  | { property: string; content: string }\n  | { httpEquiv: string; content: string }\n  | { 'script:ld+json': LdJsonObject }\n  | { tagName: 'meta' | 'link'; [name: string]: string }\n  | { [name: string]: unknown }\n\ntype LdJsonObject = { [Key in string]: LdJsonValue } & {\n  [Key in string]?: LdJsonValue | undefined\n}\ntype LdJsonArray = LdJsonValue[] | readonly LdJsonValue[]\ntype LdJsonPrimitive = string | number | boolean | null\ntype LdJsonValue = LdJsonPrimitive | LdJsonObject | LdJsonArray\n\nexport type RouteLinkEntry = {}\n\nexport type ParseParamsOption<TPath extends string, TParams> = ParseParamsFn<\n  TPath,\n  TParams\n>\n\nexport type ParseParamsFn<TPath extends string, TParams> = (\n  rawParams: IsAny<TPath, any, Record<ParsePathParams<TPath>, string>>,\n) => TParams extends Record<ParsePathParams<TPath>, any>\n  ? TParams\n  : 'parseParams must return an object'\n\nexport type ParseParamsObj<TPath extends string, TParams> = {\n  parse?: ParseParamsFn<TPath, TParams>\n}\n\n// The parse type here allows a zod schema to be passed directly to the validator\nexport type SearchSchemaValidator<TInput, TReturn> =\n  | SearchSchemaValidatorObj<TInput, TReturn>\n  | SearchSchemaValidatorFn<TInput, TReturn>\n\nexport type SearchSchemaValidatorObj<TInput, TReturn> = {\n  parse?: SearchSchemaValidatorFn<TInput, TReturn>\n}\n\nexport type SearchSchemaValidatorFn<TInput, TReturn> = (\n  searchObj: TInput,\n) => TReturn\n\nexport type RouteLoaderFn<\n  TAllParams = {},\n  TLoaderDeps extends Record<string, any> = {},\n  TAllContext extends Record<string, any> = AnyContext,\n  TRouteContext extends Record<string, any> = AnyContext,\n  TLoaderData extends any = unknown,\n> = (\n  match: LoaderFnContext<TAllParams, TLoaderDeps, TAllContext, TRouteContext>,\n) => Promise<TLoaderData> | TLoaderData | void\n\nexport interface LoaderFnContext<\n  TAllParams = {},\n  TLoaderDeps extends Record<string, any> = {},\n  TAllContext extends Record<string, any> = AnyContext,\n  TRouteContext extends Record<string, any> = AnyContext,\n> {\n  abortController: AbortController\n  preload: boolean\n  params: TAllParams\n  deps: TLoaderDeps\n  context: Expand<Assign<TAllContext, TRouteContext>>\n  location: ParsedLocation // Do not supply search schema here so as to demotivate people from trying to shortcut loaderDeps\n  navigate: (opts: NavigateOptions<AnyRoute>) => Promise<void>\n  parentMatchPromise?: Promise<void>\n  cause: 'preload' | 'enter' | 'stay'\n  route: Route\n}\n\nexport type SearchFilter<T, U = T> = (prev: T) => U\n\nexport type ResolveId<\n  TParentRoute,\n  TCustomId extends string,\n  TPath extends string,\n> = TParentRoute extends { id: infer TParentId extends string }\n  ? RoutePrefix<TParentId, string extends TCustomId ? TPath : TCustomId>\n  : RootRouteId\n\nexport type InferFullSearchSchema<TRoute> = TRoute extends {\n  types: {\n    fullSearchSchema: infer TFullSearchSchema\n  }\n}\n  ? TFullSearchSchema\n  : {}\n\nexport type InferFullSearchSchemaInput<TRoute> = TRoute extends {\n  types: {\n    fullSearchSchemaInput: infer TFullSearchSchemaInput\n  }\n}\n  ? TFullSearchSchemaInput\n  : {}\n\nexport type ResolveFullSearchSchema<TParentRoute, TSearchSchema> = Expand<\n  Assign<\n    Omit<InferFullSearchSchema<TParentRoute>, keyof RootSearchSchema>,\n    TSearchSchema\n  >\n>\n\nexport type ResolveFullSearchSchemaInput<TParentRoute, TSearchSchemaUsed> =\n  Expand<\n    Assign<\n      Omit<InferFullSearchSchemaInput<TParentRoute>, keyof RootSearchSchema>,\n      TSearchSchemaUsed\n    >\n  >\n\nexport interface AnyRoute\n  extends Route<\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any,\n    any\n  > {}\n\nexport type MergeFromFromParent<T, U> = IsAny<T, U, T & U>\n\nexport type ResolveAllParams<\n  TParentRoute extends AnyRoute,\n  TParams extends AnyPathParams,\n> =\n  Record<never, string> extends TParentRoute['types']['allParams']\n    ? TParams\n    : Expand<\n        UnionToIntersection<TParentRoute['types']['allParams'] & TParams> & {}\n      >\n\nexport type RouteConstraints = {\n  TParentRoute: AnyRoute\n  TPath: string\n  TFullPath: string\n  TCustomId: string\n  TId: string\n  TSearchSchema: AnySearchSchema\n  TFullSearchSchema: AnySearchSchema\n  TParams: Record<string, any>\n  TAllParams: Record<string, any>\n  TParentContext: AnyContext\n  TRouteContext: RouteContext\n  TAllContext: AnyContext\n  TRouterContext: AnyContext\n  TChildren: unknown\n  TRouteTree: AnyRoute\n}\n\nexport function getRouteApi<\n  TId extends RouteIds<RegisteredRouter['routeTree']>,\n  TRoute extends AnyRoute = RouteById<RegisteredRouter['routeTree'], TId>,\n  TFullSearchSchema extends Record<\n    string,\n    any\n  > = TRoute['types']['fullSearchSchema'],\n  TAllParams extends AnyPathParams = TRoute['types']['allParams'],\n  TAllContext extends Record<string, any> = TRoute['types']['allContext'],\n  TLoaderDeps extends Record<string, any> = TRoute['types']['loaderDeps'],\n  TLoaderData extends any = TRoute['types']['loaderData'],\n>(id: TId) {\n  return new RouteApi<\n    TId,\n    TRoute,\n    TFullSearchSchema,\n    TAllParams,\n    TAllContext,\n    TLoaderDeps,\n    TLoaderData\n  >({ id })\n}\n\nexport class RouteApi<\n  TId extends RouteIds<RegisteredRouter['routeTree']>,\n  TRoute extends AnyRoute = RouteById<RegisteredRouter['routeTree'], TId>,\n  TFullSearchSchema extends Record<\n    string,\n    any\n  > = TRoute['types']['fullSearchSchema'],\n  TAllParams extends AnyPathParams = TRoute['types']['allParams'],\n  TAllContext extends Record<string, any> = TRoute['types']['allContext'],\n  TLoaderDeps extends Record<string, any> = TRoute['types']['loaderDeps'],\n  TLoaderData extends any = TRoute['types']['loaderData'],\n> {\n  id: TId\n\n  /**\n   * @deprecated Use the `getRouteApi` function instead.\n   */\n  constructor({ id }: { id: TId }) {\n    this.id = id as any\n  }\n\n  useMatch = <\n    TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n    TRouteMatchState = RouteMatch<TRouteTree, TId>,\n    TSelected = TRouteMatchState,\n  >(opts?: {\n    select?: (match: TRouteMatchState) => TSelected\n  }): TSelected => {\n    return useMatch({ select: opts?.select, from: this.id })\n  }\n\n  useRouteContext = <TSelected = TAllContext>(opts?: {\n    select?: (s: TAllContext) => TSelected\n  }): TSelected => {\n    return useMatch({\n      from: this.id,\n      select: (d: any) => (opts?.select ? opts.select(d.context) : d.context),\n    })\n  }\n\n  useSearch = <TSelected = TFullSearchSchema>(opts?: {\n    select?: (s: TFullSearchSchema) => TSelected\n  }): TSelected => {\n    return useSearch({ ...opts, from: this.id })\n  }\n\n  useParams = <TSelected = TAllParams>(opts?: {\n    select?: (s: TAllParams) => TSelected\n  }): TSelected => {\n    return useParams({ ...opts, from: this.id })\n  }\n\n  useLoaderDeps = <TSelected = TLoaderDeps>(opts?: {\n    select?: (s: TLoaderDeps) => TSelected\n  }): TSelected => {\n    return useLoaderDeps({ ...opts, from: this.id } as any)\n  }\n\n  useLoaderData = <TSelected = TLoaderData>(opts?: {\n    select?: (s: TLoaderData) => TSelected\n  }): TSelected => {\n    return useLoaderData({ ...opts, from: this.id } as any)\n  }\n\n  notFound = (opts?: NotFoundError) => {\n    return notFound({ routeId: this.id as string, ...opts })\n  }\n}\n\nexport class Route<\n  TParentRoute extends RouteConstraints['TParentRoute'] = AnyRoute,\n  in out TPath extends RouteConstraints['TPath'] = '/',\n  TFullPath extends RouteConstraints['TFullPath'] = ResolveFullPath<\n    TParentRoute,\n    TPath\n  >,\n  TCustomId extends RouteConstraints['TCustomId'] = string,\n  TId extends RouteConstraints['TId'] = ResolveId<\n    TParentRoute,\n    TCustomId,\n    TPath\n  >,\n  TSearchSchemaInput extends RouteConstraints['TSearchSchema'] = {},\n  TSearchSchema extends RouteConstraints['TSearchSchema'] = {},\n  TSearchSchemaUsed extends Record<\n    string,\n    any\n  > = TSearchSchemaInput extends SearchSchemaInput\n    ? Omit<TSearchSchemaInput, keyof SearchSchemaInput>\n    : TSearchSchema,\n  TFullSearchSchemaInput extends Record<\n    string,\n    any\n  > = ResolveFullSearchSchemaInput<TParentRoute, TSearchSchemaUsed>,\n  TFullSearchSchema extends\n    RouteConstraints['TFullSearchSchema'] = ResolveFullSearchSchema<\n    TParentRoute,\n    TSearchSchema\n  >,\n  TParams extends RouteConstraints['TParams'] = Expand<\n    Record<ParsePathParams<TPath>, string>\n  >,\n  TAllParams extends RouteConstraints['TAllParams'] = ResolveAllParams<\n    TParentRoute,\n    TParams\n  >,\n  TRouteContextReturn extends RouteConstraints['TRouteContext'] = RouteContext,\n  in out TRouteContext extends RouteConstraints['TRouteContext'] = [\n    TRouteContextReturn,\n  ] extends [never]\n    ? RouteContext\n    : TRouteContextReturn,\n  in out TAllContext extends Expand<\n    Assign<IsAny<TParentRoute['types']['allContext'], {}>, TRouteContext>\n  > = Expand<\n    Assign<IsAny<TParentRoute['types']['allContext'], {}>, TRouteContext>\n  >,\n  TRouterContext extends RouteConstraints['TRouterContext'] = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderDataReturn extends any = unknown,\n  TLoaderData extends any = [TLoaderDataReturn] extends [never]\n    ? undefined\n    : TLoaderDataReturn,\n  TChildren extends RouteConstraints['TChildren'] = unknown,\n  TRouteTree extends RouteConstraints['TRouteTree'] = AnyRoute,\n> {\n  isRoot: TParentRoute extends Route<any> ? true : false\n  options: RouteOptions<\n    TParentRoute,\n    TCustomId,\n    TPath,\n    TSearchSchemaInput,\n    TSearchSchema,\n    TSearchSchemaUsed,\n    TFullSearchSchemaInput,\n    TFullSearchSchema,\n    TParams,\n    TAllParams,\n    TRouteContextReturn,\n    TRouteContext,\n    TRouterContext,\n    TAllContext,\n    TLoaderDeps,\n    TLoaderDataReturn,\n    TLoaderData\n  >\n\n  // Set up in this.init()\n  parentRoute!: TParentRoute\n  id!: TId\n  // customId!: TCustomId\n  path!: TPath\n  fullPath!: TFullPath\n  to!: TrimPathRight<TFullPath>\n\n  // Optional\n  children?: TChildren\n  originalIndex?: number\n  router?: AnyRouter\n  rank!: number\n  lazyFn?: () => Promise<LazyRoute<any>>\n\n  /**\n   * @deprecated Use the `createRoute` function instead.\n   */\n  constructor(\n    options: RouteOptions<\n      TParentRoute,\n      TCustomId,\n      TPath,\n      TSearchSchemaInput,\n      TSearchSchema,\n      TSearchSchemaUsed,\n      TFullSearchSchemaInput,\n      TFullSearchSchema,\n      TParams,\n      TAllParams,\n      TRouteContextReturn,\n      TRouteContext,\n      TRouterContext,\n      TAllContext,\n      TLoaderDeps,\n      TLoaderDataReturn,\n      TLoaderData\n    >,\n  ) {\n    this.options = (options as any) || {}\n    this.isRoot = !options?.getParentRoute as any\n    invariant(\n      !((options as any)?.id && (options as any)?.path),\n      `Route cannot have both an 'id' and a 'path' option.`,\n    )\n    ;(this as any).$$typeof = Symbol.for('react.memo')\n  }\n\n  types!: {\n    parentRoute: TParentRoute\n    path: TPath\n    to: TrimPathRight<TFullPath>\n    fullPath: TFullPath\n    customId: TCustomId\n    id: TId\n    searchSchema: TSearchSchema\n    searchSchemaInput: TSearchSchemaInput\n    searchSchemaUsed: TSearchSchemaUsed\n    fullSearchSchema: TFullSearchSchema\n    fullSearchSchemaInput: TFullSearchSchemaInput\n    params: TParams\n    allParams: TAllParams\n    routeContext: TRouteContext\n    allContext: TAllContext\n    children: TChildren\n    routeTree: TRouteTree\n    routerContext: TRouterContext\n    loaderData: TLoaderData\n    loaderDeps: TLoaderDeps\n  }\n\n  init = (opts: { originalIndex: number }) => {\n    this.originalIndex = opts.originalIndex\n\n    const options = this.options as RouteOptions<\n      TParentRoute,\n      TCustomId,\n      TPath,\n      TSearchSchemaInput,\n      TSearchSchema,\n      TSearchSchemaUsed,\n      TFullSearchSchemaInput,\n      TFullSearchSchema,\n      TParams,\n      TAllParams,\n      TRouteContextReturn,\n      TRouteContext,\n      TRouterContext,\n      TAllContext,\n      TLoaderDeps,\n      TLoaderDataReturn,\n      TLoaderData\n    > &\n      RoutePathOptionsIntersection<TCustomId, TPath>\n\n    const isRoot = !options?.path && !options?.id\n\n    this.parentRoute = this.options?.getParentRoute?.()\n\n    if (isRoot) {\n      this.path = rootRouteId as TPath\n    } else {\n      invariant(\n        this.parentRoute,\n        `Child Route instances must pass a 'getParentRoute: () => ParentRoute' option that returns a Route instance.`,\n      )\n    }\n\n    let path: undefined | string = isRoot ? rootRouteId : options.path\n\n    // If the path is anything other than an index path, trim it up\n    if (path && path !== '/') {\n      path = trimPathLeft(path)\n    }\n\n    const customId = options?.id || path\n\n    // Strip the parentId prefix from the first level of children\n    let id = isRoot\n      ? rootRouteId\n      : joinPaths([\n          (this.parentRoute.id as any) === rootRouteId\n            ? ''\n            : this.parentRoute.id,\n          customId,\n        ])\n\n    if (path === rootRouteId) {\n      path = '/'\n    }\n\n    if (id !== rootRouteId) {\n      id = joinPaths(['/', id])\n    }\n\n    const fullPath =\n      id === rootRouteId ? '/' : joinPaths([this.parentRoute.fullPath, path])\n\n    this.path = path as TPath\n    this.id = id as TId\n    // this.customId = customId as TCustomId\n    this.fullPath = fullPath as TFullPath\n    this.to = fullPath as TrimPathRight<TFullPath>\n  }\n\n  addChildren = <TNewChildren extends AnyRoute[]>(\n    children: TNewChildren,\n  ): Route<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchSchemaInput,\n    TSearchSchema,\n    TSearchSchemaUsed,\n    TFullSearchSchemaInput,\n    TFullSearchSchema,\n    TParams,\n    TAllParams,\n    TRouteContextReturn,\n    TRouteContext,\n    TAllContext,\n    TRouterContext,\n    TLoaderDeps,\n    TLoaderDataReturn,\n    TLoaderData,\n    TNewChildren,\n    TRouteTree\n  > => {\n    this.children = children as any\n    return this as any\n  }\n\n  updateLoader = <TNewLoaderData extends any = unknown>(options: {\n    loader: RouteLoaderFn<\n      TAllParams,\n      TLoaderDeps,\n      TAllContext,\n      TRouteContext,\n      TNewLoaderData\n    >\n  }) => {\n    Object.assign(this.options, options)\n    return this as unknown as Route<\n      TParentRoute,\n      TPath,\n      TFullPath,\n      TCustomId,\n      TId,\n      TSearchSchemaInput,\n      TSearchSchema,\n      TSearchSchemaUsed,\n      TFullSearchSchemaInput,\n      TFullSearchSchema,\n      TParams,\n      TAllParams,\n      TRouteContextReturn,\n      TRouteContext,\n      TAllContext,\n      TRouterContext,\n      TLoaderDeps,\n      TNewLoaderData,\n      TChildren,\n      TRouteTree\n    >\n  }\n\n  update = (\n    options: UpdatableRouteOptions<TAllParams, TFullSearchSchema, TLoaderData>,\n  ) => {\n    Object.assign(this.options, options)\n    return this\n  }\n\n  lazy = (lazyFn: () => Promise<LazyRoute<any>>) => {\n    this.lazyFn = lazyFn\n    return this\n  }\n\n  useMatch = <\n    TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n    TRouteMatchState = RouteMatch<TRouteTree, TId>,\n    TSelected = TRouteMatchState,\n  >(opts?: {\n    select?: (match: TRouteMatchState) => TSelected\n  }): TSelected => {\n    return useMatch({ ...opts, from: this.id })\n  }\n\n  useRouteContext = <TSelected = TAllContext>(opts?: {\n    select?: (search: TAllContext) => TSelected\n  }): TSelected => {\n    return useMatch({\n      ...opts,\n      from: this.id,\n      select: (d: any) => (opts?.select ? opts.select(d.context) : d.context),\n    })\n  }\n\n  useSearch = <TSelected = TFullSearchSchema>(opts?: {\n    select?: (search: TFullSearchSchema) => TSelected\n  }): TSelected => {\n    return useSearch({ ...opts, from: this.id })\n  }\n\n  useParams = <TSelected = TAllParams>(opts?: {\n    select?: (search: TAllParams) => TSelected\n  }): TSelected => {\n    return useParams({ ...opts, from: this.id })\n  }\n\n  useLoaderDeps = <TSelected = TLoaderDeps>(opts?: {\n    select?: (s: TLoaderDeps) => TSelected\n  }): TSelected => {\n    return useLoaderDeps({ ...opts, from: this.id } as any)\n  }\n\n  useLoaderData = <TSelected = TLoaderData>(opts?: {\n    select?: (search: TLoaderData) => TSelected\n  }): TSelected => {\n    return useLoaderData({ ...opts, from: this.id } as any)\n  }\n}\n\nexport function createRoute<\n  TParentRoute extends RouteConstraints['TParentRoute'] = AnyRoute,\n  TPath extends RouteConstraints['TPath'] = '/',\n  TFullPath extends RouteConstraints['TFullPath'] = ResolveFullPath<\n    TParentRoute,\n    TPath\n  >,\n  TCustomId extends RouteConstraints['TCustomId'] = string,\n  TId extends RouteConstraints['TId'] = ResolveId<\n    TParentRoute,\n    TCustomId,\n    TPath\n  >,\n  TSearchSchemaInput extends RouteConstraints['TSearchSchema'] = {},\n  TSearchSchema extends RouteConstraints['TSearchSchema'] = {},\n  TSearchSchemaUsed extends Record<\n    string,\n    any\n  > = TSearchSchemaInput extends SearchSchemaInput\n    ? Omit<TSearchSchemaInput, keyof SearchSchemaInput>\n    : TSearchSchema,\n  TFullSearchSchemaInput extends Record<\n    string,\n    any\n  > = ResolveFullSearchSchemaInput<TParentRoute, TSearchSchemaUsed>,\n  TFullSearchSchema extends\n    RouteConstraints['TFullSearchSchema'] = ResolveFullSearchSchema<\n    TParentRoute,\n    TSearchSchema\n  >,\n  TParams extends RouteConstraints['TParams'] = Expand<\n    Record<ParsePathParams<TPath>, string>\n  >,\n  TAllParams extends RouteConstraints['TAllParams'] = ResolveAllParams<\n    TParentRoute,\n    TParams\n  >,\n  TRouteContextReturn extends RouteConstraints['TRouteContext'] = RouteContext,\n  TRouteContext extends RouteConstraints['TRouteContext'] = [\n    TRouteContextReturn,\n  ] extends [never]\n    ? RouteContext\n    : TRouteContextReturn,\n  TAllContext extends Expand<\n    Assign<IsAny<TParentRoute['types']['allContext'], {}>, TRouteContext>\n  > = Expand<\n    Assign<IsAny<TParentRoute['types']['allContext'], {}>, TRouteContext>\n  >,\n  TRouterContext extends RouteConstraints['TRouterContext'] = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderDataReturn extends any = unknown,\n  TLoaderData extends any = [TLoaderDataReturn] extends [never]\n    ? undefined\n    : TLoaderDataReturn,\n  TChildren extends RouteConstraints['TChildren'] = unknown,\n  TRouteTree extends RouteConstraints['TRouteTree'] = AnyRoute,\n>(\n  options: RouteOptions<\n    TParentRoute,\n    TCustomId,\n    TPath,\n    TSearchSchemaInput,\n    TSearchSchema,\n    TSearchSchemaUsed,\n    TFullSearchSchemaInput,\n    TFullSearchSchema,\n    TParams,\n    TAllParams,\n    TRouteContextReturn,\n    TRouteContext,\n    TRouterContext,\n    TAllContext,\n    TLoaderDeps,\n    TLoaderDataReturn,\n    TLoaderData\n  >,\n) {\n  return new Route<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchSchemaInput,\n    TSearchSchema,\n    TSearchSchemaUsed,\n    TFullSearchSchemaInput,\n    TFullSearchSchema,\n    TParams,\n    TAllParams,\n    TRouteContextReturn,\n    TRouteContext,\n    TAllContext,\n    TRouterContext,\n    TLoaderDeps,\n    TLoaderDataReturn,\n    TLoaderData,\n    TChildren,\n    TRouteTree\n  >(options)\n}\n\nexport type AnyRootRoute = RootRoute<any, any, any, any, any, any, any, any>\n\nexport function createRootRouteWithContext<TRouterContext extends {}>() {\n  return <\n    TSearchSchemaInput extends Record<string, any> = RootSearchSchema,\n    TSearchSchema extends Record<string, any> = RootSearchSchema,\n    TSearchSchemaUsed extends Record<string, any> = RootSearchSchema,\n    TRouteContextReturn extends RouteContext = RouteContext,\n    TRouteContext extends RouteContext = [TRouteContextReturn] extends [never]\n      ? RouteContext\n      : TRouteContextReturn,\n    TLoaderDeps extends Record<string, any> = {},\n    TLoaderDataReturn extends any = unknown,\n    TLoaderData extends any = [TLoaderDataReturn] extends [never]\n      ? undefined\n      : TLoaderDataReturn,\n  >(\n    options?: Omit<\n      RouteOptions<\n        AnyRoute, // TParentRoute\n        RootRouteId, // TCustomId\n        '', // TPath\n        TSearchSchemaInput, // TSearchSchemaInput\n        TSearchSchema, // TSearchSchema\n        TSearchSchemaUsed,\n        TSearchSchemaUsed, //TFullSearchSchemaInput\n        TSearchSchema, // TFullSearchSchema\n        {}, // TParams\n        {}, // TAllParams\n        TRouteContextReturn, // TRouteContextReturn\n        TRouteContext, // TRouteContext\n        TRouterContext,\n        Assign<TRouterContext, TRouteContext>, // TAllContext\n        TLoaderDeps,\n        TLoaderDataReturn, // TLoaderDataReturn,\n        TLoaderData // TLoaderData,\n      >,\n      | 'path'\n      | 'id'\n      | 'getParentRoute'\n      | 'caseSensitive'\n      | 'parseParams'\n      | 'stringifyParams'\n    >,\n  ) => {\n    return createRootRoute<\n      TSearchSchemaInput,\n      TSearchSchema,\n      TSearchSchemaUsed,\n      TRouteContextReturn,\n      TRouteContext,\n      TRouterContext,\n      TLoaderDeps,\n      TLoaderData\n    >(options as any)\n  }\n}\n\n/**\n * @deprecated Use the `createRootRouteWithContext` function instead.\n */\nexport const rootRouteWithContext = createRootRouteWithContext\n\nexport type RootSearchSchema = {\n  __TRootSearchSchema__: '__TRootSearchSchema__'\n}\n\nexport class RootRoute<\n  TSearchSchemaInput extends Record<string, any> = RootSearchSchema,\n  TSearchSchema extends Record<string, any> = RootSearchSchema,\n  TSearchSchemaUsed extends Record<string, any> = RootSearchSchema,\n  TRouteContextReturn extends RouteContext = RouteContext,\n  TRouteContext extends RouteContext = [TRouteContextReturn] extends [never]\n    ? RouteContext\n    : TRouteContextReturn,\n  TRouterContext extends {} = {},\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderDataReturn extends any = unknown,\n  TLoaderData extends any = [TLoaderDataReturn] extends [never]\n    ? undefined\n    : TLoaderDataReturn,\n> extends Route<\n  any, // TParentRoute\n  '/', // TPath\n  '/', // TFullPath\n  string, // TCustomId\n  RootRouteId, // TId\n  TSearchSchemaInput, // TSearchSchemaInput\n  TSearchSchema, // TSearchSchema\n  TSearchSchemaUsed,\n  TSearchSchemaUsed, // TFullSearchSchemaInput\n  TSearchSchema, // TFullSearchSchema\n  {}, // TParams\n  {}, // TAllParams\n  TRouteContextReturn, // TRouteContextReturn\n  TRouteContext, // TRouteContext\n  Expand<Assign<TRouterContext, TRouteContext>>, // TAllContext\n  TRouterContext, // TRouterContext\n  TLoaderDeps,\n  TLoaderDataReturn,\n  TLoaderData,\n  any, // TChildren\n  any // TRouteTree\n> {\n  /**\n   * @deprecated `RootRoute` is now an internal implementation detail. Use `createRootRoute()` instead.\n   */\n  constructor(\n    options?: Omit<\n      RouteOptions<\n        AnyRoute, // TParentRoute\n        RootRouteId, // TCustomId\n        '', // TPath\n        TSearchSchemaInput, // TSearchSchemaInput\n        TSearchSchema, // TSearchSchema\n        TSearchSchemaUsed,\n        TSearchSchemaUsed, // TFullSearchSchemaInput\n        TSearchSchema, // TFullSearchSchema\n        {}, // TParams\n        {}, // TAllParams\n        TRouteContextReturn, // TRouteContextReturn\n        TRouteContext, // TRouteContext\n        TRouterContext,\n        Assign<TRouterContext, TRouteContext>, // TAllContext\n        TLoaderDeps,\n        TLoaderDataReturn,\n        TLoaderData\n      >,\n      | 'path'\n      | 'id'\n      | 'getParentRoute'\n      | 'caseSensitive'\n      | 'parseParams'\n      | 'stringifyParams'\n    >,\n  ) {\n    super(options as any)\n  }\n}\n\nexport function createRootRoute<\n  TSearchSchemaInput extends Record<string, any> = RootSearchSchema,\n  TSearchSchema extends Record<string, any> = RootSearchSchema,\n  TSearchSchemaUsed extends Record<string, any> = RootSearchSchema,\n  TRouteContextReturn extends RouteContext = RouteContext,\n  TRouteContext extends RouteContext = [TRouteContextReturn] extends [never]\n    ? RouteContext\n    : TRouteContextReturn,\n  TRouterContext extends {} = {},\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderDataReturn extends any = unknown,\n  TLoaderData extends any = [TLoaderDataReturn] extends [never]\n    ? undefined\n    : TLoaderDataReturn,\n>(\n  options?: Omit<\n    RouteOptions<\n      AnyRoute, // TParentRoute\n      RootRouteId, // TCustomId\n      '', // TPath\n      TSearchSchemaInput, // TSearchSchemaInput\n      TSearchSchema, // TSearchSchema\n      TSearchSchemaUsed,\n      TSearchSchemaUsed, // TFullSearchSchemaInput\n      TSearchSchema, // TFullSearchSchema\n      {}, // TParams\n      {}, // TAllParams\n      TRouteContextReturn, // TRouteContextReturn\n      TRouteContext, // TRouteContext\n      TRouterContext,\n      Assign<TRouterContext, TRouteContext>, // TAllContext\n      TLoaderDeps,\n      TLoaderDataReturn,\n      TLoaderData\n    >,\n    | 'path'\n    | 'id'\n    | 'getParentRoute'\n    | 'caseSensitive'\n    | 'parseParams'\n    | 'stringifyParams'\n  >,\n) {\n  return new RootRoute<\n    TSearchSchemaInput,\n    TSearchSchema,\n    TSearchSchemaUsed,\n    TRouteContextReturn,\n    TRouteContext,\n    TRouterContext,\n    TLoaderDeps,\n    TLoaderDataReturn,\n    TLoaderData\n  >(options)\n}\n\nexport type ResolveFullPath<\n  TParentRoute extends AnyRoute,\n  TPath extends string,\n  TPrefixed = RoutePrefix<TParentRoute['fullPath'], TPath>,\n> = TPrefixed extends RootRouteId ? '/' : TPrefixed\n\ntype RoutePrefix<\n  TPrefix extends string,\n  TPath extends string,\n> = string extends TPath\n  ? RootRouteId\n  : TPath extends string\n    ? TPrefix extends RootRouteId\n      ? TPath extends '/'\n        ? '/'\n        : `/${TrimPath<TPath>}`\n      : `${TPrefix}/${TPath}` extends '/'\n        ? '/'\n        : `/${TrimPathLeft<`${TrimPathRight<TPrefix>}/${TrimPath<TPath>}`>}`\n    : never\n\nexport type TrimPath<T extends string> = '' extends T\n  ? ''\n  : TrimPathRight<TrimPathLeft<T>>\n\nexport type TrimPathLeft<T extends string> =\n  T extends `${RootRouteId}/${infer U}`\n    ? TrimPathLeft<U>\n    : T extends `/${infer U}`\n      ? TrimPathLeft<U>\n      : T\nexport type TrimPathRight<T extends string> = T extends '/'\n  ? '/'\n  : T extends `${infer U}/`\n    ? TrimPathRight<U>\n    : T\n\nexport type RouteMask<TRouteTree extends AnyRoute> = {\n  routeTree: TRouteTree\n  from: RoutePaths<TRouteTree>\n  to?: any\n  params?: any\n  search?: any\n  hash?: any\n  state?: any\n  unmaskOnReload?: boolean\n}\n\nexport function createRouteMask<\n  TRouteTree extends AnyRoute,\n  TFrom extends RoutePaths<TRouteTree>,\n  TTo extends string,\n>(\n  opts: {\n    routeTree: TRouteTree\n  } & ToSubOptions<TRouteTree, TFrom, TTo>,\n): RouteMask<TRouteTree> {\n  return opts as any\n}\n\n/**\n * @deprecated Use `ErrorComponentProps` instead.\n */\nexport type ErrorRouteProps = {\n  error: unknown\n  info: { componentStack: string }\n}\n\nexport type ErrorComponentProps = {\n  error: unknown\n  info: { componentStack: string }\n}\nexport type NotFoundRouteProps = {\n  // TODO: Make sure this is `| null | undefined` (this is for global not-founds)\n  data: unknown\n}\n//\n\nexport type ReactNode = any\n\nexport type SyncRouteComponent<TProps> =\n  | ((props: TProps) => ReactNode)\n  | React.LazyExoticComponent<(props: TProps) => ReactNode>\n\nexport type AsyncRouteComponent<TProps> = SyncRouteComponent<TProps> & {\n  preload?: () => Promise<void>\n}\n\nexport type RouteComponent<TProps = any> = SyncRouteComponent<TProps> &\n  AsyncRouteComponent<TProps>\n\nexport type ErrorRouteComponent = RouteComponent<ErrorComponentProps>\n\nexport type NotFoundRouteComponent = SyncRouteComponent<NotFoundRouteProps>\n\nexport class NotFoundRoute<\n  TParentRoute extends AnyRootRoute,\n  TSearchSchemaInput extends Record<string, any> = {},\n  TSearchSchema extends RouteConstraints['TSearchSchema'] = {},\n  TSearchSchemaUsed extends RouteConstraints['TSearchSchema'] = {},\n  TFullSearchSchemaInput extends\n    RouteConstraints['TFullSearchSchema'] = ResolveFullSearchSchemaInput<\n    TParentRoute,\n    TSearchSchemaUsed\n  >,\n  TFullSearchSchema extends\n    RouteConstraints['TFullSearchSchema'] = ResolveFullSearchSchema<\n    TParentRoute,\n    TSearchSchema\n  >,\n  TRouteContextReturn extends RouteConstraints['TRouteContext'] = AnyContext,\n  TRouteContext extends RouteConstraints['TRouteContext'] = RouteContext,\n  TAllContext extends Expand<\n    Assign<IsAny<TParentRoute['types']['allContext'], {}>, TRouteContext>\n  > = Expand<\n    Assign<IsAny<TParentRoute['types']['allContext'], {}>, TRouteContext>\n  >,\n  TRouterContext extends RouteConstraints['TRouterContext'] = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderDataReturn extends any = unknown,\n  TLoaderData extends any = [TLoaderDataReturn] extends [never]\n    ? undefined\n    : TLoaderDataReturn,\n  TChildren extends RouteConstraints['TChildren'] = unknown,\n  TRouteTree extends RouteConstraints['TRouteTree'] = AnyRoute,\n> extends Route<\n  TParentRoute,\n  '/404',\n  '/404',\n  '404',\n  '404',\n  TSearchSchemaInput,\n  TSearchSchema,\n  TSearchSchemaUsed,\n  TFullSearchSchemaInput,\n  TFullSearchSchema,\n  {},\n  {},\n  TRouteContextReturn,\n  TRouteContext,\n  TAllContext,\n  TRouterContext,\n  TLoaderDeps,\n  TLoaderDataReturn,\n  TLoaderData,\n  TChildren,\n  TRouteTree\n> {\n  constructor(\n    options: Omit<\n      RouteOptions<\n        TParentRoute,\n        string,\n        string,\n        TSearchSchemaInput,\n        TSearchSchema,\n        TSearchSchemaUsed,\n        TFullSearchSchemaInput,\n        TFullSearchSchema,\n        {},\n        {},\n        TRouteContextReturn,\n        TRouteContext,\n        TRouterContext,\n        TAllContext,\n        TLoaderDeps,\n        TLoaderDataReturn,\n        TLoaderData\n      >,\n      'caseSensitive' | 'parseParams' | 'stringifyParams' | 'path' | 'id'\n    >,\n  ) {\n    super({\n      ...(options as any),\n      id: '404',\n    })\n  }\n}\n", "// @ts-nocheck\n\n// qss has been slightly modified and inlined here for our use cases (and compression's sake). We've included it as a hard dependency for MIT license attribution.\n\nexport function encode(obj, pfx?: string) {\n  var k,\n    i,\n    tmp,\n    str = ''\n\n  for (k in obj) {\n    if ((tmp = obj[k]) !== void 0) {\n      if (Array.isArray(tmp)) {\n        for (i = 0; i < tmp.length; i++) {\n          str && (str += '&')\n          str += encodeURIComponent(k) + '=' + encodeURIComponent(tmp[i])\n        }\n      } else {\n        str && (str += '&')\n        str += encodeURIComponent(k) + '=' + encodeURIComponent(tmp)\n      }\n    }\n  }\n\n  return (pfx || '') + str\n}\n\nfunction toValue(mix) {\n  if (!mix) return ''\n  var str = decodeURIComponent(mix)\n  if (str === 'false') return false\n  if (str === 'true') return true\n  return +str * 0 === 0 && +str + '' === str ? +str : str\n}\n\nexport function decode(str, pfx?: string) {\n  var tmp,\n    k,\n    out = {},\n    arr = (pfx ? str.substr(pfx.length) : str).split('&')\n\n  while ((tmp = arr.shift())) {\n    tmp = tmp.split('=')\n    k = tmp.shift()\n    if (out[k] !== void 0) {\n      out[k] = [].concat(out[k], toValue(tmp.shift()))\n    } else {\n      out[k] = toValue(tmp.shift())\n    }\n  }\n\n  return out\n}\n", "import { decode, encode } from './qss'\nimport { AnySearchSchema } from './route'\n\nexport const defaultParseSearch = parseSearchWith(JSON.parse)\nexport const defaultStringifySearch = stringifySearchWith(\n  JSON.stringify,\n  JSON.parse,\n)\n\nexport function parseSearchWith(parser: (str: string) => any) {\n  return (searchStr: string): AnySearchSchema => {\n    if (searchStr.substring(0, 1) === '?') {\n      searchStr = searchStr.substring(1)\n    }\n\n    let query: Record<string, unknown> = decode(searchStr)\n\n    // Try to parse any query params that might be json\n    for (let key in query) {\n      const value = query[key]\n      if (typeof value === 'string') {\n        try {\n          query[key] = parser(value)\n        } catch (err) {\n          //\n        }\n      }\n    }\n\n    return query\n  }\n}\n\nexport function stringifySearchWith(\n  stringify: (search: any) => string,\n  parser?: (str: string) => any,\n) {\n  function stringifyValue(val: any) {\n    if (typeof val === 'object' && val !== null) {\n      try {\n        return stringify(val)\n      } catch (err) {\n        // silent\n      }\n    } else if (typeof val === 'string' && typeof parser === 'function') {\n      try {\n        // Check if it's a valid parseable string.\n        // If it is, then stringify it again.\n        parser(val)\n        return stringify(val)\n      } catch (err) {\n        // silent\n      }\n    }\n    return val\n  }\n\n  return (search: Record<string, any>) => {\n    search = { ...search }\n\n    if (search) {\n      Object.keys(search).forEach((key) => {\n        const val = search[key]\n        if (typeof val === 'undefined' || val === undefined) {\n          delete search[key]\n        } else {\n          search[key] = stringifyValue(val)\n        }\n      })\n    }\n\n    const searchStr = encode(search as Record<string, string>).toString()\n\n    return searchStr ? `?${searchStr}` : ''\n  }\n}\n\nexport type SearchSerializer = (searchObj: Record<string, any>) => string\nexport type SearchParser = (searchStr: string) => Record<string, any>\n", "import * as React from 'react'\nimport { Matches } from './Matches'\nimport { NavigateOptions, ToOptions } from './link'\nimport { ParsedLocation } from './location'\nimport { AnyRoute } from './route'\nimport { RoutePaths } from './routeInfo'\nimport { RegisteredRouter, Router, RouterOptions, RouterState } from './router'\nimport { pick, useLayoutEffect } from './utils'\n\nimport { RouteMatch } from './Matches'\nimport { useRouter } from './useRouter'\nimport { useRouterState } from './useRouterState'\nimport { getRouterContext } from './routerContext'\n\nconst useTransition =\n  React.useTransition ||\n  (() => [\n    false,\n    (cb) => {\n      cb()\n    },\n  ])\n\nexport interface CommitLocationOptions {\n  replace?: boolean\n  resetScroll?: boolean\n  startTransition?: boolean\n}\n\nexport interface MatchLocation {\n  to?: string | number | null\n  fuzzy?: boolean\n  caseSensitive?: boolean\n  from?: string\n}\n\nexport type NavigateFn = <\n  TTo extends string,\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  <PERSON><PERSON><PERSON> extends RoutePaths<TRouteTree> | string = string,\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n>(\n  opts: NavigateOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo>,\n) => Promise<void>\n\nexport type BuildLocationFn<TRouteTree extends AnyRoute> = <\n  TTo extends string,\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n>(\n  opts: ToOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo> & {\n    leaveParams?: boolean\n  },\n) => ParsedLocation\n\nexport type InjectedHtmlEntry = string | (() => Promise<string> | string)\n\nexport function RouterProvider<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TDehydrated extends Record<string, any> = Record<string, any>,\n>({ router, ...rest }: RouterProps<TRouteTree, TDehydrated>) {\n  // Allow the router to update options on the router instance\n  router.update({\n    ...router.options,\n    ...rest,\n    context: {\n      ...router.options.context,\n      ...rest?.context,\n    },\n  } as any)\n\n  const matches = router.options.InnerWrap ? (\n    <router.options.InnerWrap>\n      <Matches />\n    </router.options.InnerWrap>\n  ) : (\n    <Matches />\n  )\n\n  const routerContext = getRouterContext()\n\n  const provider = (\n    <routerContext.Provider value={router}>\n      {matches}\n      <Transitioner />\n    </routerContext.Provider>\n  )\n\n  if (router.options.Wrap) {\n    return <router.options.Wrap>{provider}</router.options.Wrap>\n  }\n\n  return provider\n}\n\nfunction Transitioner() {\n  const router = useRouter()\n  const mountLoadForRouter = React.useRef({ router, mounted: false })\n  const routerState = useRouterState({\n    select: (s) =>\n      pick(s, ['isLoading', 'location', 'resolvedLocation', 'isTransitioning']),\n  })\n\n  const [isTransitioning, startReactTransition] = useTransition()\n\n  router.startReactTransition = startReactTransition\n\n  React.useEffect(() => {\n    if (isTransitioning) {\n      router.__store.setState((s) => ({\n        ...s,\n        isTransitioning,\n      }))\n    }\n  }, [isTransitioning])\n\n  const tryLoad = () => {\n    const apply = (cb: () => void) => {\n      if (!routerState.isTransitioning) {\n        startReactTransition(() => cb())\n      } else {\n        cb()\n      }\n    }\n\n    apply(() => {\n      try {\n        router.load()\n      } catch (err) {\n        console.error(err)\n      }\n    })\n  }\n\n  useLayoutEffect(() => {\n    const unsub = router.history.subscribe(() => {\n      router.latestLocation = router.parseLocation(router.latestLocation)\n      if (router.state.location !== router.latestLocation) {\n        tryLoad()\n      }\n    })\n\n    return () => {\n      unsub()\n    }\n  }, [router.history])\n\n  useLayoutEffect(() => {\n    if (\n      (React.useTransition as any)\n        ? routerState.isTransitioning && !isTransitioning\n        : true &&\n          !routerState.isLoading &&\n          routerState.resolvedLocation !== routerState.location\n    ) {\n      router.emit({\n        type: 'onResolved',\n        fromLocation: routerState.resolvedLocation,\n        toLocation: routerState.location,\n        pathChanged:\n          routerState.location!.href !== routerState.resolvedLocation?.href,\n      })\n\n      if ((document as any).querySelector) {\n        if (routerState.location.hash !== '') {\n          const el = document.getElementById(\n            routerState.location.hash,\n          ) as HTMLElement | null\n          if (el) {\n            el.scrollIntoView()\n          }\n        }\n      }\n\n      router.__store.setState((s) => ({\n        ...s,\n        isTransitioning: false,\n        resolvedLocation: s.location,\n      }))\n    }\n  }, [\n    routerState.isTransitioning,\n    isTransitioning,\n    routerState.isLoading,\n    routerState.resolvedLocation,\n    routerState.location,\n  ])\n\n  useLayoutEffect(() => {\n    if (\n      window.__TSR_DEHYDRATED__ ||\n      (mountLoadForRouter.current.router === router &&\n        mountLoadForRouter.current.mounted)\n    ) {\n      return\n    }\n    mountLoadForRouter.current = { router, mounted: true }\n    tryLoad()\n  }, [router])\n\n  return null\n}\n\nexport function getRouteMatch<TRouteTree extends AnyRoute>(\n  state: RouterState<TRouteTree>,\n  id: string,\n): undefined | RouteMatch<TRouteTree> {\n  return [\n    ...state.cachedMatches,\n    ...(state.pendingMatches ?? []),\n    ...state.matches,\n  ].find((d) => d.id === id)\n}\n\nexport type RouterProps<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TDehydrated extends Record<string, any> = Record<string, any>,\n> = Omit<RouterOptions<TRouteTree, TDehydrated>, 'context'> & {\n  router: Router<TRouteTree>\n  context?: Partial<RouterOptions<TRouteTree, TDehydrated>['context']>\n}\n", "import * as React from 'react'\nimport {\n  HistoryLocation,\n  HistoryState,\n  RouterHistory,\n  create<PERSON>rowserHistory,\n  createMemoryHistory,\n} from '@tanstack/history'\nimport { Store } from '@tanstack/react-store'\n\n//\n\nimport {\n  AnySearchSchema,\n  AnyRoute,\n  AnyContext,\n  RouteMask,\n  Route,\n  LoaderFnContext,\n  rootRouteId,\n  NotFoundRouteComponent,\n} from './route'\nimport {\n  FullSearchSchema,\n  RouteById,\n  RoutePaths,\n  RoutesById,\n  RoutesByPath,\n} from './routeInfo'\nimport { defaultParseSearch, defaultStringifySearch } from './searchParams'\nimport {\n  PickAsRequired,\n  Updater,\n  NonNullableUpdater,\n  replaceEqualDeep,\n  deepEqual,\n  escapeJSON,\n  functionalUpdate,\n  last,\n  pick,\n  Timeout,\n  isServer,\n} from './utils'\nimport { RouteComponent } from './route'\nimport { AnyRouteMatch, MatchRouteOptions, RouteMatch } from './Matches'\nimport { ParsedLocation } from './location'\nimport { SearchSerializer, SearchParser } from './searchParams'\nimport {\n  BuildLocationFn,\n  CommitLocationOptions,\n  InjectedHtmlEntry,\n  NavigateFn,\n  getRouteMatch,\n} from './RouterProvider'\n\nimport {\n  cleanPath,\n  interpolatePath,\n  joinPaths,\n  matchPathname,\n  parsePathname,\n  resolvePath,\n  trimPath,\n  trimPathLeft,\n  trimPathRight,\n} from './path'\nimport invariant from 'tiny-invariant'\nimport { AnyRedirect, isRedirect } from './redirects'\nimport { NotFoundError, isNotFound } from './not-found'\nimport { NavigateOptions, ResolveRelativePath, ToOptions } from './link'\nimport { NoInfer } from '@tanstack/react-store'\nimport warning from 'tiny-warning'\nimport { DeferredPromiseState } from './defer'\n\n//\n\ndeclare global {\n  interface Window {\n    __TSR_DEHYDRATED__?: { data: string }\n    __TSR_ROUTER_CONTEXT__?: React.Context<Router<any>>\n  }\n}\n\nexport interface Register {\n  // router: Router\n}\n\nexport type AnyRouter = Router<AnyRoute, any, any>\n\nexport type RegisteredRouter = Register extends {\n  router: infer TRouter extends AnyRouter\n}\n  ? TRouter\n  : AnyRouter\n\nexport type HydrationCtx = {\n  router: DehydratedRouter\n  payload: Record<string, any>\n}\n\nexport type RouterContextOptions<TRouteTree extends AnyRoute> =\n  AnyContext extends TRouteTree['types']['routerContext']\n    ? {\n        context?: TRouteTree['types']['routerContext']\n      }\n    : {\n        context: TRouteTree['types']['routerContext']\n      }\n\nexport interface RouterOptions<\n  TRouteTree extends AnyRoute,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n  TSerializedError extends Record<string, any> = Record<string, any>,\n> {\n  history?: RouterHistory\n  stringifySearch?: SearchSerializer\n  parseSearch?: SearchParser\n  defaultPreload?: false | 'intent'\n  defaultPreloadDelay?: number\n  defaultComponent?: RouteComponent\n  defaultErrorComponent?: RouteComponent\n  defaultPendingComponent?: RouteComponent\n  defaultPendingMs?: number\n  defaultPendingMinMs?: number\n  defaultStaleTime?: number\n  defaultPreloadStaleTime?: number\n  defaultPreloadGcTime?: number\n  notFoundMode?: 'root' | 'fuzzy'\n  defaultGcTime?: number\n  caseSensitive?: boolean\n  routeTree?: TRouteTree\n  basepath?: string\n  context?: TRouteTree['types']['routerContext']\n  dehydrate?: () => TDehydrated\n  hydrate?: (dehydrated: TDehydrated) => void\n  routeMasks?: RouteMask<TRouteTree>[]\n  unmaskOnReload?: boolean\n  Wrap?: (props: { children: any }) => React.ReactNode\n  InnerWrap?: (props: { children: any }) => React.ReactNode\n  /**\n   * @deprecated\n   * Use `notFoundComponent` instead.\n   * See https://tanstack.com/router/v1/docs/guide/not-found-errors#migrating-from-notfoundroute for more info.\n   */\n  notFoundRoute?: AnyRoute\n  defaultNotFoundComponent?: NotFoundRouteComponent\n  transformer?: RouterTransformer\n  errorSerializer?: RouterErrorSerializer<TSerializedError>\n}\n\nexport interface RouterTransformer {\n  stringify: (obj: unknown) => string\n  parse: (str: string) => unknown\n}\nexport interface RouterErrorSerializer<TSerializedError> {\n  serialize: (err: unknown) => TSerializedError\n  deserialize: (err: TSerializedError) => unknown\n}\n\nexport interface RouterState<TRouteTree extends AnyRoute = AnyRoute> {\n  status: 'pending' | 'idle'\n  isLoading: boolean\n  isTransitioning: boolean\n  matches: RouteMatch<TRouteTree>[]\n  pendingMatches?: RouteMatch<TRouteTree>[]\n  cachedMatches: RouteMatch<TRouteTree>[]\n  location: ParsedLocation<FullSearchSchema<TRouteTree>>\n  resolvedLocation: ParsedLocation<FullSearchSchema<TRouteTree>>\n  lastUpdated: number\n  statusCode: number\n}\n\nexport type ListenerFn<TEvent extends RouterEvent> = (event: TEvent) => void\n\nexport interface BuildNextOptions {\n  to?: string | number | null\n  params?: true | Updater<unknown>\n  search?: true | Updater<unknown>\n  hash?: true | Updater<string>\n  state?: true | NonNullableUpdater<HistoryState>\n  mask?: {\n    to?: string | number | null\n    params?: true | Updater<unknown>\n    search?: true | Updater<unknown>\n    hash?: true | Updater<string>\n    state?: true | NonNullableUpdater<HistoryState>\n    unmaskOnReload?: boolean\n  }\n  from?: string\n}\n\nexport interface DehydratedRouterState {\n  dehydratedMatches: DehydratedRouteMatch[]\n}\n\nexport type DehydratedRouteMatch = Pick<\n  RouteMatch,\n  'id' | 'status' | 'updatedAt' | 'loaderData'\n>\n\nexport interface DehydratedRouter {\n  state: DehydratedRouterState\n}\n\nexport type RouterConstructorOptions<\n  TRouteTree extends AnyRoute,\n  TDehydrated extends Record<string, any>,\n  TSerializedError extends Record<string, any>,\n> = Omit<RouterOptions<TRouteTree, TDehydrated, TSerializedError>, 'context'> &\n  RouterContextOptions<TRouteTree>\n\nexport const componentTypes = [\n  'component',\n  'errorComponent',\n  'pendingComponent',\n  'notFoundComponent',\n] as const\n\nexport type RouterEvents = {\n  onBeforeLoad: {\n    type: 'onBeforeLoad'\n    fromLocation: ParsedLocation\n    toLocation: ParsedLocation\n    pathChanged: boolean\n  }\n  onLoad: {\n    type: 'onLoad'\n    fromLocation: ParsedLocation\n    toLocation: ParsedLocation\n    pathChanged: boolean\n  }\n  onResolved: {\n    type: 'onResolved'\n    fromLocation: ParsedLocation\n    toLocation: ParsedLocation\n    pathChanged: boolean\n  }\n}\n\nexport type RouterEvent = RouterEvents[keyof RouterEvents]\n\nexport type RouterListener<TRouterEvent extends RouterEvent> = {\n  eventType: TRouterEvent['type']\n  fn: ListenerFn<TRouterEvent>\n}\n\nexport function createRouter<\n  TRouteTree extends AnyRoute = AnyRoute,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n  TSerializedError extends Record<string, any> = Record<string, any>,\n>(\n  options: RouterConstructorOptions<TRouteTree, TDehydrated, TSerializedError>,\n) {\n  return new Router<TRouteTree, TDehydrated, TSerializedError>(options)\n}\n\nexport class Router<\n  TRouteTree extends AnyRoute = AnyRoute,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n  TSerializedError extends Record<string, any> = Record<string, any>,\n> {\n  // Option-independent properties\n  tempLocationKey: string | undefined = `${Math.round(\n    Math.random() * 10000000,\n  )}`\n  resetNextScroll: boolean = true\n  navigateTimeout: Timeout | null = null\n  latestLoadPromise: Promise<void> = Promise.resolve()\n  subscribers = new Set<RouterListener<RouterEvent>>()\n  injectedHtml: InjectedHtmlEntry[] = []\n  dehydratedData?: TDehydrated\n\n  // Must build in constructor\n  __store!: Store<RouterState<TRouteTree>>\n  options!: PickAsRequired<\n    Omit<\n      RouterOptions<TRouteTree, TDehydrated, TSerializedError>,\n      'transformer'\n    > & {\n      transformer: RouterTransformer\n    },\n    'stringifySearch' | 'parseSearch' | 'context'\n  >\n  history!: RouterHistory\n  latestLocation!: ParsedLocation\n  basepath!: string\n  routeTree!: TRouteTree\n  routesById!: RoutesById<TRouteTree>\n  routesByPath!: RoutesByPath<TRouteTree>\n  flatRoutes!: AnyRoute[]\n\n  /**\n   * @deprecated Use the `createRouter` function instead\n   */\n  constructor(\n    options: RouterConstructorOptions<\n      TRouteTree,\n      TDehydrated,\n      TSerializedError\n    >,\n  ) {\n    this.update({\n      defaultPreloadDelay: 50,\n      defaultPendingMs: 1000,\n      defaultPendingMinMs: 500,\n      context: undefined!,\n      ...options,\n      stringifySearch: options?.stringifySearch ?? defaultStringifySearch,\n      parseSearch: options?.parseSearch ?? defaultParseSearch,\n      transformer: options?.transformer ?? JSON,\n    })\n\n    if (typeof document !== 'undefined') {\n      ;(window as any).__TSR__ROUTER__ = this\n    }\n  }\n\n  // These are default implementations that can optionally be overridden\n  // by the router provider once rendered. We provide these so that the\n  // router can be used in a non-react environment if necessary\n  startReactTransition: (fn: () => void) => void = (fn) => fn()\n\n  update = (\n    newOptions: RouterConstructorOptions<\n      TRouteTree,\n      TDehydrated,\n      TSerializedError\n    >,\n  ) => {\n    if (newOptions.notFoundRoute) {\n      console.warn(\n        'The notFoundRoute API is deprecated and will be removed in the next major version. See https://tanstack.com/router/v1/docs/guide/not-found-errors#migrating-from-notfoundroute for more info.',\n      )\n    }\n\n    const previousOptions = this.options\n    this.options = {\n      ...this.options,\n      ...newOptions,\n    }\n\n    if (\n      !this.basepath ||\n      (newOptions.basepath && newOptions.basepath !== previousOptions.basepath)\n    ) {\n      if (\n        newOptions.basepath === undefined ||\n        newOptions.basepath === '' ||\n        newOptions.basepath === '/'\n      ) {\n        this.basepath = '/'\n      } else {\n        this.basepath = `/${trimPath(newOptions.basepath)}`\n      }\n    }\n\n    if (\n      !this.history ||\n      (this.options.history && this.options.history !== this.history)\n    ) {\n      this.history =\n        this.options.history ??\n        (typeof document !== 'undefined'\n          ? createBrowserHistory()\n          : createMemoryHistory({\n              initialEntries: [this.options.basepath || '/'],\n            }))\n      this.latestLocation = this.parseLocation()\n    }\n\n    if (this.options.routeTree !== this.routeTree) {\n      this.routeTree = this.options.routeTree as TRouteTree\n      this.buildRouteTree()\n    }\n\n    if (!this.__store) {\n      this.__store = new Store(getInitialRouterState(this.latestLocation), {\n        onUpdate: () => {\n          this.__store.state = {\n            ...this.state,\n            status:\n              this.state.isTransitioning || this.state.isLoading\n                ? 'pending'\n                : 'idle',\n            cachedMatches: this.state.cachedMatches.filter(\n              (d) => !['redirected'].includes(d.status),\n            ),\n          }\n        },\n      })\n    }\n  }\n\n  get state() {\n    return this.__store.state\n  }\n\n  buildRouteTree = () => {\n    this.routesById = {} as RoutesById<TRouteTree>\n    this.routesByPath = {} as RoutesByPath<TRouteTree>\n\n    const notFoundRoute = this.options.notFoundRoute\n    if (notFoundRoute) {\n      notFoundRoute.init({ originalIndex: 99999999999 })\n      ;(this.routesById as any)[notFoundRoute.id] = notFoundRoute\n    }\n\n    const recurseRoutes = (childRoutes: AnyRoute[]) => {\n      childRoutes.forEach((childRoute, i) => {\n        childRoute.init({ originalIndex: i })\n\n        const existingRoute = (this.routesById as any)[childRoute.id]\n\n        invariant(\n          !existingRoute,\n          `Duplicate routes found with id: ${String(childRoute.id)}`,\n        )\n        ;(this.routesById as any)[childRoute.id] = childRoute\n\n        if (!childRoute.isRoot && childRoute.path) {\n          const trimmedFullPath = trimPathRight(childRoute.fullPath)\n          if (\n            !(this.routesByPath as any)[trimmedFullPath] ||\n            childRoute.fullPath.endsWith('/')\n          ) {\n            ;(this.routesByPath as any)[trimmedFullPath] = childRoute\n          }\n        }\n\n        const children = childRoute.children as Route[]\n\n        if (children?.length) {\n          recurseRoutes(children)\n        }\n      })\n    }\n\n    recurseRoutes([this.routeTree])\n\n    const scoredRoutes: {\n      child: AnyRoute\n      trimmed: string\n      parsed: ReturnType<typeof parsePathname>\n      index: number\n      scores: number[]\n    }[] = []\n\n    ;(Object.values(this.routesById) as AnyRoute[]).forEach((d, i) => {\n      if (d.isRoot || !d.path) {\n        return\n      }\n\n      const trimmed = trimPathLeft(d.fullPath)\n      const parsed = parsePathname(trimmed)\n\n      while (parsed.length > 1 && parsed[0]?.value === '/') {\n        parsed.shift()\n      }\n\n      const scores = parsed.map((d) => {\n        if (d.value === '/') {\n          return 0.75\n        }\n\n        if (d.type === 'param') {\n          return 0.5\n        }\n\n        if (d.type === 'wildcard') {\n          return 0.25\n        }\n\n        return 1\n      })\n\n      scoredRoutes.push({ child: d, trimmed, parsed, index: i, scores })\n    })\n\n    this.flatRoutes = scoredRoutes\n      .sort((a, b) => {\n        const minLength = Math.min(a.scores.length, b.scores.length)\n\n        // Sort by min available score\n        for (let i = 0; i < minLength; i++) {\n          if (a.scores[i] !== b.scores[i]) {\n            return b.scores[i]! - a.scores[i]!\n          }\n        }\n\n        // Sort by length of score\n        if (a.scores.length !== b.scores.length) {\n          return b.scores.length - a.scores.length\n        }\n\n        // Sort by min available parsed value\n        for (let i = 0; i < minLength; i++) {\n          if (a.parsed[i]!.value !== b.parsed[i]!.value) {\n            return a.parsed[i]!.value! > b.parsed[i]!.value! ? 1 : -1\n          }\n        }\n\n        // Sort by original index\n        return a.index - b.index\n      })\n      .map((d, i) => {\n        d.child.rank = i\n        return d.child\n      })\n  }\n\n  subscribe = <TType extends keyof RouterEvents>(\n    eventType: TType,\n    fn: ListenerFn<RouterEvents[TType]>,\n  ) => {\n    const listener: RouterListener<any> = {\n      eventType,\n      fn,\n    }\n\n    this.subscribers.add(listener)\n\n    return () => {\n      this.subscribers.delete(listener)\n    }\n  }\n\n  emit = (routerEvent: RouterEvent) => {\n    this.subscribers.forEach((listener) => {\n      if (listener.eventType === routerEvent.type) {\n        listener.fn(routerEvent)\n      }\n    })\n  }\n\n  checkLatest = (promise: Promise<void>): undefined | Promise<void> => {\n    return this.latestLoadPromise !== promise\n      ? this.latestLoadPromise\n      : undefined\n  }\n\n  parseLocation = (\n    previousLocation?: ParsedLocation,\n  ): ParsedLocation<FullSearchSchema<TRouteTree>> => {\n    const parse = ({\n      pathname,\n      search,\n      hash,\n      state,\n    }: HistoryLocation): ParsedLocation<FullSearchSchema<TRouteTree>> => {\n      const parsedSearch = this.options.parseSearch(search)\n      const searchStr = this.options.stringifySearch(parsedSearch)\n\n      return {\n        pathname: pathname,\n        searchStr,\n        search: replaceEqualDeep(previousLocation?.search, parsedSearch) as any,\n        hash: hash.split('#').reverse()[0] ?? '',\n        href: `${pathname}${searchStr}${hash}`,\n        state: replaceEqualDeep(previousLocation?.state, state) as HistoryState,\n      }\n    }\n\n    const location = parse(this.history.location)\n\n    let { __tempLocation, __tempKey } = location.state\n\n    if (__tempLocation && (!__tempKey || __tempKey === this.tempLocationKey)) {\n      // Sync up the location keys\n      const parsedTempLocation = parse(__tempLocation) as any\n      parsedTempLocation.state.key = location.state.key\n\n      delete parsedTempLocation.state.__tempLocation\n\n      return {\n        ...parsedTempLocation,\n        maskedLocation: location,\n      }\n    }\n\n    return location\n  }\n\n  resolvePathWithBase = (from: string, path: string) => {\n    return resolvePath(this.basepath!, from, cleanPath(path))\n  }\n\n  get looseRoutesById() {\n    return this.routesById as Record<string, AnyRoute>\n  }\n\n  matchRoutes = <TRouteTree extends AnyRoute>(\n    pathname: string,\n    locationSearch: AnySearchSchema,\n    opts?: { preload?: boolean; throwOnError?: boolean; debug?: boolean },\n  ): RouteMatch<TRouteTree>[] => {\n    let routeParams: Record<string, string> = {}\n\n    let foundRoute = this.flatRoutes.find((route) => {\n      const matchedParams = matchPathname(\n        this.basepath,\n        trimPathRight(pathname),\n        {\n          to: route.fullPath,\n          caseSensitive:\n            route.options.caseSensitive ?? this.options.caseSensitive,\n          fuzzy: true,\n        },\n      )\n\n      if (matchedParams) {\n        routeParams = matchedParams\n        return true\n      }\n\n      return false\n    })\n\n    let routeCursor: AnyRoute =\n      foundRoute || (this.routesById as any)[rootRouteId]\n\n    let matchedRoutes: AnyRoute[] = [routeCursor]\n\n    let isGlobalNotFound = false\n\n    // Check to see if the route needs a 404 entry\n    if (\n      // If we found a route, and it's not an index route and we have left over path\n      foundRoute\n        ? foundRoute.path !== '/' && routeParams['**']\n        : // Or if we didn't find a route and we have left over path\n          trimPathRight(pathname)\n    ) {\n      // If the user has defined an (old) 404 route, use it\n      if (this.options.notFoundRoute) {\n        matchedRoutes.push(this.options.notFoundRoute)\n      } else {\n        // If there is no routes found during path matching\n        isGlobalNotFound = true\n      }\n    }\n\n    while (routeCursor?.parentRoute) {\n      routeCursor = routeCursor.parentRoute\n      if (routeCursor) matchedRoutes.unshift(routeCursor)\n    }\n\n    const globalNotFoundRouteId = (() => {\n      if (!isGlobalNotFound) {\n        return undefined\n      }\n\n      if (this.options.notFoundMode !== 'root') {\n        for (let i = matchedRoutes.length - 1; i >= 0; i--) {\n          const route = matchedRoutes[i]!\n          if (route.children) {\n            return route.id\n          }\n        }\n      }\n\n      return rootRouteId\n    })()\n\n    // Existing matches are matches that are already loaded along with\n    // pending matches that are still loading\n\n    const parseErrors = matchedRoutes.map((route) => {\n      let parsedParamsError\n\n      if (route.options.parseParams) {\n        try {\n          const parsedParams = route.options.parseParams(routeParams)\n          // Add the parsed params to the accumulated params bag\n          Object.assign(routeParams, parsedParams)\n        } catch (err: any) {\n          parsedParamsError = new PathParamError(err.message, {\n            cause: err,\n          })\n\n          if (opts?.throwOnError) {\n            throw parsedParamsError\n          }\n\n          return parsedParamsError\n        }\n      }\n\n      return\n    })\n\n    const matches: AnyRouteMatch[] = []\n\n    matchedRoutes.forEach((route, index) => {\n      // Take each matched route and resolve + validate its search params\n      // This has to happen serially because each route's search params\n      // can depend on the parent route's search params\n      // It must also happen before we create the match so that we can\n      // pass the search params to the route's potential key function\n      // which is used to uniquely identify the route match in state\n\n      const parentMatch = matches[index - 1]\n      const isLast = index === matchedRoutes.length - 1\n\n      const [preMatchSearch, searchError]: [Record<string, any>, any] = (() => {\n        // Validate the search params and stabilize them\n        const parentSearch = parentMatch?.search ?? locationSearch\n\n        try {\n          const validator =\n            typeof route.options.validateSearch === 'object'\n              ? route.options.validateSearch.parse\n              : route.options.validateSearch\n\n          let search = validator?.(parentSearch) ?? {}\n\n          return [\n            {\n              ...parentSearch,\n              ...search,\n            },\n            undefined,\n          ]\n        } catch (err: any) {\n          const searchError = new SearchParamError(err.message, {\n            cause: err,\n          })\n\n          if (opts?.throwOnError) {\n            throw searchError\n          }\n\n          return [parentSearch, searchError]\n        }\n      })()\n\n      // This is where we need to call route.options.loaderDeps() to get any additional\n      // deps that the route's loader function might need to run. We need to do this\n      // before we create the match so that we can pass the deps to the route's\n      // potential key function which is used to uniquely identify the route match in state\n\n      const loaderDeps =\n        route.options.loaderDeps?.({\n          search: preMatchSearch,\n        }) ?? ''\n\n      const loaderDepsHash = loaderDeps ? JSON.stringify(loaderDeps) : ''\n\n      const interpolatedPath = interpolatePath({\n        path: route.fullPath,\n        params: routeParams,\n      })\n\n      const matchId =\n        interpolatePath({\n          path: route.id,\n          params: routeParams,\n          leaveWildcards: true,\n        }) + loaderDepsHash\n\n      // Waste not, want not. If we already have a match for this route,\n      // reuse it. This is important for layout routes, which might stick\n      // around between navigation actions that only change leaf routes.\n      let existingMatch = getRouteMatch(this.state, matchId)\n\n      const cause = this.state.matches.find((d) => d.id === matchId)\n        ? 'stay'\n        : 'enter'\n\n      const match: AnyRouteMatch = existingMatch\n        ? {\n            ...existingMatch,\n            cause,\n            params: routeParams,\n          }\n        : {\n            id: matchId,\n            routeId: route.id,\n            params: routeParams,\n            pathname: joinPaths([this.basepath, interpolatedPath]),\n            updatedAt: Date.now(),\n            search: {} as any,\n            searchError: undefined,\n            status: 'pending',\n            showPending: false,\n            isFetching: false,\n            error: undefined,\n            paramsError: parseErrors[index],\n            loadPromise: Promise.resolve(),\n            routeContext: undefined!,\n            context: undefined!,\n            abortController: new AbortController(),\n            fetchCount: 0,\n            cause,\n            loaderDeps,\n            invalid: false,\n            preload: false,\n            links: route.options.links?.(),\n            scripts: route.options.scripts?.(),\n            staticData: route.options.staticData || {},\n          }\n\n      if (!opts?.preload) {\n        // If we have a global not found, mark the right match as global not found\n        match.globalNotFound = globalNotFoundRouteId === route.id\n      }\n\n      // Regardless of whether we're reusing an existing match or creating\n      // a new one, we need to update the match's search params\n      match.search = replaceEqualDeep(match.search, preMatchSearch)\n      // And also update the searchError if there is one\n      match.searchError = searchError\n\n      matches.push(match)\n    })\n\n    return matches as any\n  }\n\n  cancelMatch = (id: string) => {}\n\n  cancelMatches = () => {\n    this.state.pendingMatches?.forEach((match) => {\n      this.cancelMatch(match.id)\n    })\n  }\n\n  buildLocation: BuildLocationFn<TRouteTree> = (opts) => {\n    const build = (\n      dest: BuildNextOptions & {\n        unmaskOnReload?: boolean\n      } = {},\n      matches?: AnyRouteMatch[],\n    ): ParsedLocation => {\n      // if (dest.href) {\n      //   return {\n      //     pathname: dest.href,\n      //     search: {},\n      //     searchStr: '',\n      //     state: {},\n      //     hash: '',\n      //     href: dest.href,\n      //     unmaskOnReload: dest.unmaskOnReload,\n      //   }\n      // }\n\n      const relevantMatches = this.state.pendingMatches || this.state.matches\n      const fromSearch =\n        relevantMatches[relevantMatches.length - 1]?.search ||\n        this.latestLocation.search\n\n      const fromMatches = this.matchRoutes(\n        this.latestLocation.pathname,\n        fromSearch,\n      )\n      const stayingMatches = matches?.filter((d) =>\n        fromMatches?.find((e) => e.routeId === d.routeId),\n      )\n\n      const fromRoute = this.looseRoutesById[last(fromMatches)?.routeId]\n\n      let pathname = dest.to\n        ? this.resolvePathWithBase(\n            dest.from ?? this.latestLocation.pathname,\n            `${dest.to}`,\n          )\n        : fromRoute?.fullPath\n\n      const prevParams = { ...last(fromMatches)?.params }\n\n      let nextParams =\n        (dest.params ?? true) === true\n          ? prevParams\n          : { ...prevParams, ...functionalUpdate(dest.params!, prevParams) }\n\n      if (nextParams) {\n        matches\n          ?.map((d) => this.looseRoutesById[d.routeId]!.options.stringifyParams)\n          .filter(Boolean)\n          .forEach((fn) => {\n            nextParams = { ...nextParams!, ...fn!(nextParams!) }\n          })\n      }\n\n      pathname = interpolatePath({\n        path: pathname,\n        params: nextParams ?? {},\n        leaveWildcards: false,\n        leaveParams: opts.leaveParams,\n      })\n\n      const preSearchFilters =\n        stayingMatches\n          ?.map(\n            (match) =>\n              this.looseRoutesById[match.routeId]!.options.preSearchFilters ??\n              [],\n          )\n          .flat()\n          .filter(Boolean) ?? []\n\n      const postSearchFilters =\n        stayingMatches\n          ?.map(\n            (match) =>\n              this.looseRoutesById[match.routeId]!.options.postSearchFilters ??\n              [],\n          )\n          .flat()\n          .filter(Boolean) ?? []\n\n      // Pre filters first\n      const preFilteredSearch = preSearchFilters?.length\n        ? preSearchFilters?.reduce(\n            (prev, next) => next(prev) as any,\n            fromSearch,\n          )\n        : fromSearch\n\n      // Then the link/navigate function\n      const destSearch =\n        dest.search === true\n          ? preFilteredSearch // Preserve resolvedFrom true\n          : dest.search\n            ? functionalUpdate(dest.search, preFilteredSearch) ?? {} // Updater\n            : preSearchFilters?.length\n              ? preFilteredSearch // Preserve resolvedFrom filters\n              : {}\n\n      // Then post filters\n      const postFilteredSearch = postSearchFilters?.length\n        ? postSearchFilters.reduce((prev, next) => next(prev), destSearch)\n        : destSearch\n\n      const search = replaceEqualDeep(fromSearch, postFilteredSearch)\n\n      const searchStr = this.options.stringifySearch(search)\n\n      const hash =\n        dest.hash === true\n          ? this.latestLocation.hash\n          : dest.hash\n            ? functionalUpdate(dest.hash!, this.latestLocation.hash)\n            : undefined\n\n      const hashStr = hash ? `#${hash}` : ''\n\n      let nextState =\n        dest.state === true\n          ? this.latestLocation.state\n          : dest.state\n            ? functionalUpdate(dest.state, this.latestLocation.state)\n            : {}\n\n      nextState = replaceEqualDeep(this.latestLocation.state, nextState)\n\n      return {\n        pathname,\n        search,\n        searchStr,\n        state: nextState as any,\n        hash: hash ?? '',\n        href: `${pathname}${searchStr}${hashStr}`,\n        unmaskOnReload: dest.unmaskOnReload,\n      }\n    }\n\n    const buildWithMatches = (\n      dest: BuildNextOptions = {},\n      maskedDest?: BuildNextOptions,\n    ) => {\n      let next = build(dest)\n      let maskedNext = maskedDest ? build(maskedDest) : undefined\n\n      if (!maskedNext) {\n        let params = {}\n\n        let foundMask = this.options.routeMasks?.find((d) => {\n          const match = matchPathname(this.basepath, next.pathname, {\n            to: d.from,\n            caseSensitive: false,\n            fuzzy: false,\n          })\n\n          if (match) {\n            params = match\n            return true\n          }\n\n          return false\n        })\n\n        if (foundMask) {\n          maskedDest = {\n            ...pick(opts, ['from']),\n            ...foundMask,\n            params,\n          }\n          maskedNext = build(maskedDest)\n        }\n      }\n\n      const nextMatches = this.matchRoutes(next.pathname, next.search)\n      const maskedMatches = maskedNext\n        ? this.matchRoutes(maskedNext.pathname, maskedNext.search)\n        : undefined\n      const maskedFinal = maskedNext\n        ? build(maskedDest, maskedMatches)\n        : undefined\n\n      const final = build(dest, nextMatches)\n\n      if (maskedFinal) {\n        final.maskedLocation = maskedFinal\n      }\n\n      return final\n    }\n\n    if (opts.mask) {\n      return buildWithMatches(opts, {\n        ...pick(opts, ['from']),\n        ...opts.mask,\n      })\n    }\n\n    return buildWithMatches(opts)\n  }\n\n  commitLocation = async ({\n    startTransition,\n    ...next\n  }: ParsedLocation & CommitLocationOptions) => {\n    if (this.navigateTimeout) clearTimeout(this.navigateTimeout)\n\n    const isSameUrl = this.latestLocation.href === next.href\n\n    // If the next urls are the same and we're not replacing,\n    // do nothing\n    if (!isSameUrl) {\n      let { maskedLocation, ...nextHistory } = next\n\n      if (maskedLocation) {\n        nextHistory = {\n          ...maskedLocation,\n          state: {\n            ...maskedLocation.state,\n            __tempKey: undefined,\n            __tempLocation: {\n              ...nextHistory,\n              search: nextHistory.searchStr,\n              state: {\n                ...nextHistory.state,\n                __tempKey: undefined!,\n                __tempLocation: undefined!,\n                key: undefined!,\n              },\n            },\n          },\n        }\n\n        if (\n          nextHistory.unmaskOnReload ??\n          this.options.unmaskOnReload ??\n          false\n        ) {\n          nextHistory.state.__tempKey = this.tempLocationKey\n        }\n      }\n\n      const apply = () => {\n        this.history[next.replace ? 'replace' : 'push'](\n          nextHistory.href,\n          nextHistory.state,\n        )\n      }\n\n      if (startTransition ?? true) {\n        this.startReactTransition(apply)\n      } else {\n        apply()\n      }\n    }\n\n    this.resetNextScroll = next.resetScroll ?? true\n\n    return this.latestLoadPromise\n  }\n\n  buildAndCommitLocation = ({\n    replace,\n    resetScroll,\n    startTransition,\n    ...rest\n  }: BuildNextOptions & CommitLocationOptions = {}) => {\n    const location = this.buildLocation(rest as any)\n    return this.commitLocation({\n      ...location,\n      startTransition,\n      replace,\n      resetScroll,\n    })\n  }\n\n  navigate: NavigateFn = ({ from, to, ...rest }) => {\n    // If this link simply reloads the current route,\n    // make sure it has a new key so it will trigger a data refresh\n\n    // If this `to` is a valid external URL, return\n    // null for LinkUtils\n    const toString = String(to)\n    // const fromString = from !== undefined ? String(from) : from\n    let isExternal\n\n    try {\n      new URL(`${toString}`)\n      isExternal = true\n    } catch (e) {}\n\n    invariant(\n      !isExternal,\n      'Attempting to navigate to external url with this.navigate!',\n    )\n\n    return this.buildAndCommitLocation({\n      ...rest,\n      from,\n      to,\n      // to: toString,\n    })\n  }\n\n  loadMatches = async ({\n    checkLatest,\n    location,\n    matches,\n    preload,\n  }: {\n    checkLatest: () => Promise<void> | undefined\n    location: ParsedLocation\n    matches: AnyRouteMatch[]\n    preload?: boolean\n  }): Promise<RouteMatch[]> => {\n    let latestPromise\n    let firstBadMatchIndex: number | undefined\n\n    const updateMatch = (match: AnyRouteMatch, opts?: { remove?: boolean }) => {\n      const isPending = this.state.pendingMatches?.find(\n        (d) => d.id === match.id,\n      )\n\n      const isMatched = this.state.matches.find((d) => d.id === match.id)\n\n      const matchesKey = isPending\n        ? 'pendingMatches'\n        : isMatched\n          ? 'matches'\n          : 'cachedMatches'\n\n      this.__store.setState((s) => ({\n        ...s,\n        [matchesKey]: opts?.remove\n          ? s[matchesKey]?.filter((d) => d.id !== match.id)\n          : s[matchesKey]?.map((d) => (d.id === match.id ? match : d)),\n      }))\n    }\n\n    const handleMatchSpecialError = (match: AnyRouteMatch, err: any) => {\n      match = {\n        ...match,\n        status: isRedirect(err)\n          ? 'redirected'\n          : isNotFound(err)\n            ? 'notFound'\n            : 'error',\n        isFetching: false,\n        error: err,\n      }\n\n      updateMatch(match)\n\n      if (!err.routeId) {\n        err.routeId = match.routeId\n      }\n\n      throw err\n    }\n\n    // Check each match middleware to see if the route can be accessed\n    for (let [index, match] of matches.entries()) {\n      const parentMatch = matches[index - 1]\n      const route = this.looseRoutesById[match.routeId]!\n      const abortController = new AbortController()\n\n      const handleSerialError = (err: any, code: string) => {\n        err.routerCode = code\n        firstBadMatchIndex = firstBadMatchIndex ?? index\n\n        if (isRedirect(err) || isNotFound(err)) {\n          handleMatchSpecialError(match, err)\n        }\n\n        try {\n          route.options.onError?.(err)\n        } catch (errorHandlerErr) {\n          err = errorHandlerErr\n\n          if (isRedirect(err) || isNotFound(err)) {\n            handleMatchSpecialError(match, errorHandlerErr)\n          }\n        }\n\n        matches[index] = match = {\n          ...match,\n          error: err,\n          status: 'error',\n          updatedAt: Date.now(),\n          abortController: new AbortController(),\n        }\n      }\n\n      if (match.paramsError) {\n        handleSerialError(match.paramsError, 'PARSE_PARAMS')\n      }\n\n      if (match.searchError) {\n        handleSerialError(match.searchError, 'VALIDATE_SEARCH')\n      }\n\n      // if (match.globalNotFound && !preload) {\n      //   handleSerialError(notFound({ _global: true }), 'NOT_FOUND')\n      // }\n\n      try {\n        const parentContext = parentMatch?.context ?? this.options.context ?? {}\n\n        const pendingMs =\n          route.options.pendingMs ?? this.options.defaultPendingMs\n        const pendingPromise =\n          typeof pendingMs === 'number' && pendingMs <= 0\n            ? Promise.resolve()\n            : new Promise<void>((r) => setTimeout(r, pendingMs))\n\n        const beforeLoadContext =\n          (await route.options.beforeLoad?.({\n            search: match.search,\n            abortController,\n            params: match.params,\n            preload: !!preload,\n            context: parentContext,\n            location,\n            navigate: (opts) =>\n              this.navigate({ ...opts, from: match.pathname } as any),\n            buildLocation: this.buildLocation,\n            cause: preload ? 'preload' : match.cause,\n          })) ?? ({} as any)\n\n        if (isRedirect(beforeLoadContext) || isNotFound(beforeLoadContext)) {\n          handleSerialError(beforeLoadContext, 'BEFORE_LOAD')\n        }\n\n        const context = {\n          ...parentContext,\n          ...beforeLoadContext,\n        }\n\n        matches[index] = match = {\n          ...match,\n          routeContext: replaceEqualDeep(match.routeContext, beforeLoadContext),\n          context: replaceEqualDeep(match.context, context),\n          abortController,\n          pendingPromise,\n        }\n      } catch (err) {\n        handleSerialError(err, 'BEFORE_LOAD')\n        break\n      }\n    }\n\n    const validResolvedMatches = matches.slice(0, firstBadMatchIndex)\n    const matchPromises: Promise<any>[] = []\n\n    validResolvedMatches.forEach((match, index) => {\n      matchPromises.push(\n        new Promise<void>(async (resolve, reject) => {\n          const parentMatchPromise = matchPromises[index - 1]\n          const route = this.looseRoutesById[match.routeId]!\n\n          const handleError = (err: any) => {\n            if (isRedirect(err) || isNotFound(err)) {\n              handleMatchSpecialError(match, err)\n            }\n          }\n\n          let loadPromise: Promise<void> | undefined\n\n          matches[index] = match = {\n            ...match,\n            showPending: false,\n          }\n\n          let didShowPending = false\n          const pendingMs =\n            route.options.pendingMs ?? this.options.defaultPendingMs\n          const pendingMinMs =\n            route.options.pendingMinMs ?? this.options.defaultPendingMinMs\n\n          const loaderContext: LoaderFnContext = {\n            params: match.params,\n            deps: match.loaderDeps,\n            preload: !!preload,\n            parentMatchPromise,\n            abortController: match.abortController,\n            context: match.context,\n            location,\n            navigate: (opts) =>\n              this.navigate({ ...opts, from: match.pathname } as any),\n            cause: preload ? 'preload' : match.cause,\n            route,\n          }\n\n          const fetch = async () => {\n            try {\n              if (match.isFetching) {\n                loadPromise = getRouteMatch(this.state, match.id)?.loadPromise\n              } else {\n                // If the user doesn't want the route to reload, just\n                // resolve with the existing loader data\n\n                // if (match.fetchCount && match.status === 'success') {\n                //   resolve()\n                // }\n\n                // Otherwise, load the route\n                matches[index] = match = {\n                  ...match,\n                  isFetching: true,\n                  fetchCount: match.fetchCount + 1,\n                }\n\n                const lazyPromise =\n                  route.lazyFn?.().then((lazyRoute) => {\n                    Object.assign(route.options, lazyRoute.options)\n                  }) || Promise.resolve()\n\n                // If for some reason lazy resolves more lazy components...\n                // We'll wait for that before pre attempt to preload any\n                // components themselves.\n                const componentsPromise = lazyPromise.then(() =>\n                  Promise.all(\n                    componentTypes.map(async (type) => {\n                      const component = route.options[type]\n\n                      if ((component as any)?.preload) {\n                        await (component as any).preload()\n                      }\n                    }),\n                  ),\n                )\n\n                // Kick off the loader!\n                const loaderPromise = route.options.loader?.(loaderContext)\n\n                loadPromise = Promise.all([\n                  componentsPromise,\n                  loaderPromise,\n                  lazyPromise,\n                ]).then((d) => d[1])\n              }\n\n              matches[index] = match = {\n                ...match,\n                loadPromise,\n              }\n\n              updateMatch(match)\n\n              const loaderData = await loadPromise\n              if ((latestPromise = checkLatest())) return await latestPromise\n\n              handleError(loaderData)\n\n              if (didShowPending && pendingMinMs) {\n                await new Promise((r) => setTimeout(r, pendingMinMs))\n              }\n\n              if ((latestPromise = checkLatest())) return await latestPromise\n\n              const [meta, headers] = await Promise.all([\n                route.options.meta?.({\n                  params: match.params,\n                  loaderData,\n                }),\n                route.options.headers?.({\n                  loaderData,\n                }),\n              ])\n\n              matches[index] = match = {\n                ...match,\n                error: undefined,\n                status: 'success',\n                isFetching: false,\n                updatedAt: Date.now(),\n                loaderData,\n                loadPromise: undefined,\n                meta,\n                headers,\n              }\n            } catch (error) {\n              if ((latestPromise = checkLatest())) return await latestPromise\n\n              handleError(error)\n\n              try {\n                route.options.onError?.(error)\n              } catch (onErrorError) {\n                error = onErrorError\n                handleError(onErrorError)\n              }\n\n              matches[index] = match = {\n                ...match,\n                error,\n                status: 'error',\n                isFetching: false,\n              }\n            }\n\n            updateMatch(match)\n          }\n\n          // This is where all of the stale-while-revalidate magic happens\n          const age = Date.now() - match.updatedAt\n\n          let staleAge = preload\n            ? route.options.preloadStaleTime ??\n              this.options.defaultPreloadStaleTime ??\n              30_000 // 30 seconds for preloads by default\n            : route.options.staleTime ?? this.options.defaultStaleTime ?? 0\n\n          // Default to reloading the route all the time\n          let shouldReload\n\n          const shouldReloadOption = route.options.shouldReload\n\n          // Allow shouldReload to get the last say,\n          // if provided.\n          shouldReload =\n            typeof shouldReloadOption === 'function'\n              ? shouldReloadOption(loaderContext)\n              : shouldReloadOption\n\n          matches[index] = match = {\n            ...match,\n            preload:\n              !!preload && !this.state.matches.find((d) => d.id === match.id),\n          }\n\n          // If the route is successful and still fresh, just resolve\n          if (\n            match.status === 'success' &&\n            (match.invalid || (shouldReload ?? age > staleAge))\n          ) {\n            ;(async () => {\n              try {\n                await fetch()\n              } catch (err) {\n                console.info('Background Fetching Error', err)\n\n                if (isRedirect(err)) {\n                  const isActive = (\n                    this.state.pendingMatches || this.state.matches\n                  ).find((d) => d.id === match.id)\n\n                  // Redirects should not be persisted\n                  handleError(err)\n\n                  // If the route is still active, redirect\n                  if (isActive) {\n                    this.handleRedirect(err)\n                  }\n                }\n              }\n            })()\n\n            return resolve()\n          }\n\n          const shouldPending =\n            !preload &&\n            typeof pendingMs === 'number' &&\n            (route.options.pendingComponent ??\n              this.options.defaultPendingComponent)\n\n          if (match.status !== 'success') {\n            try {\n              if (shouldPending) {\n                match.pendingPromise?.then(async () => {\n                  if ((latestPromise = checkLatest())) return latestPromise\n\n                  didShowPending = true\n                  matches[index] = match = {\n                    ...match,\n                    showPending: true,\n                  }\n\n                  updateMatch(match)\n                  resolve()\n                })\n              }\n\n              await fetch()\n            } catch (err) {\n              reject(err)\n            }\n          }\n\n          resolve()\n        }),\n      )\n    })\n\n    await Promise.all(matchPromises)\n\n    return matches\n  }\n\n  invalidate = () => {\n    const invalidate = (d: any) => ({\n      ...d,\n      invalid: true,\n    })\n\n    this.__store.setState((s) => ({\n      ...s,\n      matches: s.matches.map(invalidate),\n      cachedMatches: s.cachedMatches.map(invalidate),\n      pendingMatches: s.pendingMatches?.map(invalidate),\n    }))\n\n    this.load()\n  }\n\n  load = async (): Promise<void> => {\n    const promise = new Promise<void>(async (resolve, reject) => {\n      const next = this.latestLocation\n      const prevLocation = this.state.resolvedLocation\n      const pathDidChange = prevLocation!.href !== next.href\n      let latestPromise: Promise<void> | undefined | null\n\n      // Cancel any pending matches\n      this.cancelMatches()\n\n      this.emit({\n        type: 'onBeforeLoad',\n        fromLocation: prevLocation,\n        toLocation: next,\n        pathChanged: pathDidChange,\n      })\n\n      let pendingMatches!: RouteMatch<any, any>[]\n      const previousMatches = this.state.matches\n\n      this.__store.batch(() => {\n        this.cleanCache()\n\n        // Match the routes\n        pendingMatches = this.matchRoutes(next.pathname, next.search, {\n          debug: true,\n        })\n\n        // Ingest the new matches\n        // If a cached moved to pendingMatches, remove it from cachedMatches\n        this.__store.setState((s) => ({\n          ...s,\n          isLoading: true,\n          location: next,\n          pendingMatches,\n          cachedMatches: s.cachedMatches.filter((d) => {\n            return !pendingMatches.find((e) => e.id === d.id)\n          }),\n        }))\n      })\n\n      try {\n        let redirected: AnyRedirect\n        let notFound: NotFoundError\n\n        try {\n          // Load the matches\n          await this.loadMatches({\n            matches: pendingMatches,\n            location: next,\n            checkLatest: () => this.checkLatest(promise),\n          })\n        } catch (err) {\n          if (isRedirect(err)) {\n            redirected = err\n            this.handleRedirect(err)\n          } else if (isNotFound(err)) {\n            notFound = err\n            this.handleNotFound(pendingMatches, err)\n          }\n\n          // Swallow all other errors that happen inside\n          // of loadMatches. These errors will be handled\n          // as state on each match.\n        }\n\n        // Only apply the latest transition\n        if ((latestPromise = this.checkLatest(promise))) {\n          return latestPromise\n        }\n\n        const exitingMatches = previousMatches.filter(\n          (match) => !pendingMatches.find((d) => d.id === match.id),\n        )\n        const enteringMatches = pendingMatches.filter(\n          (match) => !previousMatches.find((d) => d.id === match.id),\n        )\n        const stayingMatches = previousMatches.filter((match) =>\n          pendingMatches.find((d) => d.id === match.id),\n        )\n\n        // Commit the pending matches. If a previous match was\n        // removed, place it in the cachedMatches\n        this.__store.batch(() => {\n          this.__store.setState((s) => ({\n            ...s,\n            isLoading: false,\n            matches: s.pendingMatches!,\n            pendingMatches: undefined,\n            cachedMatches: [\n              ...s.cachedMatches,\n              ...exitingMatches.filter((d) => d.status !== 'error'),\n            ],\n            statusCode:\n              redirected?.code || notFound\n                ? 404\n                : s.matches.some((d) => d.status === 'error')\n                  ? 500\n                  : 200,\n          }))\n          this.cleanCache()\n        })\n\n        //\n        ;(\n          [\n            [exitingMatches, 'onLeave'],\n            [enteringMatches, 'onEnter'],\n            [stayingMatches, 'onStay'],\n          ] as const\n        ).forEach(([matches, hook]) => {\n          matches.forEach((match) => {\n            this.looseRoutesById[match.routeId]!.options[hook]?.(match)\n          })\n        })\n\n        this.emit({\n          type: 'onLoad',\n          fromLocation: prevLocation,\n          toLocation: next,\n          pathChanged: pathDidChange,\n        })\n\n        resolve()\n      } catch (err) {\n        // Only apply the latest transition\n        if ((latestPromise = this.checkLatest(promise))) {\n          return latestPromise\n        }\n\n        console.log('Load Error', err)\n\n        reject(err)\n      }\n    })\n\n    this.latestLoadPromise = promise\n\n    return this.latestLoadPromise\n  }\n\n  handleRedirect = (err: AnyRedirect) => {\n    if (!err.href) {\n      err.href = this.buildLocation(err as any).href\n    }\n    if (!isServer) {\n      this.navigate({ ...(err as any), replace: true })\n    }\n  }\n\n  cleanCache = () => {\n    // This is where all of the garbage collection magic happens\n    this.__store.setState((s) => {\n      return {\n        ...s,\n        cachedMatches: s.cachedMatches.filter((d) => {\n          const route = this.looseRoutesById[d.routeId]!\n\n          if (!route.options.loader) {\n            return false\n          }\n\n          // If the route was preloaded, use the preloadGcTime\n          // otherwise, use the gcTime\n          const gcTime =\n            (d.preload\n              ? route.options.preloadGcTime ?? this.options.defaultPreloadGcTime\n              : route.options.gcTime ?? this.options.defaultGcTime) ??\n            5 * 60 * 1000\n\n          return d.status !== 'error' && Date.now() - d.updatedAt < gcTime\n        }),\n      }\n    })\n  }\n\n  preloadRoute = async <\n    TFrom extends RoutePaths<TRouteTree> | string = string,\n    TTo extends string = '',\n    TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n    TMaskTo extends string = '',\n  >(\n    opts: NavigateOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo>,\n  ): Promise<AnyRouteMatch[] | undefined> => {\n    let next = this.buildLocation(opts as any)\n\n    let matches = this.matchRoutes(next.pathname, next.search, {\n      throwOnError: true,\n      preload: true,\n    })\n\n    const loadedMatchIds = Object.fromEntries(\n      [\n        ...this.state.matches,\n        ...(this.state.pendingMatches ?? []),\n        ...this.state.cachedMatches,\n      ]?.map((d) => [d.id, true]),\n    )\n\n    this.__store.batch(() => {\n      matches.forEach((match) => {\n        if (!loadedMatchIds[match.id]) {\n          this.__store.setState((s) => ({\n            ...s,\n            cachedMatches: [...(s.cachedMatches as any), match],\n          }))\n        }\n      })\n    })\n\n    try {\n      matches = await this.loadMatches({\n        matches,\n        location: next,\n        preload: true,\n        checkLatest: () => undefined,\n      })\n\n      return matches\n    } catch (err) {\n      if (isRedirect(err)) {\n        return await this.preloadRoute(err as any)\n      }\n      // Preload errors are not fatal, but we should still log them\n      console.error(err)\n      return undefined\n    }\n  }\n\n  matchRoute = <\n    TFrom extends RoutePaths<TRouteTree> = '/',\n    TTo extends string = '',\n    TResolved = ResolveRelativePath<TFrom, NoInfer<TTo>>,\n  >(\n    location: ToOptions<TRouteTree, TFrom, TTo>,\n    opts?: MatchRouteOptions,\n  ): false | RouteById<TRouteTree, TResolved>['types']['allParams'] => {\n    const matchLocation = {\n      ...location,\n      to: location.to\n        ? this.resolvePathWithBase((location.from || '') as string, location.to)\n        : undefined,\n      params: location.params || {},\n      leaveParams: true,\n    }\n    const next = this.buildLocation(matchLocation as any)\n\n    if (opts?.pending && this.state.status !== 'pending') {\n      return false\n    }\n\n    const baseLocation = opts?.pending\n      ? this.latestLocation\n      : this.state.resolvedLocation\n\n    if (!baseLocation) {\n      return false\n    }\n    const match = matchPathname(this.basepath, baseLocation.pathname, {\n      ...opts,\n      to: next.pathname,\n    }) as any\n\n    if (!match) {\n      return false\n    }\n    if (location.params) {\n      if (!deepEqual(match, location.params, true)) {\n        return false\n      }\n    }\n\n    if (match && (opts?.includeSearch ?? true)) {\n      return deepEqual(baseLocation.search, next.search, true) ? match : false\n    }\n\n    return match\n  }\n\n  injectHtml = async (html: string | (() => Promise<string> | string)) => {\n    this.injectedHtml.push(html)\n  }\n\n  // We use a token -> weak map to keep track of deferred promises\n  // that are registered on the server and need to be resolved\n  registeredDeferredsIds = new Map<string, {}>()\n  registeredDeferreds = new WeakMap<{}, DeferredPromiseState<any>>()\n\n  getDeferred = (uid: string) => {\n    const token = this.registeredDeferredsIds.get(uid)\n\n    if (!token) {\n      return undefined\n    }\n\n    return this.registeredDeferreds.get(token)\n  }\n\n  /**\n   * @deprecated Please inject your own html using the `injectHtml` method\n   */\n  dehydrateData = <T>(key: any, getData: T | (() => Promise<T> | T)) => {\n    warning(\n      false,\n      `The dehydrateData method is deprecated. Please use the injectHtml method to inject your own data.`,\n    )\n\n    if (typeof document === 'undefined') {\n      const strKey = typeof key === 'string' ? key : JSON.stringify(key)\n\n      this.injectHtml(async () => {\n        const id = `__TSR_DEHYDRATED__${strKey}`\n        const data =\n          typeof getData === 'function' ? await (getData as any)() : getData\n        return `<script id='${id}' suppressHydrationWarning>\n  window[\"__TSR_DEHYDRATED__${escapeJSON(\n    strKey,\n  )}\"] = ${JSON.stringify(this.options.transformer.stringify(data))}\n</script>`\n      })\n\n      return () => this.hydrateData<T>(key)\n    }\n\n    return () => undefined\n  }\n\n  /**\n   * @deprecated Please extract your own data from scripts injected using the `injectHtml` method\n   */\n  hydrateData = <T extends any = unknown>(key: any) => {\n    warning(\n      false,\n      `The hydrateData method is deprecated. Please use the extractHtml method to extract your own data.`,\n    )\n\n    if (typeof document !== 'undefined') {\n      const strKey = typeof key === 'string' ? key : JSON.stringify(key)\n\n      return this.options.transformer.parse(\n        window[`__TSR_DEHYDRATED__${strKey}` as any] as unknown as string,\n      ) as T\n    }\n\n    return undefined\n  }\n\n  dehydrate = (): DehydratedRouter => {\n    const pickError =\n      this.options.errorSerializer?.serialize ?? defaultSerializeError\n\n    return {\n      state: {\n        dehydratedMatches: this.state.matches.map((d) => ({\n          ...pick(d, ['id', 'status', 'updatedAt', 'loaderData']),\n          // If an error occurs server-side during SSRing,\n          // send a small subset of the error to the client\n          error: d.error\n            ? {\n                data: pickError(d.error),\n                __isServerError: true,\n              }\n            : undefined,\n        })),\n      },\n    }\n  }\n\n  hydrate = async (__do_not_use_server_ctx?: string) => {\n    let _ctx = __do_not_use_server_ctx\n    // Client hydrates from window\n    if (typeof document !== 'undefined') {\n      _ctx = window.__TSR_DEHYDRATED__?.data\n    }\n\n    invariant(\n      _ctx,\n      'Expected to find a __TSR_DEHYDRATED__ property on window... but we did not. Did you forget to render <DehydrateRouter /> in your app?',\n    )\n\n    const ctx = this.options.transformer.parse(_ctx) as HydrationCtx\n    this.dehydratedData = ctx.payload as any\n    this.options.hydrate?.(ctx.payload as any)\n    const dehydratedState = ctx.router.state\n\n    let matches = this.matchRoutes(\n      this.state.location.pathname,\n      this.state.location.search,\n    ).map((match) => {\n      const dehydratedMatch = dehydratedState.dehydratedMatches.find(\n        (d) => d.id === match.id,\n      )\n\n      invariant(\n        dehydratedMatch,\n        `Could not find a client-side match for dehydrated match with id: ${match.id}!`,\n      )\n\n      if (dehydratedMatch) {\n        const route = this.looseRoutesById[match.routeId]!\n\n        return {\n          ...match,\n          ...dehydratedMatch,\n          meta: route.options.meta?.({\n            params: match.params,\n            loaderData: dehydratedMatch.loaderData,\n          }),\n          links: route.options.links?.(),\n          scripts: route.options.scripts?.(),\n        }\n      }\n      return match\n    })\n\n    this.__store.setState((s) => {\n      return {\n        ...s,\n        matches: matches as any,\n        lastUpdated: Date.now(),\n      }\n    })\n  }\n\n  handleNotFound = (matches: AnyRouteMatch[], err: NotFoundError) => {\n    const matchesByRouteId = Object.fromEntries(\n      matches.map((match) => [match.routeId, match]),\n    ) as Record<string, AnyRouteMatch>\n\n    // Start at the route that errored or default to the root route\n    let routeCursor =\n      (err.global\n        ? this.looseRoutesById[rootRouteId]\n        : this.looseRoutesById[err.routeId]) ||\n      this.looseRoutesById[rootRouteId]!\n\n    // Go up the tree until we find a route with a notFoundComponent or we hit the root\n    while (\n      !routeCursor.options.notFoundComponent &&\n      !this.options.defaultNotFoundComponent &&\n      routeCursor.id !== rootRouteId\n    ) {\n      routeCursor = routeCursor?.parentRoute\n\n      invariant(\n        routeCursor,\n        'Found invalid route tree while trying to find not-found handler.',\n      )\n    }\n\n    let match = matchesByRouteId[routeCursor.id]\n\n    invariant(match, 'Could not find match for route: ' + routeCursor.id)\n\n    // Assign the error to the match\n    Object.assign(match, {\n      status: 'notFound',\n      error: err,\n      isFetching: false,\n    } as AnyRouteMatch)\n  }\n\n  hasNotFoundMatch = () => {\n    return this.__store.state.matches.some(\n      (d) => d.status === 'notFound' || d.globalNotFound,\n    )\n  }\n\n  // resolveMatchPromise = (matchId: string, key: string, value: any) => {\n  //   state.matches\n  //     .find((d) => d.id === matchId)\n  //     ?.__promisesByKey[key]?.resolve(value)\n  // }\n}\n\n// A function that takes an import() argument which is a function and returns a new function that will\n// proxy arguments from the caller to the imported function, retaining all type\n// information along the way\nexport function lazyFn<\n  T extends Record<string, (...args: any[]) => any>,\n  TKey extends keyof T = 'default',\n>(fn: () => Promise<T>, key?: TKey) {\n  return async (\n    ...args: Parameters<T[TKey]>\n  ): Promise<Awaited<ReturnType<T[TKey]>>> => {\n    const imported = await fn()\n    return imported[key || 'default'](...args)\n  }\n}\n\nexport class SearchParamError extends Error {}\n\nexport class PathParamError extends Error {}\n\nexport function getInitialRouterState(\n  location: ParsedLocation,\n): RouterState<any> {\n  return {\n    isLoading: false,\n    isTransitioning: false,\n    status: 'idle',\n    resolvedLocation: { ...location },\n    location,\n    matches: [],\n    pendingMatches: [],\n    cachedMatches: [],\n    lastUpdated: 0,\n    statusCode: 200,\n  }\n}\n\nexport function defaultSerializeError(err: unknown) {\n  if (err instanceof Error)\n    return {\n      name: err.name,\n      message: err.message,\n    }\n\n  return {\n    data: err,\n  }\n}\n", "import { defaultSerializeError } from './router'\n\nexport type DeferredPromiseState<T> = {\n  uid: string\n  resolve?: () => void\n  promise?: Promise<void>\n  __resolvePromise?: () => void\n} & (\n  | {\n      status: 'pending'\n      data?: T\n      error?: unknown\n    }\n  | {\n      status: 'success'\n      data: T\n    }\n  | {\n      status: 'error'\n      data?: T\n      error: unknown\n    }\n)\n\nexport type DeferredPromise<T> = Promise<T> & {\n  __deferredState: DeferredPromiseState<T>\n}\n\nexport function defer<T>(\n  _promise: Promise<T>,\n  options?: {\n    serializeError?: typeof defaultSerializeError\n  },\n) {\n  const promise = _promise as DeferredPromise<T>\n\n  if (!promise.__deferredState) {\n    promise.__deferredState = {\n      uid: Math.random().toString(36).slice(2),\n      status: 'pending',\n    }\n\n    const state = promise.__deferredState\n\n    promise\n      .then((data) => {\n        state.status = 'success' as any\n        state.data = data\n      })\n      .catch((error) => {\n        state.status = 'error' as any\n        state.error = {\n          data: (options?.serializeError ?? defaultSerializeError)(error),\n          __isServerError: true,\n        }\n      })\n  }\n\n  return promise\n}\n\nexport function isDehydratedDeferred(obj: any): boolean {\n  return (\n    typeof obj === 'object' &&\n    obj !== null &&\n    !(obj instanceof Promise) &&\n    !obj.then &&\n    '__deferredState' in obj\n  )\n}\n", "import * as React from 'react'\nimport { useRouter } from './useRouter'\nimport { defaultSerializeError } from './router'\nimport { DeferredPromise, isDehydratedDeferred } from './defer'\nimport { defaultDeserializeError, isServerSideError } from './Matches'\n\nimport warning from 'tiny-warning'\n\nexport type AwaitOptions<T> = {\n  promise: DeferredPromise<T>\n}\n\nexport function useAwaited<T>({ promise }: AwaitOptions<T>): [T] {\n  const router = useRouter()\n  // const rerender = React.useReducer((x) => x + 1, 0)[1]\n\n  const state = promise.__deferredState\n\n  // Dehydrated promises only\n  // Successful or errored deferred promises mean they\n  // were resolved on the server and no further action is needed\n  if (isDehydratedDeferred(promise) && state.status === 'pending') {\n    const streamedData = (window as any)[`__TSR__DEFERRED__${state.uid}`]\n\n    if (streamedData) {\n      Object.assign(state, streamedData)\n    } else {\n      let token = router.registeredDeferredsIds.get(state.uid)\n\n      // If we haven't yet, create a promise and resolver that our streamed HTML can use\n      // when the client-side data is streamed in and ready.\n      if (!token) {\n        token = {}\n        router.registeredDeferredsIds.set(state.uid, token)\n        router.registeredDeferreds.set(token, state)\n\n        Object.assign(state, {\n          resolve: () => {\n            state.__resolvePromise?.()\n            // rerender()\n          },\n          promise: new Promise((r) => {\n            state.__resolvePromise = r as any\n          }),\n          __resolvePromise: () => {},\n        })\n      }\n    }\n  }\n\n  // If the promise is pending, always throw the state.promise\n  // For originating promises, this will be the original promise\n  // For dehydrated promises, this will be the placeholder promise\n  // that will be resolved when the server sends the real data\n  if (state.status === 'pending') {\n    throw isDehydratedDeferred(promise) ? state.promise : promise\n  }\n\n  // If we are the originator of the promise,\n  // inject the state into the HTML stream\n  if (!isDehydratedDeferred(promise)) {\n    router.injectHtml(`<script class='tsr_deferred_data'>window.__TSR__DEFERRED__${state.uid} = ${router.options.transformer.stringify(state)}</script>\n<script class='tsr_deferred_handler'>\n  if (window.__TSR__ROUTER__) {\n    let deferred = window.__TSR__ROUTER__.getDeferred('${state.uid}')\n    if (deferred) deferred.resolve(window.__TSR__DEFERRED__${state.uid})\n  }\n  document.querySelectorAll('.tsr_deferred_handler').forEach((el) => el.parentElement.removeChild(el))\n</script>`)\n  }\n\n  if (state.status === 'error') {\n    if (typeof document !== 'undefined') {\n      if (isServerSideError(state.error)) {\n        throw (\n          router.options.errorSerializer?.deserialize ?? defaultDeserializeError\n        )(state.error.data as any)\n      } else {\n        warning(\n          false,\n          \"Encountered a server-side error that doesn't fit the expected shape\",\n        )\n        throw state.error\n      }\n    } else {\n      throw {\n        data: (\n          router.options.errorSerializer?.serialize ?? defaultSerializeError\n        )(state.error),\n        __isServerError: true,\n      }\n    }\n  }\n\n  return [promise.__deferredState.data as any]\n}\n\nexport function Await<T>(\n  props: AwaitOptions<T> & {\n    fallback?: React.ReactNode\n    children: (result: T) => React.ReactNode\n  },\n) {\n  const inner = <AwaitInner {...props} />\n  if (props.fallback) {\n    return <React.Suspense fallback={props.fallback}>{inner}</React.Suspense>\n  }\n  return inner\n}\n\nfunction AwaitInner<T>(\n  props: AwaitOptions<T> & {\n    fallback?: React.ReactNode\n    children: (result: T) => React.ReactNode\n  },\n) {\n  const awaited = useAwaited(props)\n  return props.children(...awaited)\n}\n", "import { NoInfer } from '@tanstack/react-store'\nimport { ParsePathParams } from './link'\nimport {\n  AnyRoute,\n  ResolveFullPath,\n  ResolveFullSearchSchema,\n  MergeFromFromParent,\n  RouteContext,\n  AnyContext,\n  RouteOptions,\n  UpdatableRouteOptions,\n  Route,\n  createRoute,\n  RootRouteId,\n  TrimPathLeft,\n  RouteConstraints,\n  ResolveFullSearchSchemaInput,\n  SearchSchemaInput,\n  RouteLoaderFn,\n  AnyPathParams,\n  AnySearchSchema,\n} from './route'\nimport { Assign, Expand, IsAny } from './utils'\nimport { useMatch, useLoaderDeps, useLoaderData, RouteMatch } from './Matches'\nimport { useSearch } from './useSearch'\nimport { useParams } from './useParams'\nimport warning from 'tiny-warning'\nimport { RegisteredRouter } from './router'\nimport { RouteById, RouteIds } from './routeInfo'\n\nexport interface FileRoutesByPath {\n  // '/': {\n  //   parentRoute: typeof rootRoute\n  // }\n}\n\ntype Replace<\n  S extends string,\n  From extends string,\n  To extends string,\n> = S extends `${infer Start}${From}${infer Rest}`\n  ? `${Start}${To}${Replace<Rest, From, To>}`\n  : S\n\nexport type TrimLeft<\n  T extends string,\n  S extends string,\n> = T extends `${S}${infer U}` ? U : T\n\nexport type TrimRight<\n  T extends string,\n  S extends string,\n> = T extends `${infer U}${S}` ? U : T\n\nexport type Trim<T extends string, S extends string> = TrimLeft<\n  TrimRight<T, S>,\n  S\n>\n\nexport type RemoveUnderScores<T extends string> = Replace<\n  Replace<TrimRight<TrimLeft<T, '/_'>, '_'>, '_/', '/'>,\n  '/_',\n  '/'\n>\n\ntype RemoveRouteGroups<S extends string> =\n  S extends `${infer Before}(${infer RouteGroup})${infer After}`\n    ? RemoveRouteGroups<`${Before}${After}`>\n    : S\n\ntype NormalizeSlashes<S extends string> =\n  S extends `${infer Before}//${infer After}`\n    ? NormalizeSlashes<`${Before}/${After}`>\n    : S\n\ntype ReplaceFirstOccurrence<\n  T extends string,\n  Search extends string,\n  Replacement extends string,\n> = T extends `${infer Prefix}${Search}${infer Suffix}`\n  ? `${Prefix}${Replacement}${Suffix}`\n  : T\n\nexport type ResolveFilePath<\n  TParentRoute extends AnyRoute,\n  TFilePath extends string,\n> = TParentRoute['id'] extends RootRouteId\n  ? TrimPathLeft<TFilePath>\n  : ReplaceFirstOccurrence<\n      TrimPathLeft<TFilePath>,\n      TrimPathLeft<TParentRoute['types']['customId']>,\n      ''\n    >\n\nexport type FileRoutePath<\n  TParentRoute extends AnyRoute,\n  TFilePath extends string,\n> =\n  ResolveFilePath<TParentRoute, TFilePath> extends `_${infer _}`\n    ? ''\n    : ResolveFilePath<TParentRoute, TFilePath> extends `/_${infer _}`\n      ? ''\n      : ResolveFilePath<TParentRoute, TFilePath>\n\nexport function createFileRoute<\n  TFilePath extends keyof FileRoutesByPath,\n  TParentRoute extends AnyRoute = FileRoutesByPath[TFilePath]['parentRoute'],\n  TId extends RouteConstraints['TId'] = NormalizeSlashes<\n    RemoveRouteGroups<TFilePath>\n  >,\n  TPath extends RouteConstraints['TPath'] = FileRoutePath<\n    TParentRoute,\n    TFilePath\n  >,\n  TFullPath extends RouteConstraints['TFullPath'] = ResolveFullPath<\n    TParentRoute,\n    NormalizeSlashes<RemoveRouteGroups<RemoveUnderScores<TPath>>>\n  >,\n>(path: TFilePath) {\n  return new FileRoute<TFilePath, TParentRoute, TId, TPath, TFullPath>(path, {\n    silent: true,\n  }).createRoute\n}\n\n/** \n  @deprecated It's no longer recommended to use the `FileRoute` class directly.\n  Instead, use `createFileRoute('/path/to/file')(options)` to create a file route.\n*/\nexport class FileRoute<\n  TFilePath extends keyof FileRoutesByPath,\n  TParentRoute extends AnyRoute = FileRoutesByPath[TFilePath]['parentRoute'],\n  TId extends RouteConstraints['TId'] = TFilePath,\n  TPath extends RouteConstraints['TPath'] = FileRoutePath<\n    TParentRoute,\n    TFilePath\n  >,\n  TFullPath extends RouteConstraints['TFullPath'] = ResolveFullPath<\n    TParentRoute,\n    RemoveUnderScores<TPath>\n  >,\n> {\n  silent?: boolean\n\n  constructor(\n    public path: TFilePath,\n    _opts?: { silent: boolean },\n  ) {\n    this.silent = _opts?.silent\n  }\n\n  createRoute = <\n    TSearchSchemaInput extends RouteConstraints['TSearchSchema'] = {},\n    TSearchSchema extends RouteConstraints['TSearchSchema'] = {},\n    TSearchSchemaUsed extends Record<\n      string,\n      any\n    > = TSearchSchemaInput extends SearchSchemaInput\n      ? Omit<TSearchSchemaInput, keyof SearchSchemaInput>\n      : TSearchSchema,\n    TFullSearchSchemaInput extends\n      RouteConstraints['TFullSearchSchema'] = ResolveFullSearchSchemaInput<\n      TParentRoute,\n      TSearchSchemaUsed\n    >,\n    TFullSearchSchema extends\n      RouteConstraints['TFullSearchSchema'] = ResolveFullSearchSchema<\n      TParentRoute,\n      TSearchSchema\n    >,\n    TParams extends RouteConstraints['TParams'] = Expand<\n      Record<ParsePathParams<TPath>, string>\n    >,\n    TAllParams extends RouteConstraints['TAllParams'] = MergeFromFromParent<\n      TParentRoute['types']['allParams'],\n      TParams\n    >,\n    TRouteContextReturn extends\n      RouteConstraints['TRouteContext'] = RouteContext,\n    TRouteContext extends RouteConstraints['TRouteContext'] = [\n      TRouteContextReturn,\n    ] extends [never]\n      ? RouteContext\n      : TRouteContextReturn,\n    TAllContext extends Expand<\n      Assign<IsAny<TParentRoute['types']['allContext'], {}>, TRouteContext>\n    > = Expand<\n      Assign<IsAny<TParentRoute['types']['allContext'], {}>, TRouteContext>\n    >,\n    TRouterContext extends RouteConstraints['TRouterContext'] = AnyContext,\n    TLoaderDeps extends Record<string, any> = {},\n    TLoaderDataReturn extends any = unknown,\n    TLoaderData extends any = [TLoaderDataReturn] extends [never]\n      ? undefined\n      : TLoaderDataReturn,\n    TChildren extends RouteConstraints['TChildren'] = unknown,\n    TRouteTree extends RouteConstraints['TRouteTree'] = AnyRoute,\n  >(\n    options?: Omit<\n      RouteOptions<\n        TParentRoute,\n        string,\n        TPath,\n        TSearchSchemaInput,\n        TSearchSchema,\n        TSearchSchemaUsed,\n        TFullSearchSchemaInput,\n        TFullSearchSchema,\n        TParams,\n        TAllParams,\n        TRouteContextReturn,\n        TRouteContext,\n        TRouterContext,\n        TAllContext,\n        TLoaderDeps,\n        TLoaderDataReturn,\n        TLoaderData\n      >,\n      'getParentRoute' | 'path' | 'id'\n    > &\n      UpdatableRouteOptions<TAllParams, TFullSearchSchema, TLoaderData>,\n  ): Route<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TFilePath,\n    TId,\n    TSearchSchemaInput,\n    TSearchSchema,\n    TSearchSchemaUsed,\n    TFullSearchSchemaInput,\n    TFullSearchSchema,\n    TParams,\n    TAllParams,\n    TRouteContextReturn,\n    TRouteContext,\n    TAllContext,\n    TRouterContext,\n    TLoaderDeps,\n    TLoaderDataReturn,\n    TLoaderData,\n    TChildren,\n    TRouteTree\n  > => {\n    warning(\n      this.silent,\n      'FileRoute is deprecated and will be removed in the next major version. Use the createFileRoute(path)(options) function instead.',\n    )\n    const route = createRoute(options as any)\n    ;(route as any).isRoot = false\n    return route as any\n  }\n}\n\n/** \n  @deprecated It's recommended not to split loaders into separate files.\n  Instead, place the loader function in the the main route file, inside the\n  `createFileRoute('/path/to/file)(options)` options.\n*/\nexport function FileRouteLoader<\n  TFilePath extends keyof FileRoutesByPath,\n  TRoute extends FileRoutesByPath[TFilePath]['preLoaderRoute'],\n>(\n  _path: TFilePath,\n): <TLoaderData extends any>(\n  loaderFn: RouteLoaderFn<\n    TRoute['types']['allParams'],\n    TRoute['types']['loaderDeps'],\n    TRoute['types']['allContext'],\n    TRoute['types']['routeContext'],\n    TLoaderData\n  >,\n) => RouteLoaderFn<\n  TRoute['types']['allParams'],\n  TRoute['types']['loaderDeps'],\n  TRoute['types']['allContext'],\n  TRoute['types']['routeContext'],\n  NoInfer<TLoaderData>\n> {\n  warning(\n    false,\n    `FileRouteLoader is deprecated and will be removed in the next major version. Please place the loader function in the the main route file, inside the \\`createFileRoute('/path/to/file')(options)\\` options`,\n  )\n  return (loaderFn) => loaderFn\n}\n\nexport type LazyRouteOptions = Pick<\n  UpdatableRouteOptions<AnyPathParams, AnySearchSchema, any>,\n  'component' | 'errorComponent' | 'pendingComponent' | 'notFoundComponent'\n>\n\nexport class LazyRoute<TRoute extends AnyRoute> {\n  options: {\n    id: string\n  } & LazyRouteOptions\n\n  constructor(\n    opts: {\n      id: string\n    } & LazyRouteOptions,\n  ) {\n    this.options = opts\n    ;(this as any).$$typeof = Symbol.for('react.memo')\n  }\n\n  useMatch = <\n    TRouteMatchState = RouteMatch<\n      TRoute['types']['routeTree'],\n      TRoute['types']['id']\n    >,\n    TSelected = TRouteMatchState,\n  >(opts?: {\n    select?: (match: TRouteMatchState) => TSelected\n  }): TSelected => {\n    return useMatch({ select: opts?.select, from: this.options.id })\n  }\n\n  useRouteContext = <TSelected = TRoute['types']['allContext']>(opts?: {\n    select?: (s: TRoute['types']['allContext']) => TSelected\n  }): TSelected => {\n    return useMatch({\n      from: this.options.id,\n      select: (d: any) => (opts?.select ? opts.select(d.context) : d.context),\n    })\n  }\n\n  useSearch = <TSelected = TRoute['types']['fullSearchSchema']>(opts?: {\n    select?: (s: TRoute['types']['fullSearchSchema']) => TSelected\n  }): TSelected => {\n    return useSearch({ ...opts, from: this.options.id })\n  }\n\n  useParams = <TSelected = TRoute['types']['allParams']>(opts?: {\n    select?: (s: TRoute['types']['allParams']) => TSelected\n  }): TSelected => {\n    return useParams({ ...opts, from: this.options.id })\n  }\n\n  useLoaderDeps = <TSelected = TRoute['types']['loaderDeps']>(opts?: {\n    select?: (s: TRoute['types']['loaderDeps']) => TSelected\n  }): TSelected => {\n    return useLoaderDeps({ ...opts, from: this.options.id } as any)\n  }\n\n  useLoaderData = <TSelected = TRoute['types']['loaderData']>(opts?: {\n    select?: (s: TRoute['types']['loaderData']) => TSelected\n  }): TSelected => {\n    return useLoaderData({ ...opts, from: this.options.id } as any)\n  }\n}\n\nexport function createLazyRoute<\n  TId extends RouteIds<RegisteredRouter['routeTree']>,\n  TRoute extends AnyRoute = RouteById<RegisteredRouter['routeTree'], TId>,\n>(id: TId) {\n  return (opts: LazyRouteOptions) => {\n    return new LazyRoute<TRoute>({ id: id as any, ...opts })\n  }\n}\n\nexport function createLazyFileRoute<\n  TFilePath extends keyof FileRoutesByPath,\n  TRoute extends FileRoutesByPath[TFilePath]['preLoaderRoute'],\n>(path: TFilePath) {\n  const id = removeGroups(path)\n  return (opts: LazyRouteOptions) => new LazyRoute<TRoute>({ id, ...opts })\n}\n\nconst routeGroupPatternRegex = /\\(.+\\)/g\n\nfunction removeGroups(s: string) {\n  return s.replaceAll(routeGroupPatternRegex, '').replaceAll('//', '/')\n}\n", "import * as React from 'react'\nimport { AsyncRouteComponent } from './route'\n\n// If the load fails due to module not found, it may mean a new version of\n// the build was deployed and the user's browser is still using an old version.\n// If this happens, the old version in the user's browser would have an outdated\n// URL to the lazy module.\n// In that case, we want to attempt one window refresh to get the latest.\nfunction isModuleNotFoundError(error: any): boolean {\n  return (\n    typeof error?.message === 'string' &&\n    /Failed to fetch dynamically imported module/.test(error.message)\n  )\n}\n\nexport function lazyRouteComponent<\n  T extends Record<string, any>,\n  <PERSON><PERSON><PERSON> extends keyof T = 'default',\n>(\n  importer: () => Promise<T>,\n  exportName?: TKey,\n): T[TKey] extends (props: infer TProps) => any\n  ? AsyncRouteComponent<TProps>\n  : never {\n  let loadPromise: Promise<any> & {\n    moduleNotFoundError?: Error\n  }\n\n  const load = () => {\n    if (!loadPromise) {\n      loadPromise = importer().catch((error) => {\n        if (isModuleNotFoundError(error)) {\n          // We don't want an error thrown from preload in this case, because\n          // there's nothing we want to do about module not found during preload.\n          // Record the error, recover the promise with a null return,\n          // and we will attempt module not found resolution during the render path.\n\n          loadPromise.moduleNotFoundError = error\n\n          return null\n        }\n        throw error\n      })\n    }\n\n    return loadPromise\n  }\n\n  const lazyComp = React.lazy(async () => {\n    try {\n      const promise = load()\n\n      // Now that we're out of preload and into actual render path,\n      // throw the error if it was a module not found error during preload\n      if (promise.moduleNotFoundError) {\n        throw promise.moduleNotFoundError\n      }\n      const moduleExports = await promise\n\n      const comp = moduleExports[exportName ?? 'default']\n      return {\n        default: comp,\n      }\n    } catch (error) {\n      if (\n        error instanceof Error &&\n        isModuleNotFoundError(error) &&\n        typeof window !== 'undefined' &&\n        typeof sessionStorage !== 'undefined'\n      ) {\n        // Again, we want to reload one time on module not found error and not enter\n        // a reload loop if there is some other issue besides an old deploy.\n        // That's why we store our reload attempt in sessionStorage.\n        // Use error.message as key because it contains the module path that failed.\n        const storageKey = `tanstack_router_reload:${error.message}`\n        if (!sessionStorage.getItem(storageKey)) {\n          sessionStorage.setItem(storageKey, '1')\n          window.location.reload()\n\n          // Return empty component while we wait for window to reload\n          return {\n            default: () => null,\n          }\n        }\n      }\n      throw error\n    }\n  })\n\n  ;(lazyComp as any).preload = load\n\n  return lazyComp as any\n}\n", "import * as React from 'react'\nimport { useMatch } from './Matches'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\nimport { Trim } from './fileRoute'\nimport { AnyRoute, ReactNode, RootSearchSchema } from './route'\nimport { RouteByPath, RoutePaths, RoutePathsAutoComplete } from './routeInfo'\nimport { RegisteredRouter } from './router'\nimport { LinkProps, UseLinkPropsOptions } from './useNavigate'\nimport {\n  Expand,\n  IsUnion,\n  MakeDifferenceOptional,\n  NoInfer,\n  NonNullableUpdater,\n  PickRequired,\n  Updater,\n  WithoutEmpty,\n  deepEqual,\n  functionalUpdate,\n} from './utils'\nimport { HistoryState } from '@tanstack/history'\n\nexport type CleanPath<T extends string> = T extends `${infer L}//${infer R}`\n  ? CleanPath<`${CleanPath<L>}/${CleanPath<R>}`>\n  : T extends `${infer L}//`\n    ? `${CleanPath<L>}/`\n    : T extends `//${infer L}`\n      ? `/${CleanPath<L>}`\n      : T\n\nexport type Split<S, TIncludeTrailingSlash = true> = S extends unknown\n  ? string extends S\n    ? string[]\n    : S extends string\n      ? CleanPath<S> extends ''\n        ? []\n        : TIncludeTrailingSlash extends true\n          ? CleanPath<S> extends `${infer T}/`\n            ? [...Split<T>, '/']\n            : CleanPath<S> extends `/${infer U}`\n              ? Split<U>\n              : CleanPath<S> extends `${infer T}/${infer U}`\n                ? [...Split<T>, ...Split<U>]\n                : [S]\n          : CleanPath<S> extends `${infer T}/${infer U}`\n            ? [...Split<T>, ...Split<U>]\n            : S extends string\n              ? [S]\n              : never\n      : never\n  : never\n\nexport type ParsePathParams<T extends string> = keyof {\n  [K in Trim<Split<T>[number], '_'> as K extends `$${infer L}`\n    ? L extends ''\n      ? '_splat'\n      : L\n    : never]: K\n}\n\nexport type Join<T, Delimiter extends string = '/'> = T extends []\n  ? ''\n  : T extends [infer L extends string]\n    ? L\n    : T extends [infer L extends string, ...infer Tail extends [...string[]]]\n      ? CleanPath<`${L}${Delimiter}${Join<Tail>}`>\n      : never\n\nexport type Last<T extends any[]> = T extends [...infer _, infer L] ? L : never\n\nexport type RemoveTrailingSlashes<T> = T extends `${infer R}/`\n  ? RemoveTrailingSlashes<R>\n  : T\n\nexport type RemoveLeadingSlashes<T> = T extends `/${infer R}`\n  ? RemoveLeadingSlashes<R>\n  : T\n\nexport type SearchPaths<\n  TPaths,\n  TSearchPath extends string,\n> = TPaths extends `${TSearchPath}/${infer TRest}` ? TRest : never\n\nexport type SearchRelativePathAutoComplete<\n  TTo extends string,\n  TSearchPath extends string,\n  TPaths,\n  SearchedPaths = SearchPaths<TPaths, TSearchPath>,\n> = SearchedPaths extends string ? `${TTo}/${SearchedPaths}` : never\n\nexport type RelativeToParentPathAutoComplete<\n  TFrom extends string,\n  TTo extends string,\n  TPaths,\n  TResolvedPath extends string = RemoveTrailingSlashes<\n    ResolveRelativePath<TFrom, TTo>\n  >,\n> =\n  | SearchRelativePathAutoComplete<TTo, TResolvedPath, TPaths>\n  | (TResolvedPath extends '' ? never : `${TTo}/../`)\n\nexport type RelativeToCurrentPathAutoComplete<\n  TFrom extends string,\n  TTo extends string,\n  TRestTo extends string,\n  TPaths,\n  TResolvedPath extends\n    string = RemoveTrailingSlashes<`${RemoveTrailingSlashes<TFrom>}/${RemoveLeadingSlashes<TRestTo>}`>,\n> = SearchRelativePathAutoComplete<TTo, TResolvedPath, TPaths>\n\nexport type AbsolutePathAutoComplete<TFrom extends string, TPaths> =\n  | (string extends TFrom\n      ? './'\n      : TFrom extends `/`\n        ? never\n        : SearchPaths<\n              TPaths,\n              RemoveTrailingSlashes<TFrom>\n            > extends infer SearchedPaths\n          ? SearchedPaths extends ''\n            ? never\n            : './'\n          : never)\n  | (string extends TFrom ? '../' : TFrom extends `/` ? never : '../')\n  | TPaths\n\nexport type RelativeToPathAutoComplete<\n  TRouteTree extends AnyRoute,\n  TFrom extends string,\n  TTo extends string,\n  TPaths = RoutePaths<TRouteTree>,\n> = TTo extends `..${string}`\n  ? RelativeToParentPathAutoComplete<TFrom, RemoveTrailingSlashes<TTo>, TPaths>\n  : TTo extends `./${infer TRestTTo}`\n    ? RelativeToCurrentPathAutoComplete<\n        TFrom,\n        RemoveTrailingSlashes<TTo>,\n        TRestTTo,\n        TPaths\n      >\n    : AbsolutePathAutoComplete<TFrom, TPaths>\n\nexport type NavigateOptions<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n> = ToOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo> & {\n  // `replace` is a boolean that determines whether the navigation should replace the current history entry or push a new one.\n  replace?: boolean\n  resetScroll?: boolean\n  // If set to `true`, the link's underlying navigate() call will be wrapped in a `React.startTransition` call. Defaults to `true`.\n  startTransition?: boolean\n}\n\nexport type ToOptions<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n> = ToSubOptions<TRouteTree, TFrom, TTo> & {\n  mask?: ToMaskOptions<TRouteTree, TMaskFrom, TMaskTo>\n}\n\nexport type ToMaskOptions<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TMaskFrom extends RoutePaths<TRouteTree> | string = string,\n  TMaskTo extends string = '',\n> = ToSubOptions<TRouteTree, TMaskFrom, TMaskTo> & {\n  unmaskOnReload?: boolean\n}\n\nexport type ToSubOptions<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TTo extends string = '',\n> = {\n  to?: ToPathOption<TRouteTree, TFrom, TTo>\n  hash?: true | Updater<string>\n  state?: true | NonNullableUpdater<HistoryState>\n  // The source route path. This is automatically set when using route-level APIs, but for type-safe relative routing on the router itself, this is required\n  from?: RoutePathsAutoComplete<TRouteTree, TFrom>\n  // // When using relative route paths, this option forces resolution from the current path, instead of the route API's path or `from` path\n} & CheckPath<TRouteTree, {}, TFrom, TTo> &\n  SearchParamOptions<TRouteTree, TFrom, TTo> &\n  PathParamOptions<TRouteTree, TFrom, TTo>\n\ntype ParamsReducer<TFrom, TTo> = TTo | ((current: TFrom) => TTo)\n\ntype ParamVariant = 'PATH' | 'SEARCH'\ntype ExcludeRootSearchSchema<T, Excluded = Exclude<T, RootSearchSchema>> = [\n  Excluded,\n] extends [never]\n  ? {}\n  : Excluded\n\nexport type ResolveRoute<\n  TRouteTree extends AnyRoute,\n  TFrom,\n  TTo,\n  TPath = RemoveTrailingSlashes<\n    string extends TTo ? TFrom : ResolveRelativePath<TFrom, TTo>\n  >,\n> =\n  RouteByPath<TRouteTree, `${TPath & string}/`> extends never\n    ? RouteByPath<TRouteTree, TPath>\n    : RouteByPath<TRouteTree, `${TPath & string}/`>\n\ntype PostProcessParams<\n  T,\n  TParamVariant extends ParamVariant,\n> = TParamVariant extends 'SEARCH' ? ExcludeRootSearchSchema<T> : T\n\nexport type ParamOptions<\n  TRouteTree extends AnyRoute,\n  TFrom,\n  TTo extends string,\n  TParamVariant extends ParamVariant,\n  TFromRouteType extends\n    | 'allParams'\n    | 'fullSearchSchema' = TParamVariant extends 'PATH'\n    ? 'allParams'\n    : 'fullSearchSchema',\n  TToRouteType extends\n    | 'allParams'\n    | 'fullSearchSchemaInput' = TParamVariant extends 'PATH'\n    ? 'allParams'\n    : 'fullSearchSchemaInput',\n  TFromParams = PostProcessParams<\n    RouteByPath<TRouteTree, TFrom>['types'][TFromRouteType],\n    TParamVariant\n  >,\n  TToParams = PostProcessParams<\n    ResolveRoute<TRouteTree, TFrom, TTo>['types'][TToRouteType],\n    TParamVariant\n  >,\n  TRelativeToParams = TParamVariant extends 'SEARCH'\n    ? TToParams\n    : true extends IsUnion<TFromParams>\n      ? TToParams\n      : MakeDifferenceOptional<TFromParams, TToParams>,\n  TReducer = ParamsReducer<TFromParams, TRelativeToParams>,\n> =\n  Expand<WithoutEmpty<PickRequired<TRelativeToParams>>> extends never\n    ? Partial<MakeParamOption<TParamVariant, true | TReducer>>\n    : TFromParams extends Expand<WithoutEmpty<PickRequired<TRelativeToParams>>>\n      ? MakeParamOption<TParamVariant, true | TReducer>\n      : MakeParamOption<TParamVariant, TReducer>\n\ntype MakeParamOption<\n  TParamVariant extends ParamVariant,\n  T,\n> = TParamVariant extends 'PATH'\n  ? MakePathParamOptions<T>\n  : MakeSearchParamOptions<T>\ntype MakeSearchParamOptions<T> = { search: T }\ntype MakePathParamOptions<T> = { params: T }\n\nexport type SearchParamOptions<\n  TRouteTree extends AnyRoute,\n  TFrom,\n  TTo extends string,\n> = ParamOptions<TRouteTree, TFrom, TTo, 'SEARCH'>\n\nexport type PathParamOptions<\n  TRouteTree extends AnyRoute,\n  TFrom,\n  TTo extends string,\n> = ParamOptions<TRouteTree, TFrom, TTo, 'PATH'>\n\nexport type ToPathOption<\n  TRouteTree extends AnyRoute = AnyRoute,\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TTo extends string = '',\n> =\n  | TTo\n  | RelativeToPathAutoComplete<\n      TRouteTree,\n      NoInfer<TFrom> extends string ? NoInfer<TFrom> : '',\n      NoInfer<TTo> & string\n    >\n\nexport interface ActiveOptions {\n  exact?: boolean\n  includeHash?: boolean\n  includeSearch?: boolean\n}\n\nexport type LinkOptions<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n> = NavigateOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo> & {\n  // The standard anchor tag target attribute\n  target?: HTMLAnchorElement['target']\n  // Defaults to `{ exact: false, includeHash: false }`\n  activeOptions?: ActiveOptions\n  // If set, will preload the linked route on hover and cache it for this many milliseconds in hopes that the user will eventually navigate there.\n  preload?: false | 'intent'\n  // Delay intent preloading by this many milliseconds. If the intent exits before this delay, the preload will be cancelled.\n  preloadDelay?: number\n  // If true, will render the link without the href attribute\n  disabled?: boolean\n}\n\nexport type CheckPath<TRouteTree extends AnyRoute, TPass, TFrom, TTo> =\n  ResolveRoute<TRouteTree, TFrom, TTo> extends never\n    ? string extends TFrom\n      ? RemoveTrailingSlashes<TTo> extends '.' | '..'\n        ? TPass\n        : CheckPathError<TRouteTree>\n      : CheckPathError<TRouteTree>\n    : TPass\n\nexport type CheckPathError<TRouteTree extends AnyRoute> = {\n  to: RoutePaths<TRouteTree>\n}\n\nexport type ResolveRelativePath<TFrom, TTo = '.'> = TFrom extends string\n  ? TTo extends string\n    ? TTo extends '.'\n      ? TFrom\n      : TTo extends `./`\n        ? Join<[TFrom, '/']>\n        : TTo extends `./${infer TRest}`\n          ? ResolveRelativePath<TFrom, TRest>\n          : TTo extends `/${infer TRest}`\n            ? TTo\n            : Split<TTo> extends ['..', ...infer ToRest]\n              ? Split<TFrom> extends [...infer FromRest, infer FromTail]\n                ? ToRest extends ['/']\n                  ? Join<['/', ...FromRest, '/']>\n                  : ResolveRelativePath<Join<FromRest>, Join<ToRest>>\n                : never\n              : Split<TTo> extends ['.', ...infer ToRest]\n                ? ToRest extends ['/']\n                  ? Join<[TFrom, '/']>\n                  : ResolveRelativePath<TFrom, Join<ToRest>>\n                : CleanPath<Join<['/', ...Split<TFrom>, ...Split<TTo>]>>\n    : never\n  : never\n\ntype LinkCurrentTargetElement = {\n  preloadTimeout?: null | ReturnType<typeof setTimeout>\n}\n\nconst preloadWarning = 'Error preloading route! ☝️'\n\nexport function useLinkProps<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n>(\n  options: UseLinkPropsOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo>,\n): React.AnchorHTMLAttributes<HTMLAnchorElement> {\n  const router = useRouter()\n  const matchPathname = useMatch({\n    strict: false,\n    select: (s) => s.pathname,\n  })\n\n  const {\n    // custom props\n    activeProps = () => ({ className: 'active' }),\n    inactiveProps = () => ({}),\n    activeOptions,\n    hash,\n    search,\n    params,\n    to,\n    state,\n    mask,\n    preload: userPreload,\n    preloadDelay: userPreloadDelay,\n    replace,\n    startTransition,\n    resetScroll,\n    // element props\n    children,\n    target,\n    disabled,\n    style,\n    className,\n    onClick,\n    onFocus,\n    onMouseEnter,\n    onMouseLeave,\n    onTouchStart,\n    ...rest\n  } = options\n\n  // If this link simply reloads the current route,\n  // make sure it has a new key so it will trigger a data refresh\n\n  // If this `to` is a valid external URL, return\n  // null for LinkUtils\n\n  const dest = {\n    from: options.to ? matchPathname : undefined,\n    ...options,\n  }\n\n  let type: 'internal' | 'external' = 'internal'\n\n  try {\n    new URL(`${to}`)\n    type = 'external'\n  } catch {}\n\n  const next = router.buildLocation(dest as any)\n  const preload = userPreload ?? router.options.defaultPreload\n  const preloadDelay =\n    userPreloadDelay ?? router.options.defaultPreloadDelay ?? 0\n\n  const isActive = useRouterState({\n    select: (s) => {\n      // Compare path/hash for matches\n      const currentPathSplit = s.location.pathname.split('/')\n      const nextPathSplit = next.pathname.split('/')\n      const pathIsFuzzyEqual = nextPathSplit.every(\n        (d, i) => d === currentPathSplit[i],\n      )\n      // Combine the matches based on user router.options\n      const pathTest = activeOptions?.exact\n        ? s.location.pathname === next.pathname\n        : pathIsFuzzyEqual\n      const hashTest = activeOptions?.includeHash\n        ? s.location.hash === next.hash\n        : true\n      const searchTest =\n        activeOptions?.includeSearch ?? true\n          ? deepEqual(s.location.search, next.search, !activeOptions?.exact)\n          : true\n\n      // The final \"active\" test\n      return pathTest && hashTest && searchTest\n    },\n  })\n\n  if (type === 'external') {\n    return {\n      ...rest,\n      type,\n      href: to,\n      children,\n      target,\n      disabled,\n      style,\n      className,\n      onClick,\n      onFocus,\n      onMouseEnter,\n      onMouseLeave,\n      onTouchStart,\n    }\n  }\n\n  // The click handler\n  const handleClick = (e: MouseEvent) => {\n    if (\n      !disabled &&\n      !isCtrlEvent(e) &&\n      !e.defaultPrevented &&\n      (!target || target === '_self') &&\n      e.button === 0\n    ) {\n      e.preventDefault()\n\n      // All is well? Navigate!\n      router.commitLocation({ ...next, replace, resetScroll, startTransition })\n    }\n  }\n\n  const doPreload = () => {\n    React.startTransition(() => {\n      router.preloadRoute(dest as any).catch((err) => {\n        console.warn(err)\n        console.warn(preloadWarning)\n      })\n    })\n  }\n\n  // The click handler\n  const handleFocus = (e: MouseEvent) => {\n    if (disabled) return\n    if (preload) {\n      doPreload()\n    }\n  }\n\n  const handleTouchStart = handleFocus\n\n  const handleEnter = (e: MouseEvent) => {\n    if (disabled) return\n    const target = (e.target || {}) as LinkCurrentTargetElement\n\n    if (preload) {\n      if (target.preloadTimeout) {\n        return\n      }\n\n      target.preloadTimeout = setTimeout(() => {\n        target.preloadTimeout = null\n        doPreload()\n      }, preloadDelay)\n    }\n  }\n\n  const handleLeave = (e: MouseEvent) => {\n    if (disabled) return\n    const target = (e.target || {}) as LinkCurrentTargetElement\n\n    if (target.preloadTimeout) {\n      clearTimeout(target.preloadTimeout)\n      target.preloadTimeout = null\n    }\n  }\n\n  const composeHandlers =\n    (handlers: (undefined | ((e: any) => void))[]) =>\n    (e: React.SyntheticEvent) => {\n      if (e.persist) e.persist()\n      handlers.filter(Boolean).forEach((handler) => {\n        if (e.defaultPrevented) return\n        handler!(e)\n      })\n    }\n\n  // Get the active props\n  const resolvedActiveProps: React.HTMLAttributes<HTMLAnchorElement> = isActive\n    ? functionalUpdate(activeProps as any, {}) ?? {}\n    : {}\n\n  // Get the inactive props\n  const resolvedInactiveProps: React.HTMLAttributes<HTMLAnchorElement> =\n    isActive ? {} : functionalUpdate(inactiveProps, {}) ?? {}\n\n  return {\n    ...resolvedActiveProps,\n    ...resolvedInactiveProps,\n    ...rest,\n    href: disabled\n      ? undefined\n      : next.maskedLocation\n        ? next.maskedLocation.href\n        : next.href,\n    onClick: composeHandlers([onClick, handleClick]),\n    onFocus: composeHandlers([onFocus, handleFocus]),\n    onMouseEnter: composeHandlers([onMouseEnter, handleEnter]),\n    onMouseLeave: composeHandlers([onMouseLeave, handleLeave]),\n    onTouchStart: composeHandlers([onTouchStart, handleTouchStart]),\n    target,\n    style: {\n      ...style,\n      ...resolvedActiveProps.style,\n      ...resolvedInactiveProps.style,\n    },\n    className:\n      [\n        className,\n        resolvedActiveProps.className,\n        resolvedInactiveProps.className,\n      ]\n        .filter(Boolean)\n        .join(' ') || undefined,\n    ...(disabled\n      ? {\n          role: 'link',\n          'aria-disabled': true,\n        }\n      : undefined),\n    ['data-status']: isActive ? 'active' : undefined,\n  }\n}\n\nexport interface LinkComponent<TProps extends Record<string, any> = {}> {\n  <\n    TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n    TFrom extends RoutePaths<TRouteTree> | string = string,\n    TTo extends string = '',\n    TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n    TMaskTo extends string = '',\n  >(\n    props: LinkProps<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo> &\n      TProps &\n      React.RefAttributes<HTMLAnchorElement>,\n  ): ReactNode\n}\n\nexport const Link: LinkComponent = React.forwardRef((props: any, ref) => {\n  const { type, ...linkProps } = useLinkProps(props)\n\n  const children =\n    typeof props.children === 'function'\n      ? props.children({\n          isActive: (linkProps as any)['data-status'] === 'active',\n        })\n      : props.children\n\n  return <a {...linkProps} ref={ref} children={children} />\n})\n\nfunction isCtrlEvent(e: MouseEvent) {\n  return !!(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey)\n}\n", "import * as React from 'react'\n\nconst useLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect\n\nimport { ParsedLocation } from './location'\nimport { useRouter } from './useRouter'\nimport { NonNullableUpdater, functionalUpdate } from './utils'\n\nconst windowKey = 'window'\nconst delimiter = '___'\n\nlet weakScrolledElements = new WeakSet<any>()\n\ntype CacheValue = Record<string, { scrollX: number; scrollY: number }>\ntype CacheState = {\n  cached: CacheValue\n  next: CacheValue\n}\n\ntype Cache = {\n  state: CacheState\n  set: (updater: NonNullableUpdater<CacheState>) => void\n}\n\nconst sessionsStorage = typeof window !== 'undefined' && window.sessionStorage\n\nlet cache: Cache = sessionsStorage\n  ? (() => {\n      const storageKey = 'tsr-scroll-restoration-v2'\n\n      const state: CacheState = JSON.parse(\n        window.sessionStorage.getItem(storageKey) || 'null',\n      ) || { cached: {}, next: {} }\n\n      return {\n        state,\n        set: (updater) => {\n          cache.state = functionalUpdate(updater, cache.state)\n          window.sessionStorage.setItem(storageKey, JSON.stringify(cache.state))\n        },\n      }\n    })()\n  : (undefined as any)\n\nexport type ScrollRestorationOptions = {\n  getKey?: (location: ParsedLocation) => string\n}\n\nconst defaultGetKey = (location: ParsedLocation) => location.state.key!\n\nexport function useScrollRestoration(options?: ScrollRestorationOptions) {\n  const router = useRouter()\n\n  useLayoutEffect(() => {\n    const getKey = options?.getKey || defaultGetKey\n\n    const { history } = window\n    if (history.scrollRestoration) {\n      history.scrollRestoration = 'manual'\n    }\n\n    const onScroll = (event: Event) => {\n      if (weakScrolledElements.has(event.target)) return\n      weakScrolledElements.add(event.target)\n\n      let elementSelector = ''\n\n      if (event.target === document || event.target === window) {\n        elementSelector = windowKey\n      } else {\n        const attrId = (event.target as Element).getAttribute(\n          'data-scroll-restoration-id',\n        )\n\n        if (attrId) {\n          elementSelector = `[data-scroll-restoration-id=\"${attrId}\"]`\n        } else {\n          elementSelector = getCssSelector(event.target)\n        }\n      }\n\n      if (!cache.state.next[elementSelector]) {\n        cache.set((c) => ({\n          ...c,\n          next: {\n            ...c.next,\n            [elementSelector]: {\n              scrollX: NaN,\n              scrollY: NaN,\n            },\n          },\n        }))\n      }\n    }\n\n    if (typeof document !== 'undefined') {\n      document.addEventListener('scroll', onScroll, true)\n    }\n\n    const unsubOnBeforeLoad = router.subscribe('onBeforeLoad', (event) => {\n      if (event.pathChanged) {\n        const restoreKey = getKey(event.fromLocation)\n        for (const elementSelector in cache.state.next) {\n          const entry = cache.state.next[elementSelector]!\n          if (elementSelector === windowKey) {\n            entry.scrollX = window.scrollX || 0\n            entry.scrollY = window.scrollY || 0\n          } else if (elementSelector) {\n            const element = document.querySelector(elementSelector)\n            entry.scrollX = element?.scrollLeft || 0\n            entry.scrollY = element?.scrollTop || 0\n          }\n\n          cache.set((c) => {\n            const next = { ...c.next }\n            delete next[elementSelector]\n\n            return {\n              ...c,\n              next,\n              cached: {\n                ...c.cached,\n                [[restoreKey, elementSelector].join(delimiter)]: entry,\n              },\n            }\n          })\n        }\n      }\n    })\n\n    const unsubOnResolved = router.subscribe('onResolved', (event) => {\n      if (event.pathChanged) {\n        if (!router.resetNextScroll) {\n          return\n        }\n\n        router.resetNextScroll = true\n\n        const getKey = options?.getKey || defaultGetKey\n\n        const restoreKey = getKey(event.toLocation)\n        let windowRestored = false\n\n        for (const cacheKey in cache.state.cached) {\n          const entry = cache.state.cached[cacheKey]!\n          const [key, elementSelector] = cacheKey.split(delimiter)\n          if (key === restoreKey) {\n            if (elementSelector === windowKey) {\n              windowRestored = true\n              window.scrollTo(entry.scrollX, entry.scrollY)\n            } else if (elementSelector) {\n              const element = document.querySelector(elementSelector)\n              if (element) {\n                element.scrollLeft = entry.scrollX\n                element.scrollTop = entry.scrollY\n              }\n            }\n          }\n        }\n\n        if (!windowRestored) {\n          window.scrollTo(0, 0)\n        }\n\n        cache.set((c) => ({ ...c, next: {} }))\n        weakScrolledElements = new WeakSet<any>()\n      }\n    })\n\n    return () => {\n      document.removeEventListener('scroll', onScroll)\n      unsubOnBeforeLoad()\n      unsubOnResolved()\n    }\n  }, [])\n}\n\nexport function ScrollRestoration(props: ScrollRestorationOptions) {\n  useScrollRestoration(props)\n  return null\n}\n\nexport function useElementScrollRestoration(\n  options: (\n    | {\n        id: string\n        getElement?: () => Element | undefined | null\n      }\n    | {\n        id?: string\n        getElement: () => Element | undefined | null\n      }\n  ) & {\n    getKey?: (location: ParsedLocation) => string\n  },\n) {\n  const router = useRouter()\n  const getKey = options?.getKey || defaultGetKey\n\n  let elementSelector = ''\n\n  if (options.id) {\n    elementSelector = `[data-scroll-restoration-id=\"${options.id}\"]`\n  } else {\n    const element = options.getElement?.()\n    if (!element) {\n      return\n    }\n    elementSelector = getCssSelector(element)\n  }\n\n  const restoreKey = getKey(router.latestLocation)\n  const cacheKey = [restoreKey, elementSelector].join(delimiter)\n  return cache.state.cached[cacheKey]\n}\n\nfunction getCssSelector(el: any): string {\n  let path = [],\n    parent\n  while ((parent = el.parentNode)) {\n    path.unshift(\n      `${el.tagName}:nth-child(${\n        ([].indexOf as any).call(parent.children, el) + 1\n      })`,\n    )\n    el = parent\n  }\n  return `${path.join(' > ')}`.toLowerCase()\n}\n", "import * as React from 'react'\nimport { ReactNode } from './route'\nimport { useRouter } from './useRouter'\nimport { BlockerFn } from '@tanstack/history'\n\nexport function useBlocker(\n  blockerFn: BlockerFn,\n  condition: boolean | any = true,\n): void {\n  const { history } = useRouter()\n\n  React.useEffect(() => {\n    if (!condition) return\n    return history.block(blockerFn)\n  })\n}\n\nexport function Block({ blocker, condition, children }: PromptProps) {\n  useBlocker(blocker, condition)\n  return (children ?? null) as ReactNode\n}\n\nexport type PromptProps = {\n  blocker: BlockerFn\n  condition?: boolean | any\n  children?: ReactNode\n}\n", "import * as React from 'react'\nimport { useMatch } from './Matches'\nimport { useRouter } from './useRouter'\nimport { LinkOptions, NavigateOptions } from './link'\nimport { AnyRoute } from './route'\nimport { RoutePaths, RoutePathsAutoComplete } from './routeInfo'\nimport { RegisteredRouter } from './router'\n\nexport type UseNavigateResult<TDefaultFrom extends string> = <\n  TTo extends string,\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> | string = TDefaultFrom,\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n>({\n  from,\n  ...rest\n}: NavigateOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo>) => Promise<void>\n\nexport function useNavigate<\n  TDefaultFrom extends string = string,\n>(_defaultOpts?: {\n  from?: RoutePathsAutoComplete<RegisteredRouter['routeTree'], TDefaultFrom>\n}) {\n  const { navigate } = useRouter()\n\n  const matchPathname = useMatch({\n    strict: false,\n    select: (s) => s.pathname,\n  })\n\n  const result: UseNavigateResult<TDefaultFrom> = ({ from, ...rest }) => {\n    return navigate({\n      from: rest?.to ? matchPathname : undefined,\n      ...(rest as any),\n    })\n  }\n\n  return React.useCallback(result, [])\n}\n\n// NOTE: I don't know of anyone using this. It's undocumented, so let's wait until someone needs it\n// export function typedNavigate<\n//   TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n//   TDefaultFrom extends RoutePaths<TRouteTree> = '/',\n// >(navigate: (opts: NavigateOptions<any>) => Promise<void>) {\n//   return navigate as <\n//     TFrom extends RoutePaths<TRouteTree> = TDefaultFrom,\n//     TTo extends string = '',\n//     TMaskFrom extends RoutePaths<TRouteTree> = '/',\n//     TMaskTo extends string = '',\n//   >(\n//     opts?: NavigateOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo>,\n//   ) => Promise<void>\n// } //\n\nexport function Navigate<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n>(props: NavigateOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo>): null {\n  const { navigate } = useRouter()\n  const match = useMatch({ strict: false })\n\n  React.useEffect(() => {\n    navigate({\n      from: props.to ? match.pathname : undefined,\n      ...props,\n    } as any)\n  }, [])\n\n  return null\n}\n\nexport type UseLinkPropsOptions<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n> = ActiveLinkOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo> &\n  React.AnchorHTMLAttributes<HTMLAnchorElement>\n\nexport type LinkProps<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n> = ActiveLinkOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo> &\n  Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, 'children'> & {\n    // If a function is passed as a child, it will be given the `isActive` boolean to aid in further styling on the element it returns\n    children?:\n      | React.ReactNode\n      | ((state: { isActive: boolean }) => React.ReactNode)\n  }\n\nexport type ActiveLinkOptions<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TTo extends string = '',\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n> = LinkOptions<TRouteTree, TFrom, TTo, TMaskFrom, TMaskTo> & {\n  // A function that returns additional props for the `active` state of this link. These props override other props passed to the link (`style`'s are merged, `className`'s are concatenated)\n  activeProps?:\n    | React.AnchorHTMLAttributes<HTMLAnchorElement>\n    | (() => React.AnchorHTMLAttributes<HTMLAnchorElement>)\n  // A function that returns additional props for the `inactive` state of this link. These props override other props passed to the link (`style`'s are merged, `className`'s are concatenated)\n  inactiveProps?:\n    | React.AnchorHTMLAttributes<HTMLAnchorElement>\n    | (() => React.AnchorHTMLAttributes<HTMLAnchorElement>)\n}\n", "import { useMatch, RouteMatch } from './Matches'\nimport { AnyRoute } from './route'\nimport { RouteIds, RouteById } from './routeInfo'\nimport { RegisteredRouter } from './router'\nimport { StrictOrFrom } from './utils'\n\nexport function useRouteContext<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TFrom extends RouteIds<TRouteTree> = RouteIds<TRouteTree>,\n  TRouteContext = RouteById<TRouteTree, TFrom>['types']['allContext'],\n  TSelected = TRouteContext,\n>(\n  opts: StrictOrFrom<TFrom> & {\n    select?: (search: TRouteContext) => TSelected\n  },\n): TSelected {\n  return useMatch({\n    ...(opts as any),\n    select: (match: RouteMatch) =>\n      opts?.select\n        ? opts.select(match.context as TRouteContext)\n        : match.context,\n  })\n}\n", "import invariant from 'tiny-invariant'\n\nexport const serverFnReturnTypeHeader = 'server-fn-return-type'\nexport const serverFnPayloadTypeHeader = 'server-fn-payload-type'\n\nexport interface JsonResponse<TData> extends Response {\n  json(): Promise<TData>\n}\n\nexport type FetcherOptionsBase = {\n  method?: 'GET' | 'POST'\n}\n\nexport type FetcherOptions = FetcherOptionsBase & {\n  requestInit?: RequestInit\n}\n\nexport type FetchFnCtx = {\n  method: 'GET' | 'POST'\n  request: Request\n}\n\nexport type FetchFn<TPayload, TResponse> = {\n  (payload: TPayload, ctx: FetchFnCtx): Promise<TResponse> | TResponse\n  url?: string\n}\n\nexport type CompiledFetcherFnOptions<TPayload> = {\n  method: 'GET' | 'POST'\n  payload: TPayload | undefined\n  requestInit?: RequestInit\n}\n\nexport type CompiledFetcherFn<TPayload, TResponse> = {\n  (opts: CompiledFetcherFnOptions<TPayload>): Promise<TResponse>\n  url: string\n}\n\ntype IsPayloadOptional<T> = [T] extends [undefined] ? true : false\n\nexport type Fetcher<TPayload, TResponse> =\n  (IsPayloadOptional<TPayload> extends true\n    ? {\n        (\n          payload?: TPayload,\n          opts?: FetcherOptions,\n        ): Promise<JsonResponseOrPayload<TResponse>>\n      }\n    : {\n        (\n          payload: TPayload,\n          opts?: FetcherOptions,\n        ): Promise<JsonResponseOrPayload<TResponse>>\n      }) & {\n    url: string\n  }\n\nexport type JsonResponseOrPayload<TResponse> =\n  TResponse extends JsonResponse<infer TData> ? TData : TResponse\n\nexport function createServerFn<\n  TPayload extends any = undefined,\n  TResponse = unknown,\n>(\n  method: 'GET' | 'POST',\n  fn: FetchFn<TPayload, TResponse>,\n): Fetcher<TPayload, TResponse> {\n  // Cast the compiled function that will be injected by vinxi\n  const compiledFn = fn as unknown as CompiledFetcherFn<TPayload, TResponse>\n\n  invariant(\n    compiledFn.url,\n    `createServerFn must be called with a function that is marked with the 'use server' pragma.`,\n  )\n\n  return Object.assign(\n    async (payload: TPayload, opts?: FetcherOptions) => {\n      return compiledFn({\n        method,\n        payload: payload || undefined,\n        requestInit: opts?.requestInit,\n      })\n    },\n    {\n      url: fn.url!,\n    },\n  ) as Fetcher<TPayload, TResponse>\n}\n\nexport function json<TData>(\n  payload: TData,\n  opts?: {\n    status?: number\n    statusText?: string\n    headers?: HeadersInit\n  },\n): JsonResponse<TData> {\n  return new Response(JSON.stringify(payload), {\n    status: opts?.status || 200,\n    statusText: opts?.statusText || opts?.status === 200 ? 'OK' : 'Error',\n    headers: {\n      'Content-Type': 'application/json',\n      [serverFnReturnTypeHeader]: 'json',\n      ...opts?.headers,\n    },\n  })\n}\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAYA,QAAI,MAAuC;AACzC,OAAC,WAAW;AAEJ;AAGV,YACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,gCACpC,YACF;AACA,yCAA+B,4BAA4B,IAAI,MAAM,CAAC;AAAA,QACxE;AACU,YAAIA,UAAQ;AAEtB,YAAI,uBAAuBA,QAAM;AAEjC,iBAAS,MAAM,QAAQ;AACrB;AACE;AACE,uBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,qBAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,cACnC;AAEA,2BAAa,SAAS,QAAQ,IAAI;AAAA,YACpC;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,aAAa,OAAO,QAAQ,MAAM;AAGzC;AACE,gBAAI,yBAAyB,qBAAqB;AAClD,gBAAI,QAAQ,uBAAuB,iBAAiB;AAEpD,gBAAI,UAAU,IAAI;AAChB,wBAAU;AACV,qBAAO,KAAK,OAAO,CAAC,KAAK,CAAC;AAAA,YAC5B;AAGA,gBAAI,iBAAiB,KAAK,IAAI,SAAU,MAAM;AAC5C,qBAAO,OAAO,IAAI;AAAA,YACpB,CAAC;AAED,2BAAe,QAAQ,cAAc,MAAM;AAI3C,qBAAS,UAAU,MAAM,KAAK,QAAQ,KAAK,GAAG,SAAS,cAAc;AAAA,UACvE;AAAA,QACF;AAMA,iBAAS,GAAG,GAAG,GAAG;AAChB,iBAAO,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM;AAAA,QAErE;AAEA,YAAI,WAAW,OAAO,OAAO,OAAO,aAAa,OAAO,KAAK;AAI7D,YAAIC,YAAWD,QAAM,UACjBE,aAAYF,QAAM,WAClBG,mBAAkBH,QAAM,iBACxB,gBAAgBA,QAAM;AAC1B,YAAI,oBAAoB;AACxB,YAAI,6BAA6B;AAWjC,iBAAS,qBAAqB,WAAW,aAIzC,mBAAmB;AACjB;AACE,gBAAI,CAAC,mBAAmB;AACtB,kBAAIA,QAAM,oBAAoB,QAAW;AACvC,oCAAoB;AAEpB,sBAAM,gMAA+M;AAAA,cACvN;AAAA,YACF;AAAA,UACF;AAMA,cAAI,QAAQ,YAAY;AAExB;AACE,gBAAI,CAAC,4BAA4B;AAC/B,kBAAI,cAAc,YAAY;AAE9B,kBAAI,CAAC,SAAS,OAAO,WAAW,GAAG;AACjC,sBAAM,sEAAsE;AAE5E,6CAA6B;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AAgBA,cAAI,YAAYC,UAAS;AAAA,YACvB,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF;AAAA,UACF,CAAC,GACG,OAAO,UAAU,CAAC,EAAE,MACpB,cAAc,UAAU,CAAC;AAK7B,UAAAE,iBAAgB,WAAY;AAC1B,iBAAK,QAAQ;AACb,iBAAK,cAAc;AAKnB,gBAAI,uBAAuB,IAAI,GAAG;AAEhC,0BAAY;AAAA,gBACV;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,GAAG,CAAC,WAAW,OAAO,WAAW,CAAC;AAClC,UAAAD,WAAU,WAAY;AAGpB,gBAAI,uBAAuB,IAAI,GAAG;AAEhC,0BAAY;AAAA,gBACV;AAAA,cACF,CAAC;AAAA,YACH;AAEA,gBAAI,oBAAoB,WAAY;AAOlC,kBAAI,uBAAuB,IAAI,GAAG;AAEhC,4BAAY;AAAA,kBACV;AAAA,gBACF,CAAC;AAAA,cACH;AAAA,YACF;AAGA,mBAAO,UAAU,iBAAiB;AAAA,UACpC,GAAG,CAAC,SAAS,CAAC;AACd,wBAAc,KAAK;AACnB,iBAAO;AAAA,QACT;AAEA,iBAAS,uBAAuB,MAAM;AACpC,cAAI,oBAAoB,KAAK;AAC7B,cAAI,YAAY,KAAK;AAErB,cAAI;AACF,gBAAI,YAAY,kBAAkB;AAClC,mBAAO,CAAC,SAAS,WAAW,SAAS;AAAA,UACvC,SAASE,QAAO;AACd,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,uBAAuB,WAAW,aAAa,mBAAmB;AAKzE,iBAAO,YAAY;AAAA,QACrB;AAEA,YAAI,YAAY,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,eAAe,OAAO,OAAO,SAAS,kBAAkB;AAEvI,YAAI,sBAAsB,CAAC;AAE3B,YAAI,OAAO,sBAAsB,yBAAyB;AAC1D,YAAI,yBAAyBJ,QAAM,yBAAyB,SAAYA,QAAM,uBAAuB;AAErG,gBAAQ,uBAAuB;AAE/B,YACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,+BACpC,YACF;AACA,yCAA+B,2BAA2B,IAAI,MAAM,CAAC;AAAA,QACvE;AAAA,MAEE,GAAG;AAAA,IACL;AAAA;AAAA;;;AC9OA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAYA,QAAI,MAAuC;AACzC,OAAC,WAAW;AAEJ;AAGV,YACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,gCACpC,YACF;AACA,yCAA+B,4BAA4B,IAAI,MAAM,CAAC;AAAA,QACxE;AACU,YAAIK,UAAQ;AACtB,YAAI,OAAO;AAMX,iBAAS,GAAG,GAAG,GAAG;AAChB,iBAAO,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM;AAAA,QAErE;AAEA,YAAI,WAAW,OAAO,OAAO,OAAO,aAAa,OAAO,KAAK;AAE7D,YAAI,uBAAuB,KAAK;AAIhC,YAAIC,UAASD,QAAM,QACfE,aAAYF,QAAM,WAClB,UAAUA,QAAM,SAChB,gBAAgBA,QAAM;AAE1B,iBAASG,kCAAiC,WAAW,aAAa,mBAAmB,UAAU,SAAS;AAEtG,cAAI,UAAUF,QAAO,IAAI;AACzB,cAAI;AAEJ,cAAI,QAAQ,YAAY,MAAM;AAC5B,mBAAO;AAAA,cACL,UAAU;AAAA,cACV,OAAO;AAAA,YACT;AACA,oBAAQ,UAAU;AAAA,UACpB,OAAO;AACL,mBAAO,QAAQ;AAAA,UACjB;AAEA,cAAI,WAAW,QAAQ,WAAY;AAKjC,gBAAI,UAAU;AACd,gBAAI;AACJ,gBAAI;AAEJ,gBAAI,mBAAmB,SAAU,cAAc;AAC7C,kBAAI,CAAC,SAAS;AAEZ,0BAAU;AACV,mCAAmB;AAEnB,oBAAI,iBAAiB,SAAS,YAAY;AAE1C,oBAAI,YAAY,QAAW;AAIzB,sBAAI,KAAK,UAAU;AACjB,wBAAI,mBAAmB,KAAK;AAE5B,wBAAI,QAAQ,kBAAkB,cAAc,GAAG;AAC7C,0CAAoB;AACpB,6BAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF;AAEA,oCAAoB;AACpB,uBAAO;AAAA,cACT;AAIA,kBAAI,eAAe;AACnB,kBAAI,gBAAgB;AAEpB,kBAAI,SAAS,cAAc,YAAY,GAAG;AAExC,uBAAO;AAAA,cACT;AAIA,kBAAI,gBAAgB,SAAS,YAAY;AASzC,kBAAI,YAAY,UAAa,QAAQ,eAAe,aAAa,GAAG;AAClE,uBAAO;AAAA,cACT;AAEA,iCAAmB;AACnB,kCAAoB;AACpB,qBAAO;AAAA,YACT;AAIA,gBAAI,yBAAyB,sBAAsB,SAAY,OAAO;AAEtE,gBAAI,0BAA0B,WAAY;AACxC,qBAAO,iBAAiB,YAAY,CAAC;AAAA,YACvC;AAEA,gBAAI,gCAAgC,2BAA2B,OAAO,SAAY,WAAY;AAC5F,qBAAO,iBAAiB,uBAAuB,CAAC;AAAA,YAClD;AACA,mBAAO,CAAC,yBAAyB,6BAA6B;AAAA,UAChE,GAAG,CAAC,aAAa,mBAAmB,UAAU,OAAO,CAAC,GAClD,eAAe,SAAS,CAAC,GACzB,qBAAqB,SAAS,CAAC;AAEnC,cAAI,QAAQ,qBAAqB,WAAW,cAAc,kBAAkB;AAC5E,UAAAC,WAAU,WAAY;AACpB,iBAAK,WAAW;AAChB,iBAAK,QAAQ;AAAA,UACf,GAAG,CAAC,KAAK,CAAC;AACV,wBAAc,KAAK;AACnB,iBAAO;AAAA,QACT;AAEA,gBAAQ,mCAAmCC;AAE3C,YACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,+BACpC,YACF;AACA,yCAA+B,2BAA2B,IAAI,MAAM,CAAC;AAAA,QACvE;AAAA,MAEE,GAAG;AAAA,IACL;AAAA;AAAA;;;ACpKA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACkCA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,oBAAoB;AAE1B,IAAM,uBAAuB,CAAC,UAAiB;AAC7C,QAAM,eAAe;AAErB,SAAQ,MAAM,cAAc;AAC9B;AAEA,IAAM,eAAe,MAAM;AACzB,sBAAoB,mBAAmB,sBAAsB;IAC3D,SAAS;EAAA,CACV;AACH;AAEO,SAAS,cAAc,MAWZ;AACZ,MAAA,WAAW,KAAK,YAAA;AAChB,MAAA,cAAA,oBAAkB,IAAA;AACtB,MAAI,WAAwB,CAAA;AAE5B,QAAM,WAAW,MAAM;AACrB,eAAW,KAAK,YAAA;AAChB,gBAAY,QAAQ,CAAC,eAAe,WAAY,CAAA;EAAA;AAG5C,QAAA,gBAAgB,OAAO,SAAqB;AArCpD,QAAA;AAsCI,QAAI,OAAO,aAAa,eAAe,SAAS,QAAQ;AACtD,eAAS,WAAW,UAAU;AACtB,cAAA,UAAU,MAAM,QAAA;AACtB,YAAI,CAAC,SAAS;AACZ,WAAA,KAAA,KAAK,cAAL,OAAA,SAAA,GAAA,KAAA,MAAiB,QAAA;AACjB;QACF;MACF;IACF;AAEK,SAAA;EAAA;AAGA,SAAA;IACL,IAAI,WAAW;AACN,aAAA;IACT;IACA,WAAW,CAAC,OAAmB;AAC7B,kBAAY,IAAI,EAAE;AAElB,aAAO,MAAM;AACX,oBAAY,OAAO,EAAE;MAAA;IAEzB;IACA,MAAM,CAAC,MAAc,UAAe;AAClC,cAAQ,UAAU,KAAK;AACvB,oBAAc,MAAM;AACb,aAAA,UAAU,MAAM,KAAK;AACjB,iBAAA;MAAA,CACV;IACH;IACA,SAAS,CAAC,MAAc,UAAe;AACrC,cAAQ,UAAU,KAAK;AACvB,oBAAc,MAAM;AACb,aAAA,aAAa,MAAM,KAAK;AACpB,iBAAA;MAAA,CACV;IACH;IACA,IAAI,CAAC,UAAU;AACb,oBAAc,MAAM;AAClB,aAAK,GAAG,KAAK;MAAA,CACd;IACH;IACA,MAAM,MAAM;AACV,oBAAc,MAAM;AAClB,aAAK,KAAK;MAAA,CACX;IACH;IACA,SAAS,MAAM;AACb,oBAAc,MAAM;AAClB,aAAK,QAAQ;MAAA,CACd;IACH;IACA,YAAY,CAAC,QAAQ,KAAK,WAAW,GAAG;IACxC,OAAO,CAAC,YAAY;AAClB,eAAS,KAAK,OAAO;AAEjB,UAAA,SAAS,WAAW,GAAG;AACzB,yBAAiB,mBAAmB,sBAAsB;UACxD,SAAS;QAAA,CACV;MACH;AAEA,aAAO,MAAM;AACX,mBAAW,SAAS,OAAO,CAAC,MAAM,MAAM,OAAO;AAE3C,YAAA,CAAC,SAAS,QAAQ;AACP,uBAAA;QACf;MAAA;IAEJ;IACA,OAAO,MAAA;AA7GX,UAAA;AA6GiB,cAAA,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA;IAAA;IACb,SAAS,MAAA;AA9Gb,UAAA;AA8GmB,cAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAA,KAAA,IAAA;IAAA;IACf,QAAQ;EAAA;AAEZ;AAEA,SAAS,UAAU,OAAqB;AACtC,MAAI,CAAC,OAAO;AACV,YAAQ,CAAA;EACV;AACO,SAAA;IACL,GAAG;IACH,KAAK,gBAAgB;EAAA;AAEzB;AAkBO,SAAS,qBAAqB,MAInB;AAChB,QAAM,OACJ,QAAA,OAAA,SAAA,KAAM,YACL,OAAO,aAAa,cAAc,SAAU;AAE/C,QAAM,cAAa,QAAA,OAAA,SAAA,KAAM,gBAAe,CAAC,SAAS;AAC5C,QAAA,iBACJ,QAAA,OAAA,SAAA,KAAM,mBACL,MACC;IACE,GAAG,IAAI,SAAS,QAAQ,GAAG,IAAI,SAAS,MAAM,GAAG,IAAI,SAAS,IAAI;IAClE,IAAI,QAAQ;EAAA;AAGlB,MAAI,kBAAkB,cAAA;AAClB,MAAA;AAEJ,QAAM,cAAc,MAAM;AAEtB,MAAA;AAeJ,MAAI,WAAW;AAIX,MAAA;AAIE,QAAA,UAAU,CAAC,OAAmB;AACvB,eAAA;AACR,OAAA;AACQ,eAAA;EAAA;AAIb,QAAM,QAAQ,MAAM;AAElB,YAAQ,MAAM;AACZ,UAAI,CAAC;AAAM;AACX,UAAI,QAAQ,KAAK,SAAS,cAAc,cAAc;QACpD,KAAK;QACL;QACA,KAAK;MAAA;AAGA,aAAA;AACK,kBAAA;AACO,yBAAA;IAAA,CACpB;EAAA;AAIH,QAAM,qBAAqB,CACzB,MACA,UACA,UACG;AACG,UAAA,OAAO,WAAW,QAAQ;AAEhC,QAAI,CAAC,WAAW;AACK,yBAAA;IACrB;AAGkB,sBAAA,UAAU,UAAU,KAAK;AAGpC,WAAA;MACL;MACA;MACA,SAAQ,QAAA,OAAA,SAAA,KAAM,WAAU,SAAS;IAAA;AAGnC,QAAI,CAAC,WAAW;AAEd,kBAAY,QAAQ,QAAQ,EAAE,KAAK,MAAM,MAAA,CAAO;IAClD;EAAA;AAGF,QAAM,YAAY,MAAM;AACtB,sBAAkB,cAAc;AAChC,YAAQ,OAAO;EAAA;AAGb,MAAA,oBAAoB,IAAI,QAAQ;AAChC,MAAA,uBAAuB,IAAI,QAAQ;AAEvC,QAAM,UAAU,cAAc;IAC5B;IACA,WAAW,CAAC,MAAM,UAAU,mBAAmB,QAAQ,MAAM,KAAK;IAClE,cAAc,CAAC,MAAM,UAAU,mBAAmB,WAAW,MAAM,KAAK;IACxE,MAAM,MAAM,IAAI,QAAQ,KAAK;IAC7B,SAAS,MAAM,IAAI,QAAQ,QAAQ;IACnC,IAAI,CAAC,MAAM,IAAI,QAAQ,GAAG,CAAC;IAC3B,YAAY,CAAC,SAAS,WAAW,IAAI;IACrC;IACA,SAAS,MAAM;AACb,UAAI,QAAQ,YAAY;AACxB,UAAI,QAAQ,eAAe;AACvB,UAAA,oBAAoB,gBAAgB,SAAS;AAC7C,UAAA,oBAAoB,eAAe,SAAS;IAClD;IACA,WAAW,CAAC,aAAa;AAGnB,UAAA,oBAAoB,oBAAoB,kBAAkB;AAC1C,0BAAA;AAET,iBAAA;MACX;IACF;EAAA,CACD;AAEG,MAAA,iBAAiB,gBAAgB,SAAS;AAC1C,MAAA,iBAAiB,eAAe,SAAS;AAEzC,MAAA,QAAQ,YAAY,WAAY;AAClC,QAAI,MAAM,kBAAkB,MAAM,IAAI,SAAS,SAAgB;AAC3D,QAAA;AAAU,cAAQ,OAAO;AACtB,WAAA;EAAA;AAGL,MAAA,QAAQ,eAAe,WAAY;AACrC,QAAI,MAAM,qBAAqB,MAAM,IAAI,SAAS,SAAgB;AAC9D,QAAA;AAAU,cAAQ,OAAO;AACtB,WAAA;EAAA;AAGF,SAAA;AACT;AAEO,SAAS,kBAAkB,MAAwC;AACxE,QAAM,OACJ,QAAA,OAAA,SAAA,KAAM,YACL,OAAO,aAAa,cAAc,SAAU;AAC/C,SAAO,qBAAqB;IAC1B,QAAQ;IACR,eAAe,MAAM;AACnB,YAAM,WAAW,IAAI,SAAS,KAAK,MAAM,GAAG,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,KAAK;AACpE,aAAO,UAAU,UAAU,IAAI,QAAQ,KAAK;IAC9C;IACA,YAAY,CAAC,SACX,GAAG,IAAI,SAAS,QAAQ,GAAG,IAAI,SAAS,MAAM,IAAI,IAAI;EAAA,CACzD;AACH;AAEO,SAAS,oBACd,OAGI;EACF,gBAAgB,CAAC,GAAG;AACtB,GACe;AACf,QAAM,UAAU,KAAK;AACrB,MAAI,QAAQ,KAAK,gBAAgB,QAAQ,SAAS;AAClD,MAAI,eAAe;IACjB,KAAK,gBAAgB;EAAA;AAGvB,QAAM,cAAc,MAAM,UAAU,QAAQ,KAAK,GAAI,YAAY;AAEjE,SAAO,cAAc;IACnB;IAEA,WAAW,CAAC,MAAM,UAAU;AACX,qBAAA;AACf,cAAQ,KAAK,IAAI;AACjB;IACF;IACA,cAAc,CAAC,MAAM,UAAU;AACd,qBAAA;AACf,cAAQ,KAAK,IAAI;IACnB;IACA,MAAM,MAAM;AACV;IACF;IACA,SAAS,MAAM;AACb,cAAQ,KAAK,IAAI,QAAQ,GAAG,QAAQ,SAAS,CAAC;IAChD;IACA,IAAI,CAAC,MAAM;AACD,cAAA,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG,CAAC,GAAG,QAAQ,SAAS,CAAC;IAC7D;IACA,YAAY,CAAC,SAAS;EAAA,CACvB;AACH;AAEA,SAAS,UAAU,MAAc,OAAsC;AACjE,MAAA,YAAY,KAAK,QAAQ,GAAG;AAC5B,MAAA,cAAc,KAAK,QAAQ,GAAG;AAE3B,SAAA;IACL;IACA,UAAU,KAAK;MACb;MACA,YAAY,IACR,cAAc,IACZ,KAAK,IAAI,WAAW,WAAW,IAC/B,YACF,cAAc,IACZ,cACA,KAAK;IACb;IACA,MAAM,YAAY,KAAK,KAAK,UAAU,SAAS,IAAI;IACnD,QACE,cAAc,KACV,KAAK,MAAM,aAAa,cAAc,KAAK,SAAY,SAAS,IAChE;IACN,OAAO,SAAS,CAAC;EAAA;AAErB;AAGA,SAAS,kBAAkB;AACjB,UAAA,KAAK,OAAA,IAAW,GAAG,SAAS,EAAE,EAAE,UAAU,CAAC;AACrD;;;AC9ZA,IAAI,eAAe;AACnB,IAAI,SAAS;AACb,SAAS,UAAU,WAAW,SAAS;AACnC,MAAI,WAAW;AACX;AAAA,EACJ;AACA,MAAI,cAAc;AACd,UAAM,IAAI,MAAM,MAAM;AAAA,EAC1B;AACA,MAAI,WAAW,OAAO,YAAY,aAAa,QAAQ,IAAI;AAC3D,MAAI,QAAQ,WAAW,GAAG,OAAO,QAAQ,IAAI,EAAE,OAAO,QAAQ,IAAI;AAClE,QAAM,IAAI,MAAM,KAAK;AACzB;;;ACZA,IAAIC,gBAAe;AACnB,SAAS,QAAQ,WAAW,SAAS;AACnC,MAAI,CAACA,eAAc;AACjB,QAAI,WAAW;AACb;AAAA,IACF;AAEA,QAAI,OAAO,cAAc;AAEzB,QAAI,OAAO,YAAY,aAAa;AAClC,cAAQ,KAAK,IAAI;AAAA,IACnB;AAEA,QAAI;AACF,YAAM,MAAM,IAAI;AAAA,IAClB,SAAS,GAAG;AAAA,IAAC;AAAA,EACf;AACF;AAEA,IAAO,2BAAQ;;;;;;;;;;;AChBf,IAAI,gBAAsB,oBAA2B,IAAK;AAEnD,SAAS,mBAAmB;AAC7B,MAAA,OAAO,aAAa,aAAa;AAC5B,WAAA;EACT;AAEA,MAAI,OAAO,wBAAwB;AACjC,WAAO,OAAO;EAChB;AAEA,SAAO,yBAAyB;AAEzB,SAAA;AACT;;;ACXO,SAAS,UAEd,MAA+C;AAC/C,QAAM,QAAc,kBAAW,iBAAkB,CAAA;AACjD;IACE,IAAG,QAAA,OAAA,SAAA,KAAM,SAAQ,SAAS,CAAC;IAC3B;EAAA;AAEK,SAAA;AACT;;;ACdA,2BAAiD;;;ACkB1C,IAAM,QAAN,MAGL;EAQA,YAAY,cAAsB,SAA0C;AAP5E,SAAA,YAAY,oBAAI,IAAc;AAG9B,SAAA,YAAY;AACZ,SAAA,YAAY;AACZ,SAAA,gBAAiC;AAOjC,SAAA,YAAY,CAAC,aAAuB;;AAClC,WAAK,UAAU,IAAI,QAAQ;AAC3B,YAAM,SAAQ,gBAAK,YAAL,mBAAc,gBAAd,4BAA4B,UAAU;AACpD,aAAO,MAAM;AACX,aAAK,UAAU,OAAO,QAAQ;AAC9B;MACF;IACF;AAEA,SAAA,WAAW,CACT,SACA,SAGG;;AACH,YAAM,WAAW,KAAK;AACtB,WAAK,UAAQ,UAAK,YAAL,mBAAc,YACvB,KAAK,QAAQ,SAAS,QAAQ,EAAE,OAAO,IACtC,QAAgB,QAAQ;AAE7B,YAAM,YAAW,6BAAM,eAAY,UAAK,YAAL,mBAAc,oBAAmB;AACpE,UAAI,KAAK,kBAAkB,MAAM;AAC/B,aAAK,gBAAgB;MACvB,WAAW,KAAK,kBAAkB,QAAQ;AACxC,aAAK,gBAAgB;MACvB,OAAO;AACL,aAAK,kBAAgB,UAAK,YAAL,mBAAc,oBAAmB;MACxD;AAGA,uBAAK,YAAL,mBAAc,aAAd,4BAAyB;QACvB,UAAU,KAAK;MACjB;AAGA,WAAK,OAAO;IACd;AAEA,SAAA,SAAS,MAAM;AACb,UAAI,KAAK;AAAW;AACpB,YAAM,UAAU,EAAE,KAAK;AACvB,WAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,YAAI,KAAK,cAAc;AAAS;AAChC,iBAAS;UACP,UAAU,KAAK,iBAAiB;QAClC,CAAC;MACH,CAAC;IACH;AAEA,SAAA,QAAQ,CAAC,OAAmB;AAC1B,UAAI,KAAK;AAAW,eAAO,GAAG;AAC9B,WAAK,YAAY;AACjB,SAAG;AACH,WAAK,YAAY;AACjB,WAAK,OAAO;IACd;AA3DE,SAAK,QAAQ;AACb,SAAK,UAAU;EACjB;AA0DF;;;ADpFO,SAAS,SAKd,OACA,WAAkD,CAAC,MAAM,GACzD;AACA,QAAM,YAAQ;IACZ,MAAM;IACN,MAAM,MAAM;IACZ,MAAM,MAAM;IACZ;IACA;EACF;AAEA,SAAO;AACT;AAEO,SAAS,QAAW,MAAS,MAAS;AAC3C,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AACzB,WAAO;EACT;AAEA,MACE,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,SAAS,YAChB,SAAS,MACT;AACA,WAAO;EACT;AAEA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,WAAO;EACT;AAEA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QACE,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM,CAAC,CAAW,KAC9D,CAAC,OAAO,GAAG,KAAK,MAAM,CAAC,CAAY,GAAG,KAAK,MAAM,CAAC,CAAY,CAAC,GAC/D;AACA,aAAO;IACT;EACF;AACA,SAAO;AACT;;;;;;;;;AEpDO,SAAS,cAAc,OAK3B;AACK,QAAA,iBAAiB,MAAM,kBAAkB;AAG7C,aAAA;IAAC;IAAA;MACC,aAAa,MAAM;MACnB,SAAS,MAAM;MACf,UAAU,CAAC,EAAE,MAAA,MAAY;AACvB,YAAI,OAAO;AACF,iBAAM,qBAAc,gBAAgB;YACzC;UAAA,CACD;QACH;AAEA,eAAO,MAAM;MACf;IAAA;EAAA;AAGN;AAEa,IAAA,oBAAA,cAAgC,iBAI1C;EAJI,cAAA;AAAA,UAAA,GAAA,SAAA;AAKG,SAAA,QAAA,EAAE,OAAO,KAAK;EAAA;EACtB,OAAO,yBAAyB,OAAY;;AAC1C,WAAO,EAAE,WAAU,KAAA,MAAM,gBAAN,OAAA,SAAA,GAAA,KAAA,KAAA,EAAsB;EAC3C;EACA,OAAO,yBAAyB,OAAY;AAC1C,WAAO,EAAE,MAAM;EACjB;EACA,mBACE,WAKA,WACM;AACN,QAAI,UAAU,SAAS,UAAU,aAAa,KAAK,MAAM,UAAU;AACjE,WAAK,SAAS,EAAE,OAAO,KAAM,CAAA;IAC/B;EACF;EACA,kBAAkB,OAAY;;AACxB,QAAA,KAAK,MAAM,SAAS;AACjB,OAAA,MAAA,KAAA,KAAA,OAAM,YAAN,OAAA,SAAA,GAAA,KAAA,IAAgB,KAAA;IAAK,OACrB;AACL,cAAQ,MAAM,KAAK;IACrB;EACF;EACA,SAAS;AACP,WAAO,KAAK,MAAM,SAAS,KAAK,KAAK;EACvC;AACF;AAEgB,SAAA,eAAe,EAAE,MAAA,GAAyB;AAClD,QAAA,CAAC,MAAM,OAAO,IAAU,gBAAS,IAAqC;AAG1E,aAAA,yBAAC,OAAA,EAAI,OAAO,EAAE,SAAS,SAAS,UAAU,OACxC,GAAA,UAAA;QAAC,yBAAA,OAAA,EAAI,OAAO,EAAE,SAAS,QAAQ,YAAY,UAAU,KAAK,QAAA,GACxD,UAAA;UAAA,wBAAC,UAAA,EAAO,OAAO,EAAE,UAAU,OAAA,GAAU,UAAqB,wBAAA,CAAA;UAC1D;QAAC;QAAA;UACC,OAAO;YACL,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,cAAc;UAChB;UACA,SAAS,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC;UAE/B,UAAA,OAAO,eAAe;QAAA;MACzB;IAAA,EAAA,CACF;QAAA,wBACC,OAAI,EAAA,OAAO,EAAE,QAAQ,SAAA,EAAA,CAAY;IACjC,WAAA,wBACE,OACC,EAAA,cAAA;MAAC;MAAA;QACC,OAAO;UACL,UAAU;UACV,QAAQ;UACR,cAAc;UACd,SAAS;UACT,OAAO;UACP,UAAU;QACZ;QAEC,UAAA,MAAM,cAAU,wBAAC,QAAM,EAAA,UAAA,MAAM,QAAQ,CAAA,IAAU;MAAA;IAAA,EAAA,CAEpD,IACE;EACN,EAAA,CAAA;AAEJ;;;AClGO,SAAS,eAGd,MAGY;AACZ,QAAM,gBAAgB,UAAsB;IAC1C,OAAM,QAAA,OAAA,SAAA,KAAM,YAAW;EAAA,CACxB;AACD,SAAO,WAAU,QAAA,OAAA,SAAA,KAAM,WAAU,eAAe,SAAS,QAAA,OAAA,SAAA,KAAM,MAAa;AAC9E;;;;ACkHa,IAAA,WAAW,OAAO,aAAa;AAErC,SAAS,KAAQ,KAAU;AACzB,SAAA,IAAI,IAAI,SAAS,CAAC;AAC3B;AAEA,SAAS,WAAW,GAAuB;AACzC,SAAO,OAAO,MAAM;AACtB;AAEgB,SAAA,iBACd,SACA,UACS;AACL,MAAA,WAAW,OAAO,GAAG;AACvB,WAAO,QAAQ,QAAmB;EACpC;AAEO,SAAA;AACT;AAEgB,SAAA,KAA2B,QAAW,MAAuB;AAC3E,SAAO,KAAK,OAAO,CAAC,KAAU,QAAW;AACnC,QAAA,GAAG,IAAI,OAAO,GAAG;AACd,WAAA;EACT,GAAG,CAAS,CAAA;AACd;AAQgB,SAAA,iBAAoB,MAAW,OAAa;AAC1D,MAAI,SAAS,OAAO;AACX,WAAA;EACT;AAEA,QAAM,OAAO;AAEb,QAAM,QAAQ,aAAa,IAAI,KAAK,aAAa,IAAI;AAErD,MAAI,SAAU,cAAc,IAAI,KAAK,cAAc,IAAI,GAAI;AACzD,UAAM,YAAY,QAAQ,OAAO,OAAO,KAAK,IAAI;AACjD,UAAM,WAAW,UAAU;AAC3B,UAAM,YAAY,QAAQ,OAAO,OAAO,KAAK,IAAI;AACjD,UAAM,WAAW,UAAU;AAC3B,UAAM,OAAY,QAAQ,CAAC,IAAI,CAAA;AAE/B,QAAI,aAAa;AAEjB,aAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,YAAM,MAAM,QAAQ,IAAI,UAAU,CAAC;AACnC,UACE,CAAC,SACD,KAAK,GAAG,MAAM,UACd,KAAK,GAAG,MAAM,UACd,UAAU,SAAS,GAAG,GACtB;AACA,aAAK,GAAG,IAAI;AACZ;MAAA,OACK;AACA,aAAA,GAAG,IAAI,iBAAiB,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC;AAC7C,YAAA,KAAK,GAAG,MAAM,KAAK,GAAG,KAAK,KAAK,GAAG,MAAM,QAAW;AACtD;QACF;MACF;IACF;AAEA,WAAO,aAAa,YAAY,eAAe,WAAW,OAAO;EACnE;AAEO,SAAA;AACT;AAGO,SAAS,cAAc,GAAQ;AAChC,MAAA,CAAC,mBAAmB,CAAC,GAAG;AACnB,WAAA;EACT;AAGA,QAAM,OAAO,EAAE;AACX,MAAA,OAAO,SAAS,aAAa;AACxB,WAAA;EACT;AAGA,QAAM,OAAO,KAAK;AACd,MAAA,CAAC,mBAAmB,IAAI,GAAG;AACtB,WAAA;EACT;AAGA,MAAI,CAAC,KAAK,eAAe,eAAe,GAAG;AAClC,WAAA;EACT;AAGO,SAAA;AACT;AAEA,SAAS,mBAAmB,GAAQ;AAClC,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAC/C;AAEO,SAAS,aAAa,OAAgB;AACpC,SAAA,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,OAAO,KAAK,KAAK,EAAE;AACrE;AAEO,SAAS,UAAU,GAAQ,GAAQ,UAAmB,OAAgB;AAC3E,MAAI,MAAM,GAAG;AACJ,WAAA;EACT;AAEI,MAAA,OAAO,MAAM,OAAO,GAAG;AAClB,WAAA;EACT;AAEA,MAAI,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG;AAClC,UAAA,QAAQ,OAAO,KAAK,CAAC;AACrB,UAAA,QAAQ,OAAO,KAAK,CAAC;AAE3B,QAAI,CAAC,WAAW,MAAM,WAAW,MAAM,QAAQ;AACtC,aAAA;IACT;AAEA,WAAO,CAAC,MAAM;MACZ,CAAC,QAAQ,EAAE,OAAO,MAAM,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,OAAO;IAAA;EAE9D;AAEA,MAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,GAAG;AACxC,WAAO,CAAC,EAAE,KAAK,CAAC,MAAM,UAAU,CAAC,UAAU,MAAM,EAAE,KAAK,GAAG,OAAO,CAAC;EACrE;AAEO,SAAA;AACT;AAEO,SAAS,kBAAqD,IAAU;AACvE,QAAA,QAAc,cAAO,EAAE;AAC7B,QAAM,UAAU;AAEV,QAAA,MAAY,cAAO,IAAI,SAAgB,MAAM,QAAQ,GAAG,IAAI,CAAC;AACnE,SAAO,IAAI;AACb;AAEgB,SAAAC,SAAW,MAAS,MAAS;AAC3C,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AAClB,WAAA;EACT;AAGE,MAAA,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,SAAS,YAChB,SAAS,MACT;AACO,WAAA;EACT;AAEM,QAAA,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AACtC,WAAA;EACT;AAEA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAEnC,QAAA,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM,CAAC,CAAW,KAC9D,CAAC,OAAO,GAAG,KAAK,MAAM,CAAC,CAAY,GAAG,KAAK,MAAM,CAAC,CAAY,CAAC,GAC/D;AACO,aAAA;IACT;EACF;AACO,SAAA;AACT;AAmBO,IAAMC,mBACX,OAAO,WAAW,cAAoB,yBAAwB;AAEzD,SAAS,WAAW,YAAoB;AACtC,SAAA,WACJ,QAAQ,OAAO,MAAM,EACrB,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,KAAK;AACxB;;;;ACvTgB,SAAA,SAAS,UAAyB,CAAA,GAAI;AAClD,UAAgB,aAAa;AAC/B,MAAI,QAAQ;AAAa,UAAA;AAClB,SAAA;AACT;AAEO,SAAS,WAAW,KAAgC;AAClD,SAAA,CAAC,EAAC,OAAA,OAAA,SAAA,IAAK;AAChB;AAEO,SAAS,cAAc,OAI3B;AAED,QAAM,WAAW,eAAe;IAC9B,QAAQ,CAAC,MAAM,aAAa,EAAE,SAAS,QAAQ,IAAI,EAAE,MAAM;EAAA,CAC5D;AAGC,aAAA;IAAC;IAAA;MACC,aAAa,MAAM;MACnB,SAAS,CAAC,UAAU;;AACd,YAAA,WAAW,KAAK,GAAG;AACrB,WAAA,KAAA,MAAM,YAAN,OAAA,SAAA,GAAA,KAAA,OAAgB,KAAA;QAAK,OAChB;AACC,gBAAA;QACR;MACF;MACA,gBAAgB,CAAC,EAAE,MAAA,MACjB;;AAAA,gBAAA,KAAA,MAAM,aAAN,OAAA,SAAA,GAAA,KAAA,OAAiB,KAAA;MAAA;MAGlB,UAAM,MAAA;IAAA;EAAA;AAGb;AAEO,SAAS,wBAAwB;AAC/B,aAAA,yBAAC,KAAA,EAAE,UAAS,YAAA,CAAA;AACrB;;;AC1CO,SAAS,SAOd,MACsD;AACpD,OAAa,aAAa;AACxB,MAAA,KAAK,SAAS,MAAM;AAChB,UAAA;EACR;AAEO,SAAA;AACT;AAEO,SAAS,WAAW,KAA8B;AAChD,SAAA,CAAC,EAAC,OAAA,OAAA,SAAA,IAAK;AAChB;;;ACNa,IAAA,eAAqB,qBAAkC,MAAS;AAiDtE,SAAS,UAAU;AACxB,QAAM,SAAS,UAAA;AACf,QAAM,UAAU,eAAe;IAC7B,QAAQ,CAAC,MAAM;;AACb,cAAO,KAAA,mBAAmB,CAAC,EAAE,CAAC,MAAvB,OAAA,SAAA,GAA0B;IACnC;EAAA,CACD;AAED,aACG,yBAAA,aAAa,UAAb,EAAsB,OAAO,SAC5B,cAAA;IAAC;IAAA;MACC,aAAa,MAAA;;AAAM,gBAAA,KAAA,OAAO,MAAM,iBAAiB,UAA9B,OAAA,SAAA,GAAqC;MAAA;MACxD,gBAAgB;MAChB,SAAS,CAAC,UAAU;AAClB;UACE;UACA;QAAA;AAEF,gBAAQ,MAAM,KAAK;MACrB;MAEC,UAAU,cAAA,yBAAC,OAAM,EAAA,QAAkB,CAAA,IAAK;IAAA;EAE7C,EAAA,CAAA;AAEJ;AAEA,SAAS,aAAa,OAAY;AACzB,aAAA,yBAAA,8BAAA,EAAG,UAAA,MAAM,SAAS,CAAA;AAC3B;AAEgB,SAAA,MAAM,EAAE,QAAA,GAAgC;;AACtD,QAAM,SAAS,UAAA;AACf,QAAM,UAAU,eAAe;IAC7B,QAAQ,CAAC,MAAA;;AACP,cAAAC,MAAA,mBAAmB,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,MAAlD,OAAA,SAAAA,IAAqD;IAAA;EAAA,CACxD;AAED;IACE;IACA,uCAAuC,OAAO;EAAA;AAG1C,QAAA,QAAQ,OAAO,WAAW,OAAO;AAEvC,QAAM,mBAAoB,MAAM,QAAQ,oBACtC,OAAO,QAAQ;AAEjB,QAAM,iBAAiB,uBAAoB,yBAAA,kBAAA,CAAA,CAAiB,IAAK;AAEjE,QAAM,sBACJ,MAAM,QAAQ,kBACd,OAAO,QAAQ,yBACf;AAEF,QAAM,yBAAyB,MAAM;;IAEjC,MAAM,QAAQ,uBACd,KAAA,OAAO,QAAQ,kBAAf,OAAA,SAAA,GAA8B,QAAQ;MACtC,MAAM,QAAQ;AAElB,QAAM,2BACJ,MAAM,QAAQ,kBACd,sBACA,KAAA,MAAM,QAAQ,cAAd,OAAA,SAAA,GAAyB,cACzB,KAAA,MAAM,QAAQ,qBAAd,OAAA,SAAA,GAAgC,cAC/B,KAAA,MAAM,QAAQ,mBAAd,OAAA,SAAA,GAAsC,WAC7B,kBACN;AAEA,QAAA,wBAAwB,sBAC1B,gBACA;AAEE,QAAA,2BAA2B,yBAC7B,gBACA;AAGF,aAAA,yBAAC,aAAa,UAAb,EAAsB,OAAO,SAC5B,cAAA,yBAAC,0BAAyB,EAAA,UAAU,gBAClC,cAAA;IAAC;IAAA;MACC,aAAa,MAAA;;AAAM,gBAAAA,MAAA,OAAO,MAAM,iBAAiB,UAA9B,OAAA,SAAAA,IAAqC;MAAA;MACxD,gBAAgB;MAChB,SAAS,CAAC,UAAU;AAElB,YAAI,WAAW,KAAK;AAAS,gBAAA;AACrB,iCAAA,OAAO,yBAAyB,OAAO,EAAE;AACjD,gBAAQ,MAAM,KAAK;MACrB;MAEA,cAAA;QAAC;QAAA;UACC,UAAU,CAAC,UAAU;AAIjB,gBAAA,CAAC,0BACA,MAAM,WAAW,MAAM,YAAY,WACnC,CAAC,MAAM,WAAW,CAAC,MAAM;AAEpB,oBAAA;AAED,mBAAM,qBAAc,wBAAwB,KAAY;UACjE;UAEA,cAAA,yBAAC,YAAW,EAAA,SAAmB,eAAgC,CAAA;QAAA;MACjE;IAAA;EAAA,EAEJ,CAAA,EACF,CAAA;AAEJ;AAEA,SAAS,WAAW;EAClB;EACA;AACF,GAGQ;;AACN,QAAM,SAAS,UAAA;AACf,QAAM,UAAU,eAAe;IAC7B,QAAQ,CAAC,MAAA;;AACP,cAAAA,MAAA,mBAAmB,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,MAAlD,OAAA,SAAAA,IAAqD;IAAA;EAAA,CACxD;AAEK,QAAA,QAAQ,OAAO,WAAW,OAAO;AAEvC,QAAM,QAAQ,eAAe;IAC3B,QAAQ,CAAC,MACP,KAAK,mBAAmB,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,GAAI;MACzD;MACA;MACA;MACA;IAAA,CACD;EAAA,CACJ;AAED,QAAM,uBACH,MAAM,QAAQ,kBAAkB,OAAO,QAAQ,0BAChD;AAEE,MAAA,MAAM,WAAW,YAAY;AAC/B,cAAU,WAAW,MAAM,KAAK,GAAG,2BAA2B;AAE9D,WAAO,oBAAoB,QAAQ,OAAO,MAAM,MAAM,IAAI;EAC5D;AAEI,MAAA,MAAM,WAAW,cAAc;AAGjC,cAAU,WAAW,MAAM,KAAK,GAAG,2BAA2B;AAE9D;MACE;MACA;IAAA;AAGK,WAAA;EACT;AAEI,MAAA,MAAM,WAAW,SAAS;AAM5B,QAAI,UAAU;AAEV,iBAAA;QAAC;QAAA;UACC,OAAO,MAAM;UACb,MAAM;YACJ,gBAAgB;UAClB;QAAA;MAAA;IAGN;AAEI,QAAA,kBAAkB,MAAM,KAAK,GAAG;AAClC,YAAM,qBACJ,KAAA,OAAO,QAAQ,oBAAf,OAAA,SAAA,GAAgC,gBAAe;AAC3C,YAAA,iBAAiB,MAAM,MAAM,IAAI;IAAA,OAClC;AACL,YAAM,MAAM;IACd;EACF;AAEI,MAAA,MAAM,WAAW,WAAW;AAC9B,QAAI,MAAM,aAAa;AACd,aAAA;IACT;AACA,UAAM,MAAM;EACd;AAEI,MAAA,MAAM,WAAW,WAAW;AAC9B,QAAI,OAAO,MAAM,QAAQ,aAAa,OAAO,QAAQ;AAErD,QAAI,MAAM;AACR,iBAAA,yBAAQ,MAAK,CAAA,CAAA;IACf;AAEA,eAAA,yBAAQ,QAAO,CAAA,CAAA;EACjB;AAEA;IACE;IACA;EAAA;AAEJ;AAEO,IAAM,SAAe,YAAK,SAASC,UAAS;AACjD,QAAM,SAAS,UAAA;AACT,QAAA,UAAgB,kBAAW,YAAY;AAC7C,QAAM,UAAU,eAAe;IAC7B,QAAQ,CAAC,MAAA;;AACP,cAAA,KAAA,mBAAmB,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,MAAlD,OAAA,SAAA,GAAqD;IAAA;EAAA,CACxD;AAEK,QAAA,QAAQ,OAAO,WAAW,OAAO;AAEjC,QAAA,EAAE,qBAAqB,IAAI,eAAe;IAC9C,QAAQ,CAAC,MAAM;AACP,YAAA,UAAU,mBAAmB,CAAC;AACpC,YAAM,cAAc,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD;QACE;QACA,4CAA4C,OAAO;MAAA;AAE9C,aAAA;QACL,sBAAsB,YAAY;MAAA;IAEtC;EAAA,CACD;AAED,QAAM,eAAe,eAAe;IAClC,QAAQ,CAAC,MAAM;;AACP,YAAA,UAAU,mBAAmB,CAAC;AACpC,YAAM,QAAQ,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,OAAO;AAChD,cAAA,KAAA,QAAQ,QAAQ,CAAC,MAAjB,OAAA,SAAA,GAAoB;IAC7B;EAAA,CACD;AAED,MAAI,sBAAsB;AACjB,WAAA,oBAAoB,QAAQ,OAAO,MAAS;EACrD;AAEA,MAAI,CAAC,cAAc;AACV,WAAA;EACT;AAEO,aAAA,yBAAC,OAAM,EAAA,SAAS,aAAc,CAAA;AACvC,CAAC;AAED,SAAS,oBAAoB,QAAmB,OAAiB,MAAW;AACtE,MAAA,CAAC,MAAM,QAAQ,mBAAmB;AAChC,QAAA,OAAO,QAAQ,0BAA0B;AAC3C,iBAAQ,yBAAA,OAAO,QAAQ,0BAAf,EAAwC,KAAY,CAAA;IAC9D;AAEI,QAAA,MAAwC;AAC1C;QACE,MAAM,QAAQ;QACd,yDAAyD,MAAM,EAAE;MAAA;IAErE;AAEA,eAAA,yBAAQ,uBAAsB,CAAA,CAAA;EAChC;AAEA,aAAQ,yBAAA,MAAM,QAAQ,mBAAd,EAAgC,KAAY,CAAA;AACtD;AA0BO,SAAS,gBAEZ;AACa,iBAAA,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,UAAU,EAAE,gBAAgB,EAAA,CAAG;AAC5D,QAAA,EAAE,WAAA,IAAe,UAAA;AAEvB,SAAa;IACX,CAOE,SACmE;AACnE,YAAM,EAAE,SAAS,eAAe,OAAO,eAAe,GAAG,KAAS,IAAA;AAElE,aAAO,WAAW,MAAa;QAC7B;QACA;QACA;QACA;MAAA,CACD;IACH;IACA,CAAC;EAAA;AAEL;AAoBO,SAAS,WAOd,OACK;AACL,QAAM,aAAa,cAAA;AACb,QAAA,SAAS,WAAW,KAAY;AAElC,MAAA,OAAO,MAAM,aAAa,YAAY;AAChC,WAAA,MAAM,SAAiB,MAAM;EACvC;AAEA,SAAO,CAAC,CAAC,SAAS,MAAM,WAAW;AACrC;AAEO,SAAS,mBAEd,OAAgC;;AACzB,WAAA,KAAA,MAAM,mBAAN,OAAA,SAAA,GAAsB,KAAK,CAAC,MAAM,EAAE,WAAA,KACvC,MAAM,iBACN,MAAM;AACZ;AAEO,SAAS,SAOd,MAGW;;AACX,QAAM,SAAS,UAAA;AACT,QAAA,iBAAuB,kBAAW,YAAY;AAEpD,QAAM,uBAAsB,KAAA,mBAAmB,OAAO,KAAK,EAAE;IAC3D,CAAC,MAAM,EAAE,OAAO;EACf,MAFyB,OAAA,SAAA,GAEzB;AAEH,QAAM,gBAAgB,MAAM;AACpB,UAAA,UAAU,mBAAmB,OAAO,KAAK;AAC/C,UAAM,SAAQ,QAAA,OAAA,SAAA,KAAM,QAChB,QAAQ,KAAK,CAAC,MAAM,EAAE,aAAY,QAAA,OAAA,SAAA,KAAM,KAAI,IAC5C,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,cAAc;AAC/C,WAAO,MAAO;EAAA,GAAA;AAGZ,OAAA,QAAA,OAAA,SAAA,KAAM,WAAU,MAAM;AACxB;MACE,uBAAuB;MACvB,aACE,YACF,kEAAkE,mBAAmB,uCACnF,YACF,wCACE,YACF;IAAA;EAEJ;AAEA,QAAM,iBAAiB,eAAe;IACpC,QAAQ,CAAC,UAAU;AACX,YAAA,QAAQ,mBAAmB,KAAK,EAAE;QAAK,CAAC,OAC5C,QAAA,OAAA,SAAA,KAAM,SAAO,QAAA,OAAA,SAAA,KAAM,UAAS,EAAE,UAAU,EAAE,OAAO;MAAA;AAGnD;QACE;QACA,mBACE,QAAA,OAAA,SAAA,KAAM,QACF,yBAAyB,KAAK,IAAI,MAClC,kBACN;MAAA;AAGF,cAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,KAAY,IAAI;IACpD;EAAA,CACD;AAEM,SAAA;AACT;AAEO,SAAS,WAMd,MAGI;AACJ,SAAO,eAAe;IACpB,QAAQ,CAAC,UAAU;AACX,YAAA,UAAU,mBAAmB,KAAK;AACxC,cAAO,QAAA,OAAA,SAAA,KAAM,UACT,KAAK,OAAO,OAAwB,IACnC;IACP;EAAA,CACD;AACH;AAEO,SAAS,iBAMd,MAGI;AACE,QAAA,iBAAuB,kBAAW,YAAY;AAEpD,SAAO,WAAW;IAChB,QAAQ,CAAC,YAAY;AACnB,gBAAU,QAAQ;QAChB;QACA,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,cAAc;MAAA;AAElD,cAAO,QAAA,OAAA,SAAA,KAAM,UACT,KAAK,OAAO,OAAwB,IACnC;IACP;EAAA,CACD;AACH;AAEO,SAAS,gBAMd,MAGI;AACE,QAAA,iBAAuB,kBAAW,YAAY;AAEpD,SAAO,WAAW;IAChB,QAAQ,CAAC,YAAY;AACnB,gBAAU,QAAQ;QAChB,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,cAAc,IAAI;MAAA;AAEtD,cAAO,QAAA,OAAA,SAAA,KAAM,UACT,KAAK,OAAO,OAAwB,IACnC;IACP;EAAA,CACD;AACH;AAEO,SAAS,cASd,MAGW;AACX,SAAO,SAAS;IACd,GAAG;IACH,QAAQ,CAAC,MAAM;AACN,aAAA,OAAO,KAAK,WAAW,aAC1B,KAAK,OAAO,KAAA,OAAA,SAAA,EAAG,UAAU,IACzB,KAAA,OAAA,SAAA,EAAG;IACT;EAAA,CACD;AACH;AAEO,SAAS,cASd,MAGW;AACX,SAAO,SAAS;IACd,GAAG;IACH,QAAQ,CAAC,MAAM;AACN,aAAA,OAAO,KAAK,WAAW,aAC1B,KAAK,OAAO,KAAA,OAAA,SAAA,EAAG,UAAU,IACzB,KAAA,OAAA,SAAA,EAAG;IACT;EAAA,CACD;AACH;AAEO,SAAS,kBAAkB,OAGhC;AACA,MAAI,EAAE,OAAO,UAAU,YAAY,SAAS,UAAU;AAAe,WAAA;AACjE,MAAA,EAAE,qBAAqB,SAAS,MAAM;AAAyB,WAAA;AACnE,MAAI,EAAE,OAAO,MAAM,SAAS,YAAY,MAAM;AAAc,WAAA;AAE5D,SAAO,MAAM,oBAAoB;AACnC;AAEO,SAAS,wBAAwB,gBAAqC;AACvE,MAAA,UAAU,kBAAkB,aAAa,gBAAgB;AAC3D,UAAM,QAAQ,IAAI,MAAM,eAAe,OAAO;AAC9C,UAAM,OAAO,eAAe;AACrB,WAAA;EACT;AAEA,SAAO,eAAe;AACxB;;;AC/nBO,SAAS,UAAU,OAA+B;AACvD,SAAO,UAAU,MAAM,OAAO,OAAO,EAAE,KAAK,GAAG,CAAC;AAClD;AAEO,SAAS,UAAU,MAAc;AAE/B,SAAA,KAAK,QAAQ,WAAW,GAAG;AACpC;AAEO,SAAS,aAAa,MAAc;AACzC,SAAO,SAAS,MAAM,OAAO,KAAK,QAAQ,WAAW,EAAE;AACzD;AAEO,SAAS,cAAc,MAAc;AAC1C,SAAO,SAAS,MAAM,OAAO,KAAK,QAAQ,WAAW,EAAE;AACzD;AAEO,SAAS,SAAS,MAAc;AAC9B,SAAA,cAAc,aAAa,IAAI,CAAC;AACzC;AAEgB,SAAA,YAAY,UAAkB,MAAc,IAAY;AAC/D,SAAA,KAAK,QAAQ,IAAI,OAAO,IAAI,QAAQ,EAAE,GAAG,GAAG;AAC9C,OAAA,GAAG,QAAQ,IAAI,OAAO,IAAI,QAAQ,EAAE,GAAG,GAAG;AAE3C,MAAA,eAAe,cAAc,IAAI;AAC/B,QAAA,aAAa,cAAc,EAAE;AAExB,aAAA,QAAQ,CAAC,WAAW,UAAU;;AACnC,QAAA,UAAU,UAAU,KAAK;AAC3B,UAAI,CAAC,OAAO;AAEV,uBAAe,CAAC,SAAS;MAChB,WAAA,UAAU,WAAW,SAAS,GAAG;AAE1C,qBAAa,KAAK,SAAS;MAAA;AACtB;IAEP,WACS,UAAU,UAAU,MAAM;AAEnC,UAAI,aAAa,SAAS,OAAK,KAAA,KAAK,YAAY,MAAjB,OAAA,SAAA,GAAoB,WAAU,KAAK;AAChE,qBAAa,IAAI;MACnB;AACA,mBAAa,IAAI;IAAA,WACR,UAAU,UAAU,KAAK;AAClC;IAAA,OACK;AACL,mBAAa,KAAK,SAAS;IAC7B;EAAA,CACD;AAED,QAAM,SAAS,UAAU,CAAC,UAAU,GAAG,aAAa,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAExE,SAAO,UAAU,MAAM;AACzB;AAEO,SAAS,cAAc,UAA8B;AAC1D,MAAI,CAAC,UAAU;AACb,WAAO,CAAA;EACT;AAEA,aAAW,UAAU,QAAQ;AAE7B,QAAM,WAAsB,CAAA;AAE5B,MAAI,SAAS,MAAM,GAAG,CAAC,MAAM,KAAK;AACrB,eAAA,SAAS,UAAU,CAAC;AAC/B,aAAS,KAAK;MACZ,MAAM;MACN,OAAO;IAAA,CACR;EACH;AAEA,MAAI,CAAC,UAAU;AACN,WAAA;EACT;AAGA,QAAM,QAAQ,SAAS,MAAM,GAAG,EAAE,OAAO,OAAO;AAEvC,WAAA;IACP,GAAG,MAAM,IAAI,CAAC,SAAkB;AAC1B,UAAA,SAAS,OAAO,SAAS,KAAK;AACzB,eAAA;UACL,MAAM;UACN,OAAO;QAAA;MAEX;AAEA,UAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AACnB,eAAA;UACL,MAAM;UACN,OAAO;QAAA;MAEX;AAEO,aAAA;QACL,MAAM;QACN,OAAO;MAAA;IACT,CACD;EAAA;AAGH,MAAI,SAAS,MAAM,EAAE,MAAM,KAAK;AACnB,eAAA,SAAS,UAAU,CAAC;AAC/B,aAAS,KAAK;MACZ,MAAM;MACN,OAAO;IAAA,CACR;EACH;AAEO,SAAA;AACT;AAQO,SAAS,gBAAgB;EAC9B;EACA;EACA;EACA;AACF,GAA2B;AACnB,QAAA,2BAA2B,cAAc,IAAI;AAE5C,SAAA;IACL,yBAAyB,IAAI,CAAC,YAAY;AACpC,UAAA,QAAQ,SAAS,YAAY;AAC/B,cAAM,QAAQ,OAAO;AACjB,YAAA;AAAgB,iBAAO,GAAG,QAAQ,KAAK,GAAG,SAAS,EAAE;AAClD,eAAA;MACT;AAEI,UAAA,QAAQ,SAAS,SAAS;AAC5B,YAAI,aAAa;AACT,gBAAA,QAAQ,OAAO,QAAQ,KAAK;AAClC,iBAAO,GAAG,QAAQ,KAAK,GAAG,SAAS,EAAE;QACvC;AACA,eAAO,OAAQ,QAAQ,MAAM,UAAU,CAAC,CAAC,KAAK;MAChD;AAEA,aAAO,QAAQ;IAAA,CAChB;EAAA;AAEL;AAEgB,SAAA,cACd,UACA,iBACA,eAC2B;AAC3B,QAAM,aAAa,YAAY,UAAU,iBAAiB,aAAa;AAGnE,MAAA,cAAc,MAAM,CAAC,YAAY;AACnC;EACF;AAEA,SAAO,cAAc,CAAA;AACvB;AAEgB,SAAA,eAAe,UAAkB,UAAkB;AACjE,SAAO,YAAY,MAAM,SAAS,QAAQ,UAAU,EAAE,IAAI;AAC5D;AAEgB,SAAA,YACd,UACA,MACA,eACoC;AAE7B,SAAA,eAAe,UAAU,IAAI;AAEpC,QAAM,KAAK,eAAe,UAAU,GAAG,cAAc,MAAM,GAAG,EAAE;AAG1D,QAAA,eAAe,cAAc,IAAI;AACjC,QAAA,gBAAgB,cAAc,EAAE;AAEtC,MAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AACzB,iBAAa,QAAQ;MACnB,MAAM;MACN,OAAO;IAAA,CACR;EACH;AAEA,MAAI,CAAC,GAAG,WAAW,GAAG,GAAG;AACvB,kBAAc,QAAQ;MACpB,MAAM;MACN,OAAO;IAAA,CACR;EACH;AAEA,QAAM,SAAiC,CAAA;AAEvC,MAAI,WAAW,MAAM;AAEb,aAAA,IAAI,GACR,IAAI,KAAK,IAAI,aAAa,QAAQ,cAAc,MAAM,GACtD,KACA;AACM,YAAA,cAAc,aAAa,CAAC;AAC5B,YAAA,eAAe,cAAc,CAAC;AAE9B,YAAA,oBAAoB,KAAK,aAAa,SAAS;AAC/C,YAAA,qBAAqB,KAAK,cAAc,SAAS;AAEvD,UAAI,cAAc;AACZ,YAAA,aAAa,SAAS,YAAY;AACpC,cAAI,eAAA,OAAA,SAAA,YAAa,OAAO;AACtB,kBAAM,SAAS;cACb,UAAU,aAAa,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;YAAA;AAGrD,mBAAO,GAAG,IAAI;AACd,mBAAO,QAAQ,IAAI;AACZ,mBAAA;UACT;AACO,iBAAA;QACT;AAEI,YAAA,aAAa,SAAS,YAAY;AACpC,cAAI,aAAa,UAAU,OAAO,EAAC,eAAA,OAAA,SAAA,YAAa,QAAO;AAC9C,mBAAA;UACT;AAEA,cAAI,aAAa;AACf,gBAAI,cAAc,eAAe;AAC3B,kBAAA,aAAa,UAAU,YAAY,OAAO;AACrC,uBAAA;cACT;YAAA,WAEA,aAAa,MAAM,YAAA,MACnB,YAAY,MAAM,YAAA,GAClB;AACO,qBAAA;YACT;UACF;QACF;AAEA,YAAI,CAAC,aAAa;AACT,iBAAA;QACT;AAEI,YAAA,aAAa,SAAS,SAAS;AAC7B,eAAA,eAAA,OAAA,SAAA,YAAa,WAAU,KAAK;AACvB,mBAAA;UACT;AACA,cAAI,YAAY,MAAM,OAAO,CAAC,MAAM,KAAK;AACvC,mBAAO,aAAa,MAAM,UAAU,CAAC,CAAC,IAAI;cACxC,YAAY;YAAA;UAEhB;QACF;MACF;AAEI,UAAA,CAAC,qBAAqB,oBAAoB;AAC5C,eAAO,IAAI,IAAI,UAAU,aAAa,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AACtE,eAAO,CAAC,CAAC,cAAc,UAAS,gBAAA,OAAA,SAAA,aAAc,WAAU;MAC1D;IACF;AAEO,WAAA;EAAA,GAAA;AAGT,SAAO,UAAW,SAAoC;AACxD;;;AC/QO,SAAS,UASd,MAGW;AACX,SAAO,eAAe;IACpB,QAAQ,CAAC,UAAe;;AACtB,YAAM,UAAU,KAAA,KAAK,mBAAmB,KAAK,CAAC,MAA9B,OAAA,SAAA,GAAyC;AACzD,cAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,MAAM,IAAI;IAC9C;EAAA,CACD;AACH;;;ACpBO,SAAS,UAYd,MAGW;AACX,SAAO,SAAS;IACd,GAAG;IACH,QAAQ,CAAC,UAAsB;AAC7B,cAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,MAAM,MAAiB,IAAI,MAAM;IACrE;EAAA,CACD;AACH;;;ACNO,IAAM,cAAc;AA4YpB,SAAS,YAWd,IAAS;AACT,SAAO,IAAI,SAQT,EAAE,GAAA,CAAI;AACV;AAEO,IAAM,WAAN,MAWL;;;;EAMA,YAAY,EAAE,GAAA,GAAmB;AAIjC,SAAA,WAAW,CAIT,SAEe;AACR,aAAA,SAAS,EAAE,QAAQ,QAAA,OAAA,SAAA,KAAM,QAAQ,MAAM,KAAK,GAAA,CAAI;IAAA;AAGzD,SAAA,kBAAkB,CAA0B,SAE3B;AACf,aAAO,SAAS;QACd,MAAM,KAAK;QACX,QAAQ,CAAC,OAAY,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,EAAE,OAAO,IAAI,EAAE;MAAA,CAChE;IAAA;AAGH,SAAA,YAAY,CAAgC,SAE3B;AACf,aAAO,UAAU,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAI;IAAA;AAG7C,SAAA,YAAY,CAAyB,SAEpB;AACf,aAAO,UAAU,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAI;IAAA;AAG7C,SAAA,gBAAgB,CAA0B,SAEzB;AACf,aAAO,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAW;IAAA;AAGxD,SAAA,gBAAgB,CAA0B,SAEzB;AACf,aAAO,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAW;IAAA;AAGxD,SAAA,WAAW,CAAC,SAAyB;AACnC,aAAO,SAAS,EAAE,SAAS,KAAK,IAAc,GAAG,KAAA,CAAM;IAAA;AA/CvD,SAAK,KAAK;EACZ;AAgDF;AAEO,IAAM,QAAN,MAwDL;;;;EAwCA,YACE,SAmBA;AAiCF,SAAA,OAAO,CAAC,SAAoC;;AAC1C,WAAK,gBAAgB,KAAK;AAE1B,YAAMC,WAAU,KAAK;AAqBrB,YAAM,SAAS,EAACA,YAAA,OAAA,SAAAA,SAAS,SAAQ,EAACA,YAAA,OAAA,SAAAA,SAAS;AAEtC,WAAA,eAAc,MAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,mBAAd,OAAA,SAAA,GAAA,KAAA,EAAA;AAEnB,UAAI,QAAQ;AACV,aAAK,OAAO;MAAA,OACP;AACL;UACE,KAAK;UACL;QAAA;MAEJ;AAEI,UAAA,OAA2B,SAAS,cAAcA,SAAQ;AAG1D,UAAA,QAAQ,SAAS,KAAK;AACxB,eAAO,aAAa,IAAI;MAC1B;AAEM,YAAA,YAAWA,YAAA,OAAA,SAAAA,SAAS,OAAM;AAG5B,UAAA,KAAK,SACL,cACA,UAAU;QACP,KAAK,YAAY,OAAe,cAC7B,KACA,KAAK,YAAY;QACrB;MAAA,CACD;AAEL,UAAI,SAAS,aAAa;AACjB,eAAA;MACT;AAEA,UAAI,OAAO,aAAa;AACtB,aAAK,UAAU,CAAC,KAAK,EAAE,CAAC;MAC1B;AAEM,YAAA,WACJ,OAAO,cAAc,MAAM,UAAU,CAAC,KAAK,YAAY,UAAU,IAAI,CAAC;AAExE,WAAK,OAAO;AACZ,WAAK,KAAK;AAEV,WAAK,WAAW;AAChB,WAAK,KAAK;IAAA;AAGZ,SAAA,cAAc,CACZ,aAuBG;AACH,WAAK,WAAW;AACT,aAAA;IAAA;AAGT,SAAA,eAAe,CAAuCA,aAQhD;AACG,aAAA,OAAO,KAAK,SAASA,QAAO;AAC5B,aAAA;IAAA;AAwBT,SAAA,SAAS,CACPA,aACG;AACI,aAAA,OAAO,KAAK,SAASA,QAAO;AAC5B,aAAA;IAAA;AAGT,SAAA,OAAO,CAACC,YAA0C;AAChD,WAAK,SAASA;AACP,aAAA;IAAA;AAGT,SAAA,WAAW,CAIT,SAEe;AACf,aAAO,SAAS,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAI;IAAA;AAG5C,SAAA,kBAAkB,CAA0B,SAE3B;AACf,aAAO,SAAS;QACd,GAAG;QACH,MAAM,KAAK;QACX,QAAQ,CAAC,OAAY,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,EAAE,OAAO,IAAI,EAAE;MAAA,CAChE;IAAA;AAGH,SAAA,YAAY,CAAgC,SAE3B;AACf,aAAO,UAAU,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAI;IAAA;AAG7C,SAAA,YAAY,CAAyB,SAEpB;AACf,aAAO,UAAU,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAI;IAAA;AAG7C,SAAA,gBAAgB,CAA0B,SAEzB;AACf,aAAO,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAW;IAAA;AAGxD,SAAA,gBAAgB,CAA0B,SAEzB;AACf,aAAO,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAW;IAAA;AA9NjD,SAAA,UAAW,WAAmB,CAAA;AAC9B,SAAA,SAAS,EAAC,WAAA,OAAA,SAAA,QAAS;AACxB;MACE,GAAG,WAAA,OAAA,SAAA,QAAiB,QAAO,WAAA,OAAA,SAAA,QAAiB;MAC5C;IAAA;AAEA,SAAa,WAAW,OAAO,IAAI,YAAY;EACnD;AAyNF;AAEO,SAAS,YAyDd,SAmBA;AACO,SAAA,IAAI,MAsBT,OAAO;AACX;AAIO,SAAS,6BAAwD;AACtE,SAAO,CAcL,YA2BG;AACH,WAAO,gBASL,OAAc;EAAA;AAEpB;AAKO,IAAM,uBAAuB;AAM7B,IAAM,YAAN,cAcG,MAsBR;;;;EAIA,YACE,SA2BA;AACA,UAAM,OAAc;EACtB;AACF;AAEO,SAAS,gBAed,SA2BA;AACO,SAAA,IAAI,UAUT,OAAO;AACX;AAkDO,SAAS,gBAKd,MAGuB;AAChB,SAAA;AACT;AAqCO,IAAM,gBAAN,cA8BG,MAsBR;EACA,YACE,SAsBA;AACM,UAAA;MACJ,GAAI;MACJ,IAAI;IAAA,CACL;EACH;AACF;;;AC7yCgB,SAAA,OAAO,KAAK,KAAc;AACpC,MAAA,GACF,GACA,KACA,MAAM;AAER,OAAK,KAAK,KAAK;AACb,SAAK,MAAM,IAAI,CAAC,OAAO,QAAQ;AACzB,UAAA,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC/B,kBAAQ,OAAO;AACf,iBAAO,mBAAmB,CAAC,IAAI,MAAM,mBAAmB,IAAI,CAAC,CAAC;QAChE;MAAA,OACK;AACL,gBAAQ,OAAO;AACf,eAAO,mBAAmB,CAAC,IAAI,MAAM,mBAAmB,GAAG;MAC7D;IACF;EACF;AAEA,UAAQ,OAAO,MAAM;AACvB;AAEA,SAAS,QAAQ,KAAK;AACpB,MAAI,CAAC;AAAY,WAAA;AACb,MAAA,MAAM,mBAAmB,GAAG;AAChC,MAAI,QAAQ;AAAgB,WAAA;AAC5B,MAAI,QAAQ;AAAe,WAAA;AACpB,SAAA,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM;AACtD;AAEgB,SAAA,OAAO,KAAK,KAAc;AACxC,MAAI,KACF,GACA,MAAM,CAAA,GACN,OAAO,MAAM,IAAI,OAAO,IAAI,MAAM,IAAI,KAAK,MAAM,GAAG;AAE9C,SAAA,MAAM,IAAI,MAAA,GAAU;AACpB,UAAA,IAAI,MAAM,GAAG;AACnB,QAAI,IAAI,MAAA;AACJ,QAAA,IAAI,CAAC,MAAM,QAAQ;AACrB,UAAI,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG,QAAQ,IAAI,MAAM,CAAC,CAAC;IAAA,OAC1C;AACL,UAAI,CAAC,IAAI,QAAQ,IAAI,MAAO,CAAA;IAC9B;EACF;AAEO,SAAA;AACT;;;ACjDa,IAAA,qBAAqB,gBAAgB,KAAK,KAAK;AACrD,IAAM,yBAAyB;EACpC,KAAK;EACL,KAAK;AACP;AAEO,SAAS,gBAAgB,QAA8B;AAC5D,SAAO,CAAC,cAAuC;AAC7C,QAAI,UAAU,UAAU,GAAG,CAAC,MAAM,KAAK;AACzB,kBAAA,UAAU,UAAU,CAAC;IACnC;AAEI,QAAA,QAAiC,OAAO,SAAS;AAGrD,aAAS,OAAO,OAAO;AACf,YAAA,QAAQ,MAAM,GAAG;AACnB,UAAA,OAAO,UAAU,UAAU;AACzB,YAAA;AACI,gBAAA,GAAG,IAAI,OAAO,KAAK;QAAA,SAClB,KAAK;QAEd;MACF;IACF;AAEO,WAAA;EAAA;AAEX;AAEgB,SAAA,oBACd,WACA,QACA;AACA,WAAS,eAAe,KAAU;AAChC,QAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACvC,UAAA;AACF,eAAO,UAAU,GAAG;MAAA,SACb,KAAK;MAEd;IAAA,WACS,OAAO,QAAQ,YAAY,OAAO,WAAW,YAAY;AAC9D,UAAA;AAGF,eAAO,GAAG;AACV,eAAO,UAAU,GAAG;MAAA,SACb,KAAK;MAEd;IACF;AACO,WAAA;EACT;AAEA,SAAO,CAAC,WAAgC;AAC7B,aAAA,EAAE,GAAG,OAAA;AAEd,QAAI,QAAQ;AACV,aAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,QAAQ;AAC7B,cAAA,MAAM,OAAO,GAAG;AACtB,YAAI,OAAO,QAAQ,eAAe,QAAQ,QAAW;AACnD,iBAAO,OAAO,GAAG;QAAA,OACZ;AACE,iBAAA,GAAG,IAAI,eAAe,GAAG;QAClC;MAAA,CACD;IACH;AAEA,UAAM,YAAY,OAAO,MAAgC,EAAE,SAAS;AAE7D,WAAA,YAAY,IAAI,SAAS,KAAK;EAAA;AAEzC;;;;;AC7DA,IAAMC,iBACE,yBACL,MAAM;EACL;EACA,CAAC,OAAO;AACH,OAAA;EACL;AACF;AAsCK,SAAS,eAGd,EAAE,QAAQ,GAAG,KAAA,GAA8C;AAE3D,SAAO,OAAO;IACZ,GAAG,OAAO;IACV,GAAG;IACH,SAAS;MACP,GAAG,OAAO,QAAQ;MAClB,GAAG,QAAA,OAAA,SAAA,KAAM;IACX;EAAA,CACM;AAER,QAAM,UAAU,OAAO,QAAQ,gBAAA,yBAC5B,OAAO,QAAQ,WAAf,EACC,cAAC,yBAAA,SAAA,CAAA,CAAQ,EACX,CAAA,QAAA,yBAEC,SAAQ,CAAA,CAAA;AAGX,QAAMC,iBAAgB,iBAAA;AAEtB,QAAM,eACH,0BAAAA,eAAc,UAAd,EAAuB,OAAO,QAC5B,UAAA;IAAA;QAAA,yBACA,cAAa,CAAA,CAAA;EAChB,EAAA,CAAA;AAGE,MAAA,OAAO,QAAQ,MAAM;AACvB,eAAQ,yBAAA,OAAO,QAAQ,MAAf,EAAqB,UAAS,SAAA,CAAA;EACxC;AAEO,SAAA;AACT;AAEA,SAAS,eAAe;AACtB,QAAM,SAAS,UAAA;AACf,QAAM,qBAA2B,cAAO,EAAE,QAAQ,SAAS,MAAA,CAAO;AAClE,QAAM,cAAc,eAAe;IACjC,QAAQ,CAAC,MACP,KAAK,GAAG,CAAC,aAAa,YAAY,oBAAoB,iBAAiB,CAAC;EAAA,CAC3E;AAED,QAAM,CAAC,iBAAiB,oBAAoB,IAAID,eAAc;AAE9D,SAAO,uBAAuB;AAE9B,EAAM,iBAAU,MAAM;AACpB,QAAI,iBAAiB;AACZ,aAAA,QAAQ,SAAS,CAAC,OAAO;QAC9B,GAAG;QACH;MACA,EAAA;IACJ;EAAA,GACC,CAAC,eAAe,CAAC;AAEpB,QAAM,UAAU,MAAM;AACd,UAAA,QAAQ,CAAC,OAAmB;AAC5B,UAAA,CAAC,YAAY,iBAAiB;AACX,6BAAA,MAAM,GAAA,CAAI;MAAA,OAC1B;AACF,WAAA;MACL;IAAA;AAGF,UAAM,MAAM;AACN,UAAA;AACF,eAAO,KAAK;MAAA,SACL,KAAK;AACZ,gBAAQ,MAAM,GAAG;MACnB;IAAA,CACD;EAAA;AAGH,EAAAE,iBAAgB,MAAM;AACpB,UAAM,QAAQ,OAAO,QAAQ,UAAU,MAAM;AAC3C,aAAO,iBAAiB,OAAO,cAAc,OAAO,cAAc;AAClE,UAAI,OAAO,MAAM,aAAa,OAAO,gBAAgB;AAC3C,gBAAA;MACV;IAAA,CACD;AAED,WAAO,MAAM;AACL,YAAA;IAAA;EACR,GACC,CAAC,OAAO,OAAO,CAAC;AAEnB,EAAAA,iBAAgB,MAAM;;AACpB,QACS,uBACH,YAAY,mBAAmB,CAAC,kBAEhC,CAAC,YAAY,aACb,YAAY,qBAAqB,YAAY,UACjD;AACA,aAAO,KAAK;QACV,MAAM;QACN,cAAc,YAAY;QAC1B,YAAY,YAAY;QACxB,aACE,YAAY,SAAU,WAAS,KAAA,YAAY,qBAAZ,OAAA,SAAA,GAA8B;MAAA,CAChE;AAED,UAAK,SAAiB,eAAe;AAC/B,YAAA,YAAY,SAAS,SAAS,IAAI;AACpC,gBAAM,KAAK,SAAS;YAClB,YAAY,SAAS;UAAA;AAEvB,cAAI,IAAI;AACN,eAAG,eAAe;UACpB;QACF;MACF;AAEO,aAAA,QAAQ,SAAS,CAAC,OAAO;QAC9B,GAAG;QACH,iBAAiB;QACjB,kBAAkB,EAAE;MACpB,EAAA;IACJ;EAAA,GACC;IACD,YAAY;IACZ;IACA,YAAY;IACZ,YAAY;IACZ,YAAY;EAAA,CACb;AAED,EAAAA,iBAAgB,MAAM;AAElB,QAAA,OAAO,sBACN,mBAAmB,QAAQ,WAAW,UACrC,mBAAmB,QAAQ,SAC7B;AACA;IACF;AACA,uBAAmB,UAAU,EAAE,QAAQ,SAAS,KAAK;AAC7C,YAAA;EAAA,GACP,CAAC,MAAM,CAAC;AAEJ,SAAA;AACT;AAEgB,SAAA,cACd,OACA,IACoC;AAC7B,SAAA;IACL,GAAG,MAAM;IACT,GAAI,MAAM,kBAAkB,CAAC;IAC7B,GAAG,MAAM;EAAA,EACT,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE;AAC3B;;;ACHO,IAAM,iBAAiB;EAC5B;EACA;EACA;EACA;AACF;AA8BO,SAAS,aAKd,SACA;AACO,SAAA,IAAI,OAAkD,OAAO;AACtE;AAEO,IAAM,SAAN,MAIL;;;;EAkCA,YACE,SAKA;AAtCF,SAAA,kBAAsC,GAAG,KAAK;MAC5C,KAAK,OAAA,IAAW;IACjB,CAAA;AAC0B,SAAA,kBAAA;AACO,SAAA,kBAAA;AAClC,SAAA,oBAAmC,QAAQ,QAAA;AAC3C,SAAA,cAAA,oBAAkB,IAAA;AAClB,SAAA,eAAoC,CAAA;AAmDa,SAAA,uBAAA,CAAC,OAAO,GAAG;AAE5D,SAAA,SAAS,CACP,eAKG;AACH,UAAI,WAAW,eAAe;AACpB,gBAAA;UACN;QAAA;MAEJ;AAEA,YAAM,kBAAkB,KAAK;AAC7B,WAAK,UAAU;QACb,GAAG,KAAK;QACR,GAAG;MAAA;AAIH,UAAA,CAAC,KAAK,YACL,WAAW,YAAY,WAAW,aAAa,gBAAgB,UAChE;AAEE,YAAA,WAAW,aAAa,UACxB,WAAW,aAAa,MACxB,WAAW,aAAa,KACxB;AACA,eAAK,WAAW;QAAA,OACX;AACL,eAAK,WAAW,IAAI,SAAS,WAAW,QAAQ,CAAC;QACnD;MACF;AAGE,UAAA,CAAC,KAAK,WACL,KAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,KAAK,SACvD;AACK,aAAA,UACH,KAAK,QAAQ,YACZ,OAAO,aAAa,cACjB,qBAAqB,IACrB,oBAAoB;UAClB,gBAAgB,CAAC,KAAK,QAAQ,YAAY,GAAG;QAC9C,CAAA;AACF,aAAA,iBAAiB,KAAK,cAAA;MAC7B;AAEA,UAAI,KAAK,QAAQ,cAAc,KAAK,WAAW;AACxC,aAAA,YAAY,KAAK,QAAQ;AAC9B,aAAK,eAAe;MACtB;AAEI,UAAA,CAAC,KAAK,SAAS;AACjB,aAAK,UAAU,IAAI,MAAM,sBAAsB,KAAK,cAAc,GAAG;UACnE,UAAU,MAAM;AACd,iBAAK,QAAQ,QAAQ;cACnB,GAAG,KAAK;cACR,QACE,KAAK,MAAM,mBAAmB,KAAK,MAAM,YACrC,YACA;cACN,eAAe,KAAK,MAAM,cAAc;gBACtC,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,SAAS,EAAE,MAAM;cAC1C;YAAA;UAEJ;QAAA,CACD;MACH;IAAA;AAOF,SAAA,iBAAiB,MAAM;AACrB,WAAK,aAAa,CAAA;AAClB,WAAK,eAAe,CAAA;AAEd,YAAA,gBAAgB,KAAK,QAAQ;AACnC,UAAI,eAAe;AACjB,sBAAc,KAAK,EAAE,eAAe,YAAa,CAAA;AAC/C,aAAK,WAAmB,cAAc,EAAE,IAAI;MAChD;AAEM,YAAA,gBAAgB,CAAC,gBAA4B;AACrC,oBAAA,QAAQ,CAAC,YAAY,MAAM;AACrC,qBAAW,KAAK,EAAE,eAAe,EAAG,CAAA;AAEpC,gBAAM,gBAAiB,KAAK,WAAmB,WAAW,EAAE;AAE5D;YACE,CAAC;YACD,mCAAmC,OAAO,WAAW,EAAE,CAAC;UAAA;AAExD,eAAK,WAAmB,WAAW,EAAE,IAAI;AAE3C,cAAI,CAAC,WAAW,UAAU,WAAW,MAAM;AACnC,kBAAA,kBAAkB,cAAc,WAAW,QAAQ;AAEvD,gBAAA,CAAE,KAAK,aAAqB,eAAe,KAC3C,WAAW,SAAS,SAAS,GAAG,GAChC;AACE,mBAAK,aAAqB,eAAe,IAAI;YACjD;UACF;AAEA,gBAAM,WAAW,WAAW;AAE5B,cAAI,YAAA,OAAA,SAAA,SAAU,QAAQ;AACpB,0BAAc,QAAQ;UACxB;QAAA,CACD;MAAA;AAGW,oBAAA,CAAC,KAAK,SAAS,CAAC;AAE9B,YAAM,eAMA,CAAA;AAEJ,aAAO,OAAO,KAAK,UAAU,EAAiB,QAAQ,CAAC,GAAG,MAAM;;AAChE,YAAI,EAAE,UAAU,CAAC,EAAE,MAAM;AACvB;QACF;AAEM,cAAA,UAAU,aAAa,EAAE,QAAQ;AACjC,cAAA,SAAS,cAAc,OAAO;AAEpC,eAAO,OAAO,SAAS,OAAK,KAAA,OAAO,CAAC,MAAR,OAAA,SAAA,GAAW,WAAU,KAAK;AACpD,iBAAO,MAAM;QACf;AAEA,cAAM,SAAS,OAAO,IAAI,CAACC,OAAM;AAC3BA,cAAAA,GAAE,UAAU,KAAK;AACZ,mBAAA;UACT;AAEIA,cAAAA,GAAE,SAAS,SAAS;AACf,mBAAA;UACT;AAEIA,cAAAA,GAAE,SAAS,YAAY;AAClB,mBAAA;UACT;AAEO,iBAAA;QAAA,CACR;AAEY,qBAAA,KAAK,EAAE,OAAO,GAAG,SAAS,QAAQ,OAAO,GAAG,OAAA,CAAQ;MAAA,CAClE;AAED,WAAK,aAAa,aACf,KAAK,CAAC,GAAG,MAAM;AACR,cAAA,YAAY,KAAK,IAAI,EAAE,OAAO,QAAQ,EAAE,OAAO,MAAM;AAG3D,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,cAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG;AAC/B,mBAAO,EAAE,OAAO,CAAC,IAAK,EAAE,OAAO,CAAC;UAClC;QACF;AAGA,YAAI,EAAE,OAAO,WAAW,EAAE,OAAO,QAAQ;AACvC,iBAAO,EAAE,OAAO,SAAS,EAAE,OAAO;QACpC;AAGA,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,cAAA,EAAE,OAAO,CAAC,EAAG,UAAU,EAAE,OAAO,CAAC,EAAG,OAAO;AACtC,mBAAA,EAAE,OAAO,CAAC,EAAG,QAAS,EAAE,OAAO,CAAC,EAAG,QAAS,IAAI;UACzD;QACF;AAGO,eAAA,EAAE,QAAQ,EAAE;MACpB,CAAA,EACA,IAAI,CAAC,GAAG,MAAM;AACb,UAAE,MAAM,OAAO;AACf,eAAO,EAAE;MAAA,CACV;IAAA;AAGO,SAAA,YAAA,CACV,WACA,OACG;AACH,YAAM,WAAgC;QACpC;QACA;MAAA;AAGG,WAAA,YAAY,IAAI,QAAQ;AAE7B,aAAO,MAAM;AACN,aAAA,YAAY,OAAO,QAAQ;MAAA;IAClC;AAGF,SAAA,OAAO,CAAC,gBAA6B;AAC9B,WAAA,YAAY,QAAQ,CAAC,aAAa;AACjC,YAAA,SAAS,cAAc,YAAY,MAAM;AAC3C,mBAAS,GAAG,WAAW;QACzB;MAAA,CACD;IAAA;AAGH,SAAA,cAAc,CAAC,YAAsD;AACnE,aAAO,KAAK,sBAAsB,UAC9B,KAAK,oBACL;IAAA;AAGN,SAAA,gBAAgB,CACd,qBACiD;AACjD,YAAM,QAAQ,CAAC;QACb;QACA;QACA;QACA;MAAA,MACmE;AACnE,cAAM,eAAe,KAAK,QAAQ,YAAY,MAAM;AACpD,cAAM,YAAY,KAAK,QAAQ,gBAAgB,YAAY;AAEpD,eAAA;UACL;UACA;UACA,QAAQ,iBAAiB,oBAAA,OAAA,SAAA,iBAAkB,QAAQ,YAAY;UAC/D,MAAM,KAAK,MAAM,GAAG,EAAE,QAAQ,EAAE,CAAC,KAAK;UACtC,MAAM,GAAG,QAAQ,GAAG,SAAS,GAAG,IAAI;UACpC,OAAO,iBAAiB,oBAAA,OAAA,SAAA,iBAAkB,OAAO,KAAK;QAAA;MACxD;AAGF,YAAM,WAAW,MAAM,KAAK,QAAQ,QAAQ;AAE5C,UAAI,EAAE,gBAAgB,UAAA,IAAc,SAAS;AAE7C,UAAI,mBAAmB,CAAC,aAAa,cAAc,KAAK,kBAAkB;AAElE,cAAA,qBAAqB,MAAM,cAAc;AAC5B,2BAAA,MAAM,MAAM,SAAS,MAAM;AAE9C,eAAO,mBAAmB,MAAM;AAEzB,eAAA;UACL,GAAG;UACH,gBAAgB;QAAA;MAEpB;AAEO,aAAA;IAAA;AAGa,SAAA,sBAAA,CAAC,MAAc,SAAiB;AACpD,aAAO,YAAY,KAAK,UAAW,MAAM,UAAU,IAAI,CAAC;IAAA;AAO5C,SAAA,cAAA,CACZ,UACA,gBACA,SAC6B;AAC7B,UAAI,cAAsC,CAAA;AAE1C,UAAI,aAAa,KAAK,WAAW,KAAK,CAAC,UAAU;AAC/C,cAAM,gBAAgB;UACpB,KAAK;UACL,cAAc,QAAQ;UACtB;YACE,IAAI,MAAM;YACV,eACE,MAAM,QAAQ,iBAAiB,KAAK,QAAQ;YAC9C,OAAO;UACT;QAAA;AAGF,YAAI,eAAe;AACH,wBAAA;AACP,iBAAA;QACT;AAEO,eAAA;MAAA,CACR;AAED,UAAI,cACF,cAAe,KAAK,WAAmB,WAAW;AAEhD,UAAA,gBAA4B,CAAC,WAAW;AAE5C,UAAI,mBAAmB;AAGvB;;QAEE,aACI,WAAW,SAAS,OAAO,YAAY,IAAI;;UAE3C,cAAc,QAAQ;;QAC1B;AAEI,YAAA,KAAK,QAAQ,eAAe;AAChB,wBAAA,KAAK,KAAK,QAAQ,aAAa;QAAA,OACxC;AAEc,6BAAA;QACrB;MACF;AAEA,aAAO,eAAA,OAAA,SAAA,YAAa,aAAa;AAC/B,sBAAc,YAAY;AACtB,YAAA;AAAa,wBAAc,QAAQ,WAAW;MACpD;AAEA,YAAM,yBAAyB,MAAM;AACnC,YAAI,CAAC,kBAAkB;AACd,iBAAA;QACT;AAEI,YAAA,KAAK,QAAQ,iBAAiB,QAAQ;AACxC,mBAAS,IAAI,cAAc,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,kBAAA,QAAQ,cAAc,CAAC;AAC7B,gBAAI,MAAM,UAAU;AAClB,qBAAO,MAAM;YACf;UACF;QACF;AAEO,eAAA;MAAA,GAAA;AAMT,YAAM,cAAc,cAAc,IAAI,CAAC,UAAU;AAC3C,YAAA;AAEA,YAAA,MAAM,QAAQ,aAAa;AACzB,cAAA;AACF,kBAAM,eAAe,MAAM,QAAQ,YAAY,WAAW;AAEnD,mBAAA,OAAO,aAAa,YAAY;UAAA,SAChC,KAAU;AACG,gCAAA,IAAI,eAAe,IAAI,SAAS;cAClD,OAAO;YAAA,CACR;AAED,gBAAI,QAAA,OAAA,SAAA,KAAM,cAAc;AAChB,oBAAA;YACR;AAEO,mBAAA;UACT;QACF;AAEA;MAAA,CACD;AAED,YAAM,UAA2B,CAAA;AAEnB,oBAAA,QAAQ,CAAC,OAAO,UAAU;;AAQhC,cAAA,cAAc,QAAQ,QAAQ,CAAC;AAGrC,cAAM,CAAC,gBAAgB,WAAW,KAAiC,MAAM;AAEjE,gBAAA,gBAAe,eAAA,OAAA,SAAA,YAAa,WAAU;AAExC,cAAA;AACI,kBAAA,YACJ,OAAO,MAAM,QAAQ,mBAAmB,WACpC,MAAM,QAAQ,eAAe,QAC7B,MAAM,QAAQ;AAEpB,gBAAI,UAAS,aAAA,OAAA,SAAA,UAAY,YAAA,MAAiB,CAAA;AAEnC,mBAAA;cACL;gBACE,GAAG;gBACH,GAAG;cACL;cACA;YAAA;UAAA,SAEK,KAAU;AACjB,kBAAMC,eAAc,IAAI,iBAAiB,IAAI,SAAS;cACpD,OAAO;YAAA,CACR;AAED,gBAAI,QAAA,OAAA,SAAA,KAAM,cAAc;AAChBA,oBAAAA;YACR;AAEO,mBAAA,CAAC,cAAcA,YAAW;UACnC;QAAA,GAAA;AAQI,cAAA,eACJ,MAAA,KAAA,MAAM,SAAQ,eAAd,OAAA,SAAA,GAAA,KAAA,IAA2B;UACzB,QAAQ;QACT,CAAA,MAAK;AAER,cAAM,iBAAiB,aAAa,KAAK,UAAU,UAAU,IAAI;AAEjE,cAAM,mBAAmB,gBAAgB;UACvC,MAAM,MAAM;UACZ,QAAQ;QAAA,CACT;AAED,cAAM,UACJ,gBAAgB;UACd,MAAM,MAAM;UACZ,QAAQ;UACR,gBAAgB;QACjB,CAAA,IAAI;AAKP,YAAI,gBAAgB,cAAc,KAAK,OAAO,OAAO;AAE/C,cAAA,QAAQ,KAAK,MAAM,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,IACzD,SACA;AAEJ,cAAM,QAAuB,gBACzB;UACE,GAAG;UACH;UACA,QAAQ;QAAA,IAEV;UACE,IAAI;UACJ,SAAS,MAAM;UACf,QAAQ;UACR,UAAU,UAAU,CAAC,KAAK,UAAU,gBAAgB,CAAC;UACrD,WAAW,KAAK,IAAI;UACpB,QAAQ,CAAC;UACT,aAAa;UACb,QAAQ;UACR,aAAa;UACb,YAAY;UACZ,OAAO;UACP,aAAa,YAAY,KAAK;UAC9B,aAAa,QAAQ,QAAQ;UAC7B,cAAc;UACd,SAAS;UACT,iBAAiB,IAAI,gBAAgB;UACrC,YAAY;UACZ;UACA;UACA,SAAS;UACT,SAAS;UACT,QAAO,MAAA,KAAA,MAAM,SAAQ,UAAd,OAAA,SAAA,GAAA,KAAA,EAAA;UACP,UAAS,MAAA,KAAA,MAAM,SAAQ,YAAd,OAAA,SAAA,GAAA,KAAA,EAAA;UACT,YAAY,MAAM,QAAQ,cAAc,CAAC;QAAA;AAG3C,YAAA,EAAC,QAAA,OAAA,SAAA,KAAM,UAAS;AAEZ,gBAAA,iBAAiB,0BAA0B,MAAM;QACzD;AAIA,cAAM,SAAS,iBAAiB,MAAM,QAAQ,cAAc;AAE5D,cAAM,cAAc;AAEpB,gBAAQ,KAAK,KAAK;MAAA,CACnB;AAEM,aAAA;IAAA;AAGT,SAAA,cAAc,CAAC,OAAe;IAAA;AAE9B,SAAA,gBAAgB,MAAM;;AACpB,OAAA,KAAA,KAAK,MAAM,mBAAX,OAAA,SAAA,GAA2B,QAAQ,CAAC,UAAU;AACvC,aAAA,YAAY,MAAM,EAAE;MAAA,CAAA;IAC1B;AAGH,SAAA,gBAA6C,CAAC,SAAS;AACrD,YAAM,QAAQ,CACZ,OAEI,CAAA,GACJ,YACmB;;AAanB,cAAM,kBAAkB,KAAK,MAAM,kBAAkB,KAAK,MAAM;AAC1D,cAAA,eACJ,KAAA,gBAAgB,gBAAgB,SAAS,CAAC,MAA1C,OAAA,SAAA,GAA6C,WAC7C,KAAK,eAAe;AAEtB,cAAM,cAAc,KAAK;UACvB,KAAK,eAAe;UACpB;QAAA;AAEF,cAAM,iBAAiB,WAAA,OAAA,SAAA,QAAS;UAAO,CAAC,MACtC,eAAA,OAAA,SAAA,YAAa,KAAK,CAAC,MAAM,EAAE,YAAY,EAAE,OAAA;QAAA;AAG3C,cAAM,YAAY,KAAK,iBAAgB,KAAA,KAAK,WAAW,MAAhB,OAAA,SAAA,GAAmB,OAAO;AAE7D,YAAA,WAAW,KAAK,KAChB,KAAK;UACH,KAAK,QAAQ,KAAK,eAAe;UACjC,GAAG,KAAK,EAAE;QAAA,IAEZ,aAAA,OAAA,SAAA,UAAW;AAEf,cAAM,aAAa,EAAE,IAAG,KAAA,KAAK,WAAW,MAAhB,OAAA,SAAA,GAAmB,OAAO;AAElD,YAAI,cACD,KAAK,UAAU,UAAU,OACtB,aACA,EAAE,GAAG,YAAY,GAAG,iBAAiB,KAAK,QAAS,UAAU,EAAE;AAErE,YAAI,YAAY;AACd,qBAAA,OAAA,SAAA,QACI,IAAI,CAAC,MAAM,KAAK,gBAAgB,EAAE,OAAO,EAAG,QAAQ,eAAA,EACrD,OAAO,OAAA,EACP,QAAQ,CAAC,OAAO;AACf,yBAAa,EAAE,GAAG,YAAa,GAAG,GAAI,UAAW,EAAE;UAAA,CAAA;QAEzD;AAEA,mBAAW,gBAAgB;UACzB,MAAM;UACN,QAAQ,cAAc,CAAC;UACvB,gBAAgB;UAChB,aAAa,KAAK;QAAA,CACnB;AAED,cAAM,oBACJ,kBAAA,OAAA,SAAA,eACI;UACA,CAAC,UACC,KAAK,gBAAgB,MAAM,OAAO,EAAG,QAAQ,oBAC7C,CAAC;QAAA,EAEJ,KAAA,EACA,OAAO,OAAA,MAAY,CAAA;AAExB,cAAM,qBACJ,kBAAA,OAAA,SAAA,eACI;UACA,CAAC,UACC,KAAK,gBAAgB,MAAM,OAAO,EAAG,QAAQ,qBAC7C,CAAC;QAAA,EAEJ,KAAA,EACA,OAAO,OAAA,MAAY,CAAA;AAGlB,cAAA,qBAAoB,oBAAA,OAAA,SAAA,iBAAkB,UACxC,oBAAA,OAAA,SAAA,iBAAkB;UAChB,CAAC,MAAM,SAAS,KAAK,IAAI;UACzB;QAAA,IAEF;AAGJ,cAAM,aACJ,KAAK,WAAW,OACZ,oBACA,KAAK,SACH,iBAAiB,KAAK,QAAQ,iBAAiB,KAAK,CAAA,KACpD,oBAAA,OAAA,SAAA,iBAAkB,UAChB,oBACA,CAAA;AAGV,cAAM,sBAAqB,qBAAA,OAAA,SAAA,kBAAmB,UAC1C,kBAAkB,OAAO,CAAC,MAAM,SAAS,KAAK,IAAI,GAAG,UAAU,IAC/D;AAEE,cAAA,SAAS,iBAAiB,YAAY,kBAAkB;AAE9D,cAAM,YAAY,KAAK,QAAQ,gBAAgB,MAAM;AAErD,cAAM,OACJ,KAAK,SAAS,OACV,KAAK,eAAe,OACpB,KAAK,OACH,iBAAiB,KAAK,MAAO,KAAK,eAAe,IAAI,IACrD;AAER,cAAM,UAAU,OAAO,IAAI,IAAI,KAAK;AAEpC,YAAI,YACF,KAAK,UAAU,OACX,KAAK,eAAe,QACpB,KAAK,QACH,iBAAiB,KAAK,OAAO,KAAK,eAAe,KAAK,IACtD,CAAA;AAER,oBAAY,iBAAiB,KAAK,eAAe,OAAO,SAAS;AAE1D,eAAA;UACL;UACA;UACA;UACA,OAAO;UACP,MAAM,QAAQ;UACd,MAAM,GAAG,QAAQ,GAAG,SAAS,GAAG,OAAO;UACvC,gBAAgB,KAAK;QAAA;MACvB;AAGF,YAAM,mBAAmB,CACvB,OAAyB,CAAA,GACzB,eACG;;AACC,YAAA,OAAO,MAAM,IAAI;AACrB,YAAI,aAAa,aAAa,MAAM,UAAU,IAAI;AAElD,YAAI,CAAC,YAAY;AACf,cAAI,SAAS,CAAA;AAEb,cAAI,aAAY,KAAA,KAAK,QAAQ,eAAb,OAAA,SAAA,GAAyB,KAAK,CAAC,MAAM;AACnD,kBAAM,QAAQ,cAAc,KAAK,UAAU,KAAK,UAAU;cACxD,IAAI,EAAE;cACN,eAAe;cACf,OAAO;YAAA,CACR;AAED,gBAAI,OAAO;AACA,uBAAA;AACF,qBAAA;YACT;AAEO,mBAAA;UAAA,CAAA;AAGT,cAAI,WAAW;AACA,yBAAA;cACX,GAAG,KAAK,MAAM,CAAC,MAAM,CAAC;cACtB,GAAG;cACH;YAAA;AAEF,yBAAa,MAAM,UAAU;UAC/B;QACF;AAEA,cAAM,cAAc,KAAK,YAAY,KAAK,UAAU,KAAK,MAAM;AACzD,cAAA,gBAAgB,aAClB,KAAK,YAAY,WAAW,UAAU,WAAW,MAAM,IACvD;AACJ,cAAM,cAAc,aAChB,MAAM,YAAY,aAAa,IAC/B;AAEE,cAAA,QAAQ,MAAM,MAAM,WAAW;AAErC,YAAI,aAAa;AACf,gBAAM,iBAAiB;QACzB;AAEO,eAAA;MAAA;AAGT,UAAI,KAAK,MAAM;AACb,eAAO,iBAAiB,MAAM;UAC5B,GAAG,KAAK,MAAM,CAAC,MAAM,CAAC;UACtB,GAAG,KAAK;QAAA,CACT;MACH;AAEA,aAAO,iBAAiB,IAAI;IAAA;AAG9B,SAAA,iBAAiB,OAAO;MACtB,iBAAAC;MACA,GAAG;IAAA,MACyC;AAC5C,UAAI,KAAK;AAAiB,qBAAa,KAAK,eAAe;AAE3D,YAAM,YAAY,KAAK,eAAe,SAAS,KAAK;AAIpD,UAAI,CAAC,WAAW;AACd,YAAI,EAAE,gBAAgB,GAAG,YAAA,IAAgB;AAEzC,YAAI,gBAAgB;AACJ,wBAAA;YACZ,GAAG;YACH,OAAO;cACL,GAAG,eAAe;cAClB,WAAW;cACX,gBAAgB;gBACd,GAAG;gBACH,QAAQ,YAAY;gBACpB,OAAO;kBACL,GAAG,YAAY;kBACf,WAAW;kBACX,gBAAgB;kBAChB,KAAK;gBACP;cACF;YACF;UAAA;AAGF,cACE,YAAY,kBACZ,KAAK,QAAQ,kBACb,OACA;AACY,wBAAA,MAAM,YAAY,KAAK;UACrC;QACF;AAEA,cAAM,QAAQ,MAAM;AAClB,eAAK,QAAQ,KAAK,UAAU,YAAY,MAAM;YAC5C,YAAY;YACZ,YAAY;UAAA;QACd;AAGF,YAAIA,oBAAmB,MAAM;AAC3B,eAAK,qBAAqB,KAAK;QAAA,OAC1B;AACC,gBAAA;QACR;MACF;AAEK,WAAA,kBAAkB,KAAK,eAAe;AAE3C,aAAO,KAAK;IAAA;AAGd,SAAA,yBAAyB,CAAC;MACxB;MACA;MACA,iBAAAA;MACA,GAAG;IACL,IAA8C,CAAA,MAAO;AAC7C,YAAA,WAAW,KAAK,cAAc,IAAW;AAC/C,aAAO,KAAK,eAAe;QACzB,GAAG;QACH,iBAAAA;QACA;QACA;MAAA,CACD;IAAA;AAGH,SAAA,WAAuB,CAAC,EAAE,MAAM,IAAI,GAAG,KAAA,MAAW;AAM1C,YAAA,WAAW,OAAO,EAAE;AAEtB,UAAA;AAEA,UAAA;AACE,YAAA,IAAI,GAAG,QAAQ,EAAE;AACR,qBAAA;MAAA,SACN,GAAG;MAAC;AAEb;QACE,CAAC;QACD;MAAA;AAGF,aAAO,KAAK,uBAAuB;QACjC,GAAG;QACH;QACA;;MAAA,CAED;IAAA;AAGH,SAAA,cAAc,OAAO;MACnB;MACA;MACA;MACA;IAAA,MAM2B;;AACvB,UAAA;AACA,UAAA;AAEE,YAAA,cAAc,CAAC,OAAsB,SAAgC;;AACnE,cAAA,aAAYC,MAAA,KAAK,MAAM,mBAAX,OAAA,SAAAA,IAA2B;UAC3C,CAAC,MAAM,EAAE,OAAO,MAAM;QAAA;AAGlB,cAAA,YAAY,KAAK,MAAM,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;AAElE,cAAM,aAAa,YACf,mBACA,YACE,YACA;AAED,aAAA,QAAQ,SAAS,CAAC,MAAO;;AAAA,iBAAA;YAC5B,GAAG;YACH,CAAC,UAAU,IAAG,QAAA,OAAA,SAAA,KAAM,WAChBA,MAAA,EAAE,UAAU,MAAZ,OAAA,SAAAA,IAAe,OAAO,CAAC,MAAM,EAAE,OAAO,MAAM,EAAA,KAC5CC,MAAA,EAAE,UAAU,MAAZ,OAAA,SAAAA,IAAe,IAAI,CAAC,MAAO,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAA;UAC3D;QAAA,CAAA;MAAA;AAGE,YAAA,0BAA0B,CAAC,OAAsB,QAAa;AAC1D,gBAAA;UACN,GAAG;UACH,QAAQ,WAAW,GAAG,IAClB,eACA,WAAW,GAAG,IACZ,aACA;UACN,YAAY;UACZ,OAAO;QAAA;AAGT,oBAAY,KAAK;AAEb,YAAA,CAAC,IAAI,SAAS;AAChB,cAAI,UAAU,MAAM;QACtB;AAEM,cAAA;MAAA;AAIR,eAAS,CAAC,OAAO,KAAK,KAAK,QAAQ,QAAA,GAAW;AACtC,cAAA,cAAc,QAAQ,QAAQ,CAAC;AACrC,cAAM,QAAQ,KAAK,gBAAgB,MAAM,OAAO;AAC1C,cAAA,kBAAkB,IAAI,gBAAA;AAEtB,cAAA,oBAAoB,CAAC,KAAU,SAAiB;;AACpD,cAAI,aAAa;AACjB,+BAAqB,sBAAsB;AAE3C,cAAI,WAAW,GAAG,KAAK,WAAW,GAAG,GAAG;AACtC,oCAAwB,OAAO,GAAG;UACpC;AAEI,cAAA;AACI,aAAAA,OAAAD,MAAA,MAAA,SAAQ,YAAR,OAAA,SAAAC,IAAA,KAAAD,KAAkB,GAAA;UAAA,SACjB,iBAAiB;AAClB,kBAAA;AAEN,gBAAI,WAAW,GAAG,KAAK,WAAW,GAAG,GAAG;AACtC,sCAAwB,OAAO,eAAe;YAChD;UACF;AAEQ,kBAAA,KAAK,IAAI,QAAQ;YACvB,GAAG;YACH,OAAO;YACP,QAAQ;YACR,WAAW,KAAK,IAAI;YACpB,iBAAiB,IAAI,gBAAgB;UAAA;QACvC;AAGF,YAAI,MAAM,aAAa;AACH,4BAAA,MAAM,aAAa,cAAc;QACrD;AAEA,YAAI,MAAM,aAAa;AACH,4BAAA,MAAM,aAAa,iBAAiB;QACxD;AAMI,YAAA;AACF,gBAAM,iBAAgB,eAAA,OAAA,SAAA,YAAa,YAAW,KAAK,QAAQ,WAAW,CAAA;AAEtE,gBAAM,YACJ,MAAM,QAAQ,aAAa,KAAK,QAAQ;AAC1C,gBAAM,iBACJ,OAAO,cAAc,YAAY,aAAa,IAC1C,QAAQ,QACR,IAAA,IAAI,QAAc,CAAC,MAAM,WAAW,GAAG,SAAS,CAAC;AAEvD,gBAAM,oBACH,QAAM,MAAA,KAAA,MAAM,SAAQ,eAAd,OAAA,SAAA,GAAA,KAAA,IAA2B;YAChC,QAAQ,MAAM;YACd;YACA,QAAQ,MAAM;YACd,SAAS,CAAC,CAAC;YACX,SAAS;YACT;YACA,UAAU,CAAC,SACT,KAAK,SAAS,EAAE,GAAG,MAAM,MAAM,MAAM,SAAA,CAAiB;YACxD,eAAe,KAAK;YACpB,OAAO,UAAU,YAAY,MAAM;UACpC,CAAA,MAAO,CAAA;AAEV,cAAI,WAAW,iBAAiB,KAAK,WAAW,iBAAiB,GAAG;AAClE,8BAAkB,mBAAmB,aAAa;UACpD;AAEA,gBAAM,UAAU;YACd,GAAG;YACH,GAAG;UAAA;AAGG,kBAAA,KAAK,IAAI,QAAQ;YACvB,GAAG;YACH,cAAc,iBAAiB,MAAM,cAAc,iBAAiB;YACpE,SAAS,iBAAiB,MAAM,SAAS,OAAO;YAChD;YACA;UAAA;QAAA,SAEK,KAAK;AACZ,4BAAkB,KAAK,aAAa;AACpC;QACF;MACF;AAEA,YAAM,uBAAuB,QAAQ,MAAM,GAAG,kBAAkB;AAChE,YAAM,gBAAgC,CAAA;AAEjB,2BAAA,QAAQ,CAAC,OAAO,UAAU;AAC/B,sBAAA;UACZ,IAAI,QAAc,OAAO,SAAS,WAAW;;AACrC,kBAAA,qBAAqB,cAAc,QAAQ,CAAC;AAClD,kBAAM,QAAQ,KAAK,gBAAgB,MAAM,OAAO;AAE1C,kBAAA,cAAc,CAAC,QAAa;AAChC,kBAAI,WAAW,GAAG,KAAK,WAAW,GAAG,GAAG;AACtC,wCAAwB,OAAO,GAAG;cACpC;YAAA;AAGE,gBAAA;AAEI,oBAAA,KAAK,IAAI,QAAQ;cACvB,GAAG;cACH,aAAa;YAAA;AAGf,gBAAI,iBAAiB;AACrB,kBAAM,YACJ,MAAM,QAAQ,aAAa,KAAK,QAAQ;AAC1C,kBAAM,eACJ,MAAM,QAAQ,gBAAgB,KAAK,QAAQ;AAE7C,kBAAM,gBAAiC;cACrC,QAAQ,MAAM;cACd,MAAM,MAAM;cACZ,SAAS,CAAC,CAAC;cACX;cACA,iBAAiB,MAAM;cACvB,SAAS,MAAM;cACf;cACA,UAAU,CAAC,SACT,KAAK,SAAS,EAAE,GAAG,MAAM,MAAM,MAAM,SAAA,CAAiB;cACxD,OAAO,UAAU,YAAY,MAAM;cACnC;YAAA;AAGF,kBAAM,QAAQ,YAAY;;AACpB,kBAAA;AACF,oBAAI,MAAM,YAAY;AACpB,iCAAcA,MAAA,cAAc,KAAK,OAAO,MAAM,EAAE,MAAlC,OAAA,SAAAA,IAAqC;gBAAA,OAC9C;AASG,0BAAA,KAAK,IAAI,QAAQ;oBACvB,GAAG;oBACH,YAAY;oBACZ,YAAY,MAAM,aAAa;kBAAA;AAGjC,wBAAM,gBACJC,MAAA,MAAM,WAAN,OAAA,SAAAA,IAAA,KAAA,KAAA,EAAiB,KAAK,CAAC,cAAc;AACnC,2BAAO,OAAO,MAAM,SAAS,UAAU,OAAO;kBAAA,CAAA,MAC1C,QAAQ,QAAA;AAKhB,wBAAM,oBAAoB,YAAY;oBAAK,MACzC,QAAQ;sBACN,eAAe,IAAI,OAAO,SAAS;AAC3B,8BAAA,YAAY,MAAM,QAAQ,IAAI;AAEpC,4BAAK,aAAA,OAAA,SAAA,UAAmB,SAAS;AAC/B,gCAAO,UAAkB,QAAA;wBAC3B;sBAAA,CACD;oBACH;kBAAA;AAIF,wBAAM,iBAAgB,MAAA,KAAA,MAAM,SAAQ,WAAd,OAAA,SAAA,GAAA,KAAA,IAAuB,aAAA;AAE7C,gCAAc,QAAQ,IAAI;oBACxB;oBACA;oBACA;kBAAA,CACD,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;gBACrB;AAEQ,wBAAA,KAAK,IAAI,QAAQ;kBACvB,GAAG;kBACH;gBAAA;AAGF,4BAAY,KAAK;AAEjB,sBAAM,aAAa,MAAM;AACzB,oBAAK,gBAAgB,YAAY;AAAI,yBAAO,MAAM;AAElD,4BAAY,UAAU;AAEtB,oBAAI,kBAAkB,cAAc;AAClC,wBAAM,IAAI,QAAQ,CAAC,MAAM,WAAW,GAAG,YAAY,CAAC;gBACtD;AAEA,oBAAK,gBAAgB,YAAY;AAAI,yBAAO,MAAM;AAElD,sBAAM,CAAC,MAAM,OAAO,IAAI,MAAM,QAAQ,IAAI;mBACxC,MAAA,KAAA,MAAM,SAAQ,SAAd,OAAA,SAAA,GAAA,KAAA,IAAqB;oBACnB,QAAQ,MAAM;oBACd;kBAAA,CAAA;mBAEF,MAAA,KAAA,MAAM,SAAQ,YAAd,OAAA,SAAA,GAAA,KAAA,IAAwB;oBACtB;kBAAA,CAAA;gBACD,CACF;AAEO,wBAAA,KAAK,IAAI,QAAQ;kBACvB,GAAG;kBACH,OAAO;kBACP,QAAQ;kBACR,YAAY;kBACZ,WAAW,KAAK,IAAI;kBACpB;kBACA,aAAa;kBACb;kBACA;gBAAA;cAAA,SAEK,OAAO;AACd,oBAAK,gBAAgB,YAAY;AAAI,yBAAO,MAAM;AAElD,4BAAY,KAAK;AAEb,oBAAA;AACI,mBAAA,MAAA,KAAA,MAAA,SAAQ,YAAR,OAAA,SAAA,GAAA,KAAA,IAAkB,KAAA;gBAAA,SACjB,cAAc;AACb,0BAAA;AACR,8BAAY,YAAY;gBAC1B;AAEQ,wBAAA,KAAK,IAAI,QAAQ;kBACvB,GAAG;kBACH;kBACA,QAAQ;kBACR,YAAY;gBAAA;cAEhB;AAEA,0BAAY,KAAK;YAAA;AAInB,kBAAM,MAAM,KAAK,IAAI,IAAI,MAAM;AAE/B,gBAAI,WAAW,UACX,MAAM,QAAQ,oBACd,KAAK,QAAQ,2BACb,MACA,MAAM,QAAQ,aAAa,KAAK,QAAQ,oBAAoB;AAG5D,gBAAA;AAEE,kBAAA,qBAAqB,MAAM,QAAQ;AAIzC,2BACE,OAAO,uBAAuB,aAC1B,mBAAmB,aAAa,IAChC;AAEE,oBAAA,KAAK,IAAI,QAAQ;cACvB,GAAG;cACH,SACE,CAAC,CAAC,WAAW,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;YAAA;AAIlE,gBACE,MAAM,WAAW,cAChB,MAAM,YAAY,gBAAgB,MAAM,YACzC;AACC,eAAC,YAAY;AACR,oBAAA;AACF,wBAAM,MAAM;gBAAA,SACL,KAAK;AACJ,0BAAA,KAAK,6BAA6B,GAAG;AAEzC,sBAAA,WAAW,GAAG,GAAG;AACnB,0BAAM,YACJ,KAAK,MAAM,kBAAkB,KAAK,MAAM,SACxC,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;AAG/B,gCAAY,GAAG;AAGf,wBAAI,UAAU;AACZ,2BAAK,eAAe,GAAG;oBACzB;kBACF;gBACF;cAAA,GAAA;AAGF,qBAAO,QAAQ;YACjB;AAEM,kBAAA,gBACJ,CAAC,WACD,OAAO,cAAc,aACpB,MAAM,QAAQ,oBACb,KAAK,QAAQ;AAEb,gBAAA,MAAM,WAAW,WAAW;AAC1B,kBAAA;AACF,oBAAI,eAAe;AACX,mBAAAD,MAAA,MAAA,mBAAA,OAAA,SAAAA,IAAgB,KAAK,YAAY;AACrC,wBAAK,gBAAgB,YAAY;AAAW,6BAAA;AAE3B,qCAAA;AACT,4BAAA,KAAK,IAAI,QAAQ;sBACvB,GAAG;sBACH,aAAa;oBAAA;AAGf,gCAAY,KAAK;AACT,4BAAA;kBAAA,CAAA;gBAEZ;AAEA,sBAAM,MAAM;cAAA,SACL,KAAK;AACZ,uBAAO,GAAG;cACZ;YACF;AAEQ,oBAAA;UAAA,CACT;QAAA;MACH,CACD;AAEK,YAAA,QAAQ,IAAI,aAAa;AAExB,aAAA;IAAA;AAGT,SAAA,aAAa,MAAM;AACX,YAAA,aAAa,CAAC,OAAY;QAC9B,GAAG;QACH,SAAS;MAAA;AAGN,WAAA,QAAQ,SAAS,CAAC,MAAO;;AAAA,eAAA;UAC5B,GAAG;UACH,SAAS,EAAE,QAAQ,IAAI,UAAU;UACjC,eAAe,EAAE,cAAc,IAAI,UAAU;UAC7C,iBAAgB,KAAA,EAAE,mBAAF,OAAA,SAAA,GAAkB,IAAI,UAAA;QACtC;MAAA,CAAA;AAEF,WAAK,KAAK;IAAA;AAGZ,SAAA,OAAO,YAA2B;AAChC,YAAM,UAAU,IAAI,QAAc,OAAO,SAAS,WAAW;AAC3D,cAAM,OAAO,KAAK;AACZ,cAAA,eAAe,KAAK,MAAM;AAC1B,cAAA,gBAAgB,aAAc,SAAS,KAAK;AAC9C,YAAA;AAGJ,aAAK,cAAc;AAEnB,aAAK,KAAK;UACR,MAAM;UACN,cAAc;UACd,YAAY;UACZ,aAAa;QAAA,CACd;AAEG,YAAA;AACE,cAAA,kBAAkB,KAAK,MAAM;AAE9B,aAAA,QAAQ,MAAM,MAAM;AACvB,eAAK,WAAW;AAGhB,2BAAiB,KAAK,YAAY,KAAK,UAAU,KAAK,QAAQ;YAC5D,OAAO;UAAA,CACR;AAII,eAAA,QAAQ,SAAS,CAAC,OAAO;YAC5B,GAAG;YACH,WAAW;YACX,UAAU;YACV;YACA,eAAe,EAAE,cAAc,OAAO,CAAC,MAAM;AACpC,qBAAA,CAAC,eAAe,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;YAAA,CACjD;UACD,EAAA;QAAA,CACH;AAEG,YAAA;AACE,cAAA;AACA,cAAAE;AAEA,cAAA;AAEF,kBAAM,KAAK,YAAY;cACrB,SAAS;cACT,UAAU;cACV,aAAa,MAAM,KAAK,YAAY,OAAO;YAAA,CAC5C;UAAA,SACM,KAAK;AACR,gBAAA,WAAW,GAAG,GAAG;AACN,2BAAA;AACb,mBAAK,eAAe,GAAG;YAAA,WACd,WAAW,GAAG,GAAG;AACf,cAAAA,YAAA;AACN,mBAAA,eAAe,gBAAgB,GAAG;YACzC;UAKF;AAGA,cAAK,gBAAgB,KAAK,YAAY,OAAO,GAAI;AACxC,mBAAA;UACT;AAEA,gBAAM,iBAAiB,gBAAgB;YACrC,CAAC,UAAU,CAAC,eAAe,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;UAAA;AAE1D,gBAAM,kBAAkB,eAAe;YACrC,CAAC,UAAU,CAAC,gBAAgB,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;UAAA;AAE3D,gBAAM,iBAAiB,gBAAgB;YAAO,CAAC,UAC7C,eAAe,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;UAAA;AAKzC,eAAA,QAAQ,MAAM,MAAM;AAClB,iBAAA,QAAQ,SAAS,CAAC,OAAO;cAC5B,GAAG;cACH,WAAW;cACX,SAAS,EAAE;cACX,gBAAgB;cAChB,eAAe;gBACb,GAAG,EAAE;gBACL,GAAG,eAAe,OAAO,CAAC,MAAM,EAAE,WAAW,OAAO;cACtD;cACA,aACE,cAAA,OAAA,SAAA,WAAY,SAAQA,YAChB,MACA,EAAE,QAAQ,KAAK,CAAC,MAAM,EAAE,WAAW,OAAO,IACxC,MACA;YACR,EAAA;AACF,iBAAK,WAAW;UAAA,CACjB;AAIC;YACE,CAAC,gBAAgB,SAAS;YAC1B,CAAC,iBAAiB,SAAS;YAC3B,CAAC,gBAAgB,QAAQ;UAAA,EAE3B,QAAQ,CAAC,CAAC,SAAS,IAAI,MAAM;AACrB,oBAAA,QAAQ,CAAC,UAAU;;AACzB,eAAA,MAAA,KAAA,KAAK,gBAAgB,MAAM,OAAO,EAAG,SAAQ,IAAA,MAA7C,OAAA,SAAA,GAAA,KAAA,IAAqD,KAAA;YAAK,CAC3D;UAAA,CACF;AAED,eAAK,KAAK;YACR,MAAM;YACN,cAAc;YACd,YAAY;YACZ,aAAa;UAAA,CACd;AAEO,kBAAA;iBACD,KAAK;AAEZ,cAAK,gBAAgB,KAAK,YAAY,OAAO,GAAI;AACxC,mBAAA;UACT;AAEQ,kBAAA,IAAI,cAAc,GAAG;AAE7B,iBAAO,GAAG;QACZ;MAAA,CACD;AAED,WAAK,oBAAoB;AAEzB,aAAO,KAAK;IAAA;AAGd,SAAA,iBAAiB,CAAC,QAAqB;AACjC,UAAA,CAAC,IAAI,MAAM;AACb,YAAI,OAAO,KAAK,cAAc,GAAU,EAAE;MAC5C;AACA,UAAI,CAAC,UAAU;AACb,aAAK,SAAS,EAAE,GAAI,KAAa,SAAS,KAAA,CAAM;MAClD;IAAA;AAGF,SAAA,aAAa,MAAM;AAEZ,WAAA,QAAQ,SAAS,CAAC,MAAM;AACpB,eAAA;UACL,GAAG;UACH,eAAe,EAAE,cAAc,OAAO,CAAC,MAAM;AAC3C,kBAAM,QAAQ,KAAK,gBAAgB,EAAE,OAAO;AAExC,gBAAA,CAAC,MAAM,QAAQ,QAAQ;AAClB,qBAAA;YACT;AAIA,kBAAM,UACH,EAAE,UACC,MAAM,QAAQ,iBAAiB,KAAK,QAAQ,uBAC5C,MAAM,QAAQ,UAAU,KAAK,QAAQ,kBACzC,IAAI,KAAK;AAEX,mBAAO,EAAE,WAAW,WAAW,KAAK,IAAA,IAAQ,EAAE,YAAY;UAAA,CAC3D;QAAA;MACH,CACD;IAAA;AAGH,SAAA,eAAe,OAMb,SACyC;;AACrC,UAAA,OAAO,KAAK,cAAc,IAAW;AAEzC,UAAI,UAAU,KAAK,YAAY,KAAK,UAAU,KAAK,QAAQ;QACzD,cAAc;QACd,SAAS;MAAA,CACV;AAED,YAAM,iBAAiB,OAAO;SAC5B,KAAA;UACE,GAAG,KAAK,MAAM;UACd,GAAI,KAAK,MAAM,kBAAkB,CAAC;UAClC,GAAG,KAAK,MAAM;QAAA,MAHhB,OAAA,SAAA,GAIG,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,IAAI,CAAA;MAAC;AAGvB,WAAA,QAAQ,MAAM,MAAM;AACf,gBAAA,QAAQ,CAAC,UAAU;AACzB,cAAI,CAAC,eAAe,MAAM,EAAE,GAAG;AACxB,iBAAA,QAAQ,SAAS,CAAC,OAAO;cAC5B,GAAG;cACH,eAAe,CAAC,GAAI,EAAE,eAAuB,KAAK;YAClD,EAAA;UACJ;QAAA,CACD;MAAA,CACF;AAEG,UAAA;AACQ,kBAAA,MAAM,KAAK,YAAY;UAC/B;UACA,UAAU;UACV,SAAS;UACT,aAAa,MAAM;QAAA,CACpB;AAEM,eAAA;MAAA,SACA,KAAK;AACR,YAAA,WAAW,GAAG,GAAG;AACZ,iBAAA,MAAM,KAAK,aAAa,GAAU;QAC3C;AAEA,gBAAQ,MAAM,GAAG;AACV,eAAA;MACT;IAAA;AAGW,SAAA,aAAA,CAKX,UACA,SACmE;AACnE,YAAM,gBAAgB;QACpB,GAAG;QACH,IAAI,SAAS,KACT,KAAK,oBAAqB,SAAS,QAAQ,IAAe,SAAS,EAAE,IACrE;QACJ,QAAQ,SAAS,UAAU,CAAC;QAC5B,aAAa;MAAA;AAET,YAAA,OAAO,KAAK,cAAc,aAAoB;AAEpD,WAAI,QAAA,OAAA,SAAA,KAAM,YAAW,KAAK,MAAM,WAAW,WAAW;AAC7C,eAAA;MACT;AAEA,YAAM,gBAAe,QAAA,OAAA,SAAA,KAAM,WACvB,KAAK,iBACL,KAAK,MAAM;AAEf,UAAI,CAAC,cAAc;AACV,eAAA;MACT;AACA,YAAM,QAAQ,cAAc,KAAK,UAAU,aAAa,UAAU;QAChE,GAAG;QACH,IAAI,KAAK;MAAA,CACV;AAED,UAAI,CAAC,OAAO;AACH,eAAA;MACT;AACA,UAAI,SAAS,QAAQ;AACnB,YAAI,CAAC,UAAU,OAAO,SAAS,QAAQ,IAAI,GAAG;AACrC,iBAAA;QACT;MACF;AAEI,UAAA,WAAU,QAAA,OAAA,SAAA,KAAM,kBAAiB,OAAO;AAC1C,eAAO,UAAU,aAAa,QAAQ,KAAK,QAAQ,IAAI,IAAI,QAAQ;MACrE;AAEO,aAAA;IAAA;AAGT,SAAA,aAAa,OAAO,SAAoD;AACjE,WAAA,aAAa,KAAK,IAAI;IAAA;AAK7B,SAAA,yBAAA,oBAA6B,IAAA;AAC7B,SAAA,sBAAA,oBAA0B,QAAA;AAE1B,SAAA,cAAc,CAAC,QAAgB;AAC7B,YAAM,QAAQ,KAAK,uBAAuB,IAAI,GAAG;AAEjD,UAAI,CAAC,OAAO;AACH,eAAA;MACT;AAEO,aAAA,KAAK,oBAAoB,IAAI,KAAK;IAAA;AAM3B,SAAA,gBAAA,CAAI,KAAU,YAAwC;AACpE;QACE;QACA;MAAA;AAGE,UAAA,OAAO,aAAa,aAAa;AACnC,cAAM,SAAS,OAAO,QAAQ,WAAW,MAAM,KAAK,UAAU,GAAG;AAEjE,aAAK,WAAW,YAAY;AACpB,gBAAA,KAAK,qBAAqB,MAAM;AACtC,gBAAM,OACJ,OAAO,YAAY,aAAa,MAAO,QAAoB,IAAA;AAC7D,iBAAO,eAAe,EAAE;8BACF;YAC1B;UAAA,CACD,QAAQ,KAAK,UAAU,KAAK,QAAQ,YAAY,UAAU,IAAI,CAAC,CAAC;;QAAA,CAE5D;AAEM,eAAA,MAAM,KAAK,YAAe,GAAG;MACtC;AAEA,aAAO,MAAM;IAAA;AAMf,SAAA,cAAc,CAA0B,QAAa;AACnD;QACE;QACA;MAAA;AAGE,UAAA,OAAO,aAAa,aAAa;AACnC,cAAM,SAAS,OAAO,QAAQ,WAAW,MAAM,KAAK,UAAU,GAAG;AAE1D,eAAA,KAAK,QAAQ,YAAY;UAC9B,OAAO,qBAAqB,MAAM,EAAS;QAAA;MAE/C;AAEO,aAAA;IAAA;AAGT,SAAA,YAAY,MAAwB;;AAClC,YAAM,cACJ,KAAA,KAAK,QAAQ,oBAAb,OAAA,SAAA,GAA8B,cAAa;AAEtC,aAAA;QACL,OAAO;UACL,mBAAmB,KAAK,MAAM,QAAQ,IAAI,CAAC,OAAO;YAChD,GAAG,KAAK,GAAG,CAAC,MAAM,UAAU,aAAa,YAAY,CAAC;;;YAGtD,OAAO,EAAE,QACL;cACE,MAAM,UAAU,EAAE,KAAK;cACvB,iBAAiB;YAEnB,IAAA;UAAA,EACJ;QACJ;MAAA;IACF;AAGF,SAAA,UAAU,OAAO,4BAAqC;;AACpD,UAAI,OAAO;AAEP,UAAA,OAAO,aAAa,aAAa;AACnC,gBAAO,KAAA,OAAO,uBAAP,OAAA,SAAA,GAA2B;MACpC;AAEA;QACE;QACA;MAAA;AAGF,YAAM,MAAM,KAAK,QAAQ,YAAY,MAAM,IAAI;AAC/C,WAAK,iBAAiB,IAAI;AACrB,OAAA,MAAA,KAAA,KAAA,SAAQ,YAAR,OAAA,SAAA,GAAA,KAAA,IAAkB,IAAI,OAAA;AACrB,YAAA,kBAAkB,IAAI,OAAO;AAEnC,UAAI,UAAU,KAAK;QACjB,KAAK,MAAM,SAAS;QACpB,KAAK,MAAM,SAAS;MAAA,EACpB,IAAI,CAAC,UAAU;;AACT,cAAA,kBAAkB,gBAAgB,kBAAkB;UACxD,CAAC,MAAM,EAAE,OAAO,MAAM;QAAA;AAGxB;UACE;UACA,oEAAoE,MAAM,EAAE;QAAA;AAG9E,YAAI,iBAAiB;AACnB,gBAAM,QAAQ,KAAK,gBAAgB,MAAM,OAAO;AAEzC,iBAAA;YACL,GAAG;YACH,GAAG;YACH,OAAMD,OAAAD,MAAA,MAAM,SAAQ,SAAd,OAAA,SAAAC,IAAA,KAAAD,KAAqB;cACzB,QAAQ,MAAM;cACd,YAAY,gBAAgB;YAAA,CAAA;YAE9B,QAAO,MAAAG,MAAA,MAAM,SAAQ,UAAd,OAAA,SAAA,GAAA,KAAAA,GAAAA;YACP,UAAS,MAAA,KAAA,MAAM,SAAQ,YAAd,OAAA,SAAA,GAAA,KAAA,EAAA;UAAwB;QAErC;AACO,eAAA;MAAA,CACR;AAEI,WAAA,QAAQ,SAAS,CAAC,MAAM;AACpB,eAAA;UACL,GAAG;UACH;UACA,aAAa,KAAK,IAAI;QAAA;MACxB,CACD;IAAA;AAGc,SAAA,iBAAA,CAAC,SAA0B,QAAuB;AACjE,YAAM,mBAAmB,OAAO;QAC9B,QAAQ,IAAI,CAACC,WAAU,CAACA,OAAM,SAASA,MAAK,CAAC;MAAA;AAI/C,UAAI,eACD,IAAI,SACD,KAAK,gBAAgB,WAAW,IAChC,KAAK,gBAAgB,IAAI,OAAO,MACpC,KAAK,gBAAgB,WAAW;AAIhC,aAAA,CAAC,YAAY,QAAQ,qBACrB,CAAC,KAAK,QAAQ,4BACd,YAAY,OAAO,aACnB;AACA,sBAAc,eAAA,OAAA,SAAA,YAAa;AAE3B;UACE;UACA;QAAA;MAEJ;AAEI,UAAA,QAAQ,iBAAiB,YAAY,EAAE;AAEjC,gBAAA,OAAO,qCAAqC,YAAY,EAAE;AAGpE,aAAO,OAAO,OAAO;QACnB,QAAQ;QACR,OAAO;QACP,YAAY;MAAA,CACI;IAAA;AAGpB,SAAA,mBAAmB,MAAM;AAChB,aAAA,KAAK,QAAQ,MAAM,QAAQ;QAChC,CAAC,MAAM,EAAE,WAAW,cAAc,EAAE;MAAA;IACtC;AA3qDA,SAAK,OAAO;MACV,qBAAqB;MACrB,kBAAkB;MAClB,qBAAqB;MACrB,SAAS;MACT,GAAG;MACH,kBAAiB,WAAA,OAAA,SAAA,QAAS,oBAAmB;MAC7C,cAAa,WAAA,OAAA,SAAA,QAAS,gBAAe;MACrC,cAAa,WAAA,OAAA,SAAA,QAAS,gBAAe;IAAA,CACtC;AAEG,QAAA,OAAO,aAAa,aAAa;AACjC,aAAe,kBAAkB;IACrC;EACF;EA8EA,IAAI,QAAQ;AACV,WAAO,KAAK,QAAQ;EACtB;EA+LA,IAAI,kBAAkB;AACpB,WAAO,KAAK;EACd;;;;;;AAo5CF;AAKgB,SAAA,OAGd,IAAsB,KAAY;AAClC,SAAO,UACF,SACuC;AACpC,UAAA,WAAW,MAAM,GAAA;AACvB,WAAO,SAAS,OAAO,SAAS,EAAE,GAAG,IAAI;EAAA;AAE7C;AAEO,IAAM,mBAAN,cAA+B,MAAM;AAAC;AAEtC,IAAM,iBAAN,cAA6B,MAAM;AAAC;AAEpC,SAAS,sBACd,UACkB;AACX,SAAA;IACL,WAAW;IACX,iBAAiB;IACjB,QAAQ;IACR,kBAAkB,EAAE,GAAG,SAAS;IAChC;IACA,SAAS,CAAC;IACV,gBAAgB,CAAC;IACjB,eAAe,CAAC;IAChB,aAAa;IACb,YAAY;EAAA;AAEhB;AAEO,SAAS,sBAAsB,KAAc;AAClD,MAAI,eAAe;AACV,WAAA;MACL,MAAM,IAAI;MACV,SAAS,IAAI;IAAA;AAGV,SAAA;IACL,MAAM;EAAA;AAEV;;;ACp/DgB,SAAA,MACd,UACA,SAGA;AACA,QAAM,UAAU;AAEZ,MAAA,CAAC,QAAQ,iBAAiB;AAC5B,YAAQ,kBAAkB;MACxB,KAAK,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;MACvC,QAAQ;IAAA;AAGV,UAAM,QAAQ,QAAQ;AAGnB,YAAA,KAAK,CAAC,SAAS;AACd,YAAM,SAAS;AACf,YAAM,OAAO;IAAA,CACd,EACA,MAAM,CAAC,UAAU;AAChB,YAAM,SAAS;AACf,YAAM,QAAQ;QACZ,QAAO,WAAA,OAAA,SAAA,QAAS,mBAAkB,uBAAuB,KAAK;QAC9D,iBAAiB;MAAA;IACnB,CACD;EACL;AAEO,SAAA;AACT;AAEO,SAAS,qBAAqB,KAAmB;AAEpD,SAAA,OAAO,QAAQ,YACf,QAAQ,QACR,EAAE,eAAe,YACjB,CAAC,IAAI,QACL,qBAAqB;AAEzB;;;ACzDgB,SAAA,WAAc,EAAE,QAAA,GAAiC;;AAC/D,QAAM,SAAS,UAAA;AAGf,QAAM,QAAQ,QAAQ;AAKtB,MAAI,qBAAqB,OAAO,KAAK,MAAM,WAAW,WAAW;AAC/D,UAAM,eAAgB,OAAe,oBAAoB,MAAM,GAAG,EAAE;AAEpE,QAAI,cAAc;AACT,aAAA,OAAO,OAAO,YAAY;IAAA,OAC5B;AACL,UAAI,QAAQ,OAAO,uBAAuB,IAAI,MAAM,GAAG;AAIvD,UAAI,CAAC,OAAO;AACV,gBAAQ,CAAA;AACR,eAAO,uBAAuB,IAAI,MAAM,KAAK,KAAK;AAC3C,eAAA,oBAAoB,IAAI,OAAO,KAAK;AAE3C,eAAO,OAAO,OAAO;UACnB,SAAS,MAAM;;AACb,aAAAC,MAAA,MAAM,qBAAN,OAAA,SAAAA,IAAA,KAAA,KAAA;UAEF;UACA,SAAS,IAAI,QAAQ,CAAC,MAAM;AAC1B,kBAAM,mBAAmB;UAAA,CAC1B;UACD,kBAAkB,MAAM;UAAC;QAAA,CAC1B;MACH;IACF;EACF;AAMI,MAAA,MAAM,WAAW,WAAW;AAC9B,UAAM,qBAAqB,OAAO,IAAI,MAAM,UAAU;EACxD;AAII,MAAA,CAAC,qBAAqB,OAAO,GAAG;AAC3B,WAAA,WAAW,6DAA6D,MAAM,GAAG,MAAM,OAAO,QAAQ,YAAY,UAAU,KAAK,CAAC;;;yDAGpF,MAAM,GAAG;6DACL,MAAM,GAAG;;;WAG5D;EACR;AAEI,MAAA,MAAM,WAAW,SAAS;AACxB,QAAA,OAAO,aAAa,aAAa;AAC/B,UAAA,kBAAkB,MAAM,KAAK,GAAG;AAClC,iBACE,KAAA,OAAO,QAAQ,oBAAf,OAAA,SAAA,GAAgC,gBAAe,yBAC/C,MAAM,MAAM,IAAW;MAAA,OACpB;AACL;UACE;UACA;QAAA;AAEF,cAAM,MAAM;MACd;IAAA,OACK;AACC,YAAA;QACJ,SACE,KAAA,OAAO,QAAQ,oBAAf,OAAA,SAAA,GAAgC,cAAa,uBAC7C,MAAM,KAAK;QACb,iBAAiB;MAAA;IAErB;EACF;AAEO,SAAA,CAAC,QAAQ,gBAAgB,IAAW;AAC7C;AAEO,SAAS,MACd,OAIA;AACA,QAAM,YAAQ,yBAAC,YAAY,EAAA,GAAG,MAAO,CAAA;AACrC,MAAI,MAAM,UAAU;AAClB,eAAA,yBAAc,iBAAN,EAAe,UAAU,MAAM,UAAW,UAAM,MAAA,CAAA;EAC1D;AACO,SAAA;AACT;AAEA,SAAS,WACP,OAIA;AACM,QAAA,UAAU,WAAW,KAAK;AACzB,SAAA,MAAM,SAAS,GAAG,OAAO;AAClC;;;ACdO,SAAS,gBAcd,MAAiB;AACV,SAAA,IAAI,UAA0D,MAAM;IACzE,QAAQ;EACT,CAAA,EAAE;AACL;AAMO,IAAM,YAAN,MAYL;EAGA,YACS,MACP,OACA;AAFO,SAAA,OAAA;AAMT,SAAA,cAAc,CA+CZ,YA6CG;AACH;QACE,KAAK;QACL;MAAA;AAEI,YAAA,QAAQ,YAAY,OAAc;AACtC,YAAc,SAAS;AAClB,aAAA;IAAA;AAtGP,SAAK,SAAS,SAAA,OAAA,SAAA,MAAO;EACvB;AAuGF;AAOO,SAAS,gBAId,OAeA;AACA;IACE;IACA;EAAA;AAEF,SAAO,CAAC,aAAa;AACvB;AAOO,IAAM,YAAN,MAAyC;EAK9C,YACE,MAGA;AAKF,SAAA,WAAW,CAMTC,UAEe;AACR,aAAA,SAAS,EAAE,QAAQA,SAAA,OAAA,SAAAA,MAAM,QAAQ,MAAM,KAAK,QAAQ,GAAA,CAAI;IAAA;AAGjE,SAAA,kBAAkB,CAA4CA,UAE7C;AACf,aAAO,SAAS;QACd,MAAM,KAAK,QAAQ;QACnB,QAAQ,CAAC,OAAYA,SAAA,OAAA,SAAAA,MAAM,UAASA,MAAK,OAAO,EAAE,OAAO,IAAI,EAAE;MAAA,CAChE;IAAA;AAGH,SAAA,YAAY,CAAkDA,UAE7C;AACR,aAAA,UAAU,EAAE,GAAGA,OAAM,MAAM,KAAK,QAAQ,GAAA,CAAI;IAAA;AAGrD,SAAA,YAAY,CAA2CA,UAEtC;AACR,aAAA,UAAU,EAAE,GAAGA,OAAM,MAAM,KAAK,QAAQ,GAAA,CAAI;IAAA;AAGrD,SAAA,gBAAgB,CAA4CA,UAE3C;AACR,aAAA,cAAc,EAAE,GAAGA,OAAM,MAAM,KAAK,QAAQ,GAAA,CAAW;IAAA;AAGhE,SAAA,gBAAgB,CAA4CA,UAE3C;AACR,aAAA,cAAc,EAAE,GAAGA,OAAM,MAAM,KAAK,QAAQ,GAAA,CAAW;IAAA;AA9C9D,SAAK,UAAU;AACb,SAAa,WAAW,OAAO,IAAI,YAAY;EACnD;AA8CF;AAEO,SAAS,gBAGd,IAAS;AACT,SAAO,CAAC,SAA2B;AACjC,WAAO,IAAI,UAAkB,EAAE,IAAe,GAAG,KAAM,CAAA;EAAA;AAE3D;AAEO,SAAS,oBAGd,MAAiB;AACX,QAAA,KAAK,aAAa,IAAI;AACrB,SAAA,CAAC,SAA2B,IAAI,UAAkB,EAAE,IAAI,GAAG,KAAA,CAAM;AAC1E;AAEA,IAAM,yBAAyB;AAE/B,SAAS,aAAa,GAAW;AAC/B,SAAO,EAAE,WAAW,wBAAwB,EAAE,EAAE,WAAW,MAAM,GAAG;AACtE;;;;AC3WA,SAAS,sBAAsB,OAAqB;AAClD,SACE,QAAO,SAAA,OAAA,SAAA,MAAO,aAAY,YAC1B,8CAA8C,KAAK,MAAM,OAAO;AAEpE;AAEgB,SAAA,mBAId,UACA,YAGQ;AACJ,MAAA;AAIJ,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC,aAAa;AAChB,oBAAc,SAAS,EAAE,MAAM,CAAC,UAAU;AACpC,YAAA,sBAAsB,KAAK,GAAG;AAMhC,sBAAY,sBAAsB;AAE3B,iBAAA;QACT;AACM,cAAA;MAAA,CACP;IACH;AAEO,WAAA;EAAA;AAGH,QAAA,WAAiB,YAAK,YAAY;AAClC,QAAA;AACF,YAAM,UAAU,KAAA;AAIhB,UAAI,QAAQ,qBAAqB;AAC/B,cAAM,QAAQ;MAChB;AACA,YAAM,gBAAgB,MAAM;AAEtB,YAAA,OAAO,cAAc,cAAc,SAAS;AAC3C,aAAA;QACL,SAAS;MAAA;IAAA,SAEJ,OAAO;AAEZ,UAAA,iBAAiB,SACjB,sBAAsB,KAAK,KAC3B,OAAO,WAAW,eAClB,OAAO,mBAAmB,aAC1B;AAKM,cAAA,aAAa,0BAA0B,MAAM,OAAO;AAC1D,YAAI,CAAC,eAAe,QAAQ,UAAU,GAAG;AACxB,yBAAA,QAAQ,YAAY,GAAG;AACtC,iBAAO,SAAS,OAAA;AAGT,iBAAA;YACL,SAAS,MAAM;UAAA;QAEnB;MACF;AACM,YAAA;IACR;EAAA,CACD;AAEC,WAAiB,UAAU;AAEtB,SAAA;AACT;;;;;ACmQA,IAAM,iBAAiB;AAEhB,SAAS,aAOd,SAC+C;AAC/C,QAAM,SAAS,UAAA;AACf,QAAMC,iBAAgB,SAAS;IAC7B,QAAQ;IACR,QAAQ,CAAC,MAAM,EAAE;EAAA,CAClB;AAEK,QAAA;;IAEJ,cAAc,OAAO,EAAE,WAAW,SAAA;IAClC,gBAAgB,OAAO,CAAA;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAAS;IACT,cAAc;IACd;IACA,iBAAAC;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,GAAG;EACD,IAAA;AAQJ,QAAM,OAAO;IACX,MAAM,QAAQ,KAAKD,iBAAgB;IACnC,GAAG;EAAA;AAGL,MAAI,OAAgC;AAEhC,MAAA;AACE,QAAA,IAAI,GAAG,EAAE,EAAE;AACR,WAAA;EAAA,QACD;EAAC;AAEH,QAAA,OAAO,OAAO,cAAc,IAAW;AACvC,QAAA,UAAU,eAAe,OAAO,QAAQ;AAC9C,QAAM,eACJ,oBAAoB,OAAO,QAAQ,uBAAuB;AAE5D,QAAM,WAAW,eAAe;IAC9B,QAAQ,CAAC,MAAM;AAEb,YAAM,mBAAmB,EAAE,SAAS,SAAS,MAAM,GAAG;AACtD,YAAM,gBAAgB,KAAK,SAAS,MAAM,GAAG;AAC7C,YAAM,mBAAmB,cAAc;QACrC,CAAC,GAAG,MAAM,MAAM,iBAAiB,CAAC;MAAA;AAGpC,YAAM,YAAW,iBAAA,OAAA,SAAA,cAAe,SAC5B,EAAE,SAAS,aAAa,KAAK,WAC7B;AACJ,YAAM,YAAW,iBAAA,OAAA,SAAA,cAAe,eAC5B,EAAE,SAAS,SAAS,KAAK,OACzB;AACJ,YAAM,cACJ,iBAAA,OAAA,SAAA,cAAe,kBAAiB,OAC5B,UAAU,EAAE,SAAS,QAAQ,KAAK,QAAQ,EAAC,iBAAA,OAAA,SAAA,cAAe,MAAK,IAC/D;AAGN,aAAO,YAAY,YAAY;IACjC;EAAA,CACD;AAED,MAAI,SAAS,YAAY;AAChB,WAAA;MACL,GAAG;MACH;MACA,MAAM;MACN;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA;EAEJ;AAGM,QAAA,cAAc,CAAC,MAAkB;AACrC,QACE,CAAC,YACD,CAAC,YAAY,CAAC,KACd,CAAC,EAAE,qBACF,CAAC,UAAU,WAAW,YACvB,EAAE,WAAW,GACb;AACA,QAAE,eAAe;AAGjB,aAAO,eAAe,EAAE,GAAG,MAAM,SAAS,aAAa,iBAAAC,iBAAA,CAAiB;IAC1E;EAAA;AAGF,QAAM,YAAY,MAAM;AACtB,IAAM,uBAAgB,MAAM;AAC1B,aAAO,aAAa,IAAW,EAAE,MAAM,CAAC,QAAQ;AAC9C,gBAAQ,KAAK,GAAG;AAChB,gBAAQ,KAAK,cAAc;MAAA,CAC5B;IAAA,CACF;EAAA;AAIG,QAAA,cAAc,CAAC,MAAkB;AACjC,QAAA;AAAU;AACd,QAAI,SAAS;AACD,gBAAA;IACZ;EAAA;AAGF,QAAM,mBAAmB;AAEnB,QAAA,cAAc,CAAC,MAAkB;AACjC,QAAA;AAAU;AACRC,UAAAA,UAAU,EAAE,UAAU,CAAA;AAE5B,QAAI,SAAS;AACX,UAAIA,QAAO,gBAAgB;AACzB;MACF;AAEAA,cAAO,iBAAiB,WAAW,MAAM;AACvCA,gBAAO,iBAAiB;AACd,kBAAA;SACT,YAAY;IACjB;EAAA;AAGI,QAAA,cAAc,CAAC,MAAkB;AACjC,QAAA;AAAU;AACRA,UAAAA,UAAU,EAAE,UAAU,CAAA;AAE5B,QAAIA,QAAO,gBAAgB;AACzB,mBAAaA,QAAO,cAAc;AAClCA,cAAO,iBAAiB;IAC1B;EAAA;AAGF,QAAM,kBACJ,CAAC,aACD,CAAC,MAA4B;AAC3B,QAAI,EAAE;AAAS,QAAE,QAAQ;AACzB,aAAS,OAAO,OAAO,EAAE,QAAQ,CAAC,YAAY;AAC5C,UAAI,EAAE;AAAkB;AACxB,cAAS,CAAC;IAAA,CACX;EAAA;AAIC,QAAA,sBAA+D,WACjE,iBAAiB,aAAoB,CAAA,CAAE,KAAK,CAAC,IAC7C,CAAA;AAGE,QAAA,wBACJ,WAAW,CAAC,IAAI,iBAAiB,eAAe,CAAA,CAAE,KAAK,CAAA;AAElD,SAAA;IACL,GAAG;IACH,GAAG;IACH,GAAG;IACH,MAAM,WACF,SACA,KAAK,iBACH,KAAK,eAAe,OACpB,KAAK;IACX,SAAS,gBAAgB,CAAC,SAAS,WAAW,CAAC;IAC/C,SAAS,gBAAgB,CAAC,SAAS,WAAW,CAAC;IAC/C,cAAc,gBAAgB,CAAC,cAAc,WAAW,CAAC;IACzD,cAAc,gBAAgB,CAAC,cAAc,WAAW,CAAC;IACzD,cAAc,gBAAgB,CAAC,cAAc,gBAAgB,CAAC;IAC9D;IACA,OAAO;MACL,GAAG;MACH,GAAG,oBAAoB;MACvB,GAAG,sBAAsB;IAC3B;IACA,WACE;MACE;MACA,oBAAoB;MACpB,sBAAsB;IAAA,EAErB,OAAO,OAAO,EACd,KAAK,GAAG,KAAK;IAClB,GAAI,WACA;MACE,MAAM;MACN,iBAAiB;IAEnB,IAAA;IACJ,CAAC,aAAa,GAAG,WAAW,WAAW;EAAA;AAE3C;AAgBO,IAAM,OAA4B,kBAAW,CAAC,OAAY,QAAQ;AACvE,QAAM,EAAE,MAAM,GAAG,UAAU,IAAI,aAAa,KAAK;AAEjD,QAAM,WACJ,OAAO,MAAM,aAAa,aACtB,MAAM,SAAS;IACb,UAAW,UAAkB,aAAa,MAAM;EAAA,CACjD,IACD,MAAM;AAEZ,aAAQ,yBAAA,KAAA,EAAG,GAAG,WAAW,KAAU,SAAoB,CAAA;AACzD,CAAC;AAED,SAAS,YAAY,GAAe;AAC3B,SAAA,CAAC,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;AACpD;;;;ACjmBA,IAAMC,mBACJ,OAAO,WAAW,cAAoB,0BAAwB;AAMhE,IAAM,YAAY;AAClB,IAAM,YAAY;AAElB,IAAI,uBAAA,oBAA2B,QAAA;AAa/B,IAAM,kBAAkB,OAAO,WAAW,eAAe,OAAO;AAEhE,IAAI,QAAe,mBACd,MAAM;AACL,QAAM,aAAa;AAEnB,QAAM,QAAoB,KAAK;IAC7B,OAAO,eAAe,QAAQ,UAAU,KAAK;EAAA,KAC1C,EAAE,QAAQ,CAAA,GAAI,MAAM,CAAG,EAAA;AAErB,SAAA;IACL;IACA,KAAK,CAAC,YAAY;AAChB,YAAM,QAAQ,iBAAiB,SAAS,MAAM,KAAK;AACnD,aAAO,eAAe,QAAQ,YAAY,KAAK,UAAU,MAAM,KAAK,CAAC;IACvE;EAAA;AAEJ,GACC,IAAA;AAML,IAAM,gBAAgB,CAAC,aAA6B,SAAS,MAAM;AAE5D,SAAS,qBAAqB,SAAoC;AACvE,QAAM,SAAS,UAAA;AAEf,EAAAA,iBAAgB,MAAM;AACd,UAAA,UAAS,WAAA,OAAA,SAAA,QAAS,WAAU;AAE5B,UAAA,EAAE,QAAY,IAAA;AACpB,QAAI,QAAQ,mBAAmB;AAC7B,cAAQ,oBAAoB;IAC9B;AAEM,UAAA,WAAW,CAAC,UAAiB;AAC7B,UAAA,qBAAqB,IAAI,MAAM,MAAM;AAAG;AACvB,2BAAA,IAAI,MAAM,MAAM;AAErC,UAAI,kBAAkB;AAEtB,UAAI,MAAM,WAAW,YAAY,MAAM,WAAW,QAAQ;AACtC,0BAAA;MAAA,OACb;AACC,cAAA,SAAU,MAAM,OAAmB;UACvC;QAAA;AAGF,YAAI,QAAQ;AACV,4BAAkB,gCAAgC,MAAM;QAAA,OACnD;AACa,4BAAA,eAAe,MAAM,MAAM;QAC/C;MACF;AAEA,UAAI,CAAC,MAAM,MAAM,KAAK,eAAe,GAAG;AAChC,cAAA,IAAI,CAAC,OAAO;UAChB,GAAG;UACH,MAAM;YACJ,GAAG,EAAE;YACL,CAAC,eAAe,GAAG;cACjB,SAAS;cACT,SAAS;YACX;UACF;QACA,EAAA;MACJ;IAAA;AAGE,QAAA,OAAO,aAAa,aAAa;AAC1B,eAAA,iBAAiB,UAAU,UAAU,IAAI;IACpD;AAEA,UAAM,oBAAoB,OAAO,UAAU,gBAAgB,CAAC,UAAU;AACpE,UAAI,MAAM,aAAa;AACf,cAAA,aAAa,OAAO,MAAM,YAAY;AACjC,mBAAA,mBAAmB,MAAM,MAAM,MAAM;AAC9C,gBAAM,QAAQ,MAAM,MAAM,KAAK,eAAe;AAC9C,cAAI,oBAAoB,WAAW;AAC3B,kBAAA,UAAU,OAAO,WAAW;AAC5B,kBAAA,UAAU,OAAO,WAAW;UAAA,WACzB,iBAAiB;AACpB,kBAAA,UAAU,SAAS,cAAc,eAAe;AAChD,kBAAA,WAAU,WAAA,OAAA,SAAA,QAAS,eAAc;AACjC,kBAAA,WAAU,WAAA,OAAA,SAAA,QAAS,cAAa;UACxC;AAEM,gBAAA,IAAI,CAAC,MAAM;AACf,kBAAM,OAAO,EAAE,GAAG,EAAE,KAAK;AACzB,mBAAO,KAAK,eAAe;AAEpB,mBAAA;cACL,GAAG;cACH;cACA,QAAQ;gBACN,GAAG,EAAE;gBACL,CAAC,CAAC,YAAY,eAAe,EAAE,KAAK,SAAS,CAAC,GAAG;cACnD;YAAA;UACF,CACD;QACH;MACF;IAAA,CACD;AAED,UAAM,kBAAkB,OAAO,UAAU,cAAc,CAAC,UAAU;AAChE,UAAI,MAAM,aAAa;AACjB,YAAA,CAAC,OAAO,iBAAiB;AAC3B;QACF;AAEA,eAAO,kBAAkB;AAEnBC,cAAAA,WAAS,WAAA,OAAA,SAAA,QAAS,WAAU;AAE5B,cAAA,aAAaA,QAAO,MAAM,UAAU;AAC1C,YAAI,iBAAiB;AAEV,mBAAA,YAAY,MAAM,MAAM,QAAQ;AACzC,gBAAM,QAAQ,MAAM,MAAM,OAAO,QAAQ;AACzC,gBAAM,CAAC,KAAK,eAAe,IAAI,SAAS,MAAM,SAAS;AACvD,cAAI,QAAQ,YAAY;AACtB,gBAAI,oBAAoB,WAAW;AAChB,+BAAA;AACjB,qBAAO,SAAS,MAAM,SAAS,MAAM,OAAO;YAAA,WACnC,iBAAiB;AACpB,oBAAA,UAAU,SAAS,cAAc,eAAe;AACtD,kBAAI,SAAS;AACX,wBAAQ,aAAa,MAAM;AAC3B,wBAAQ,YAAY,MAAM;cAC5B;YACF;UACF;QACF;AAEA,YAAI,CAAC,gBAAgB;AACZ,iBAAA,SAAS,GAAG,CAAC;QACtB;AAEM,cAAA,IAAI,CAAC,OAAO,EAAE,GAAG,GAAG,MAAM,CAAA,EAAK,EAAA;AACrC,+BAAA,oBAA2B,QAAA;MAC7B;IAAA,CACD;AAED,WAAO,MAAM;AACF,eAAA,oBAAoB,UAAU,QAAQ;AAC7B,wBAAA;AACF,sBAAA;IAAA;EAEpB,GAAG,CAAE,CAAA;AACP;AAEO,SAAS,kBAAkB,OAAiC;AACjE,uBAAqB,KAAK;AACnB,SAAA;AACT;AAEO,SAAS,4BACd,SAYA;;AACA,QAAM,SAAS,UAAA;AACT,QAAA,UAAS,WAAA,OAAA,SAAA,QAAS,WAAU;AAElC,MAAI,kBAAkB;AAEtB,MAAI,QAAQ,IAAI;AACI,sBAAA,gCAAgC,QAAQ,EAAE;EAAA,OACvD;AACC,UAAA,WAAU,KAAA,QAAQ,eAAR,OAAA,SAAA,GAAA,KAAA,OAAA;AAChB,QAAI,CAAC,SAAS;AACZ;IACF;AACA,sBAAkB,eAAe,OAAO;EAC1C;AAEM,QAAA,aAAa,OAAO,OAAO,cAAc;AAC/C,QAAM,WAAW,CAAC,YAAY,eAAe,EAAE,KAAK,SAAS;AACtD,SAAA,MAAM,MAAM,OAAO,QAAQ;AACpC;AAEA,SAAS,eAAe,IAAiB;AACnC,MAAA,OAAO,CACT,GAAA;AACM,SAAA,SAAS,GAAG,YAAa;AAC1B,SAAA;MACH,GAAG,GAAG,OAAO,cACV,CAAC,EAAE,QAAgB,KAAK,OAAO,UAAU,EAAE,IAAI,CAClD;IAAA;AAEG,SAAA;EACP;AACA,SAAO,GAAG,KAAK,KAAK,KAAK,CAAC,GAAG,YAAA;AAC/B;;;;AChOgB,SAAA,WACd,WACA,YAA2B,MACrB;AACA,QAAA,EAAE,QAAA,IAAY,UAAA;AAEpB,EAAM,kBAAU,MAAM;AACpB,QAAI,CAAC;AAAW;AACT,WAAA,QAAQ,MAAM,SAAS;EAAA,CAC/B;AACH;AAEO,SAAS,MAAM,EAAE,SAAS,WAAW,SAAA,GAAyB;AACnE,aAAW,SAAS,SAAS;AAC7B,SAAQ,YAAY;AACtB;;;;ACDO,SAAS,YAEd,cAEC;AACK,QAAA,EAAE,SAAA,IAAa,UAAA;AAErB,QAAMC,iBAAgB,SAAS;IAC7B,QAAQ;IACR,QAAQ,CAAC,MAAM,EAAE;EAAA,CAClB;AAED,QAAM,SAA0C,CAAC,EAAE,MAAM,GAAG,KAAA,MAAW;AACrE,WAAO,SAAS;MACd,OAAM,QAAA,OAAA,SAAA,KAAM,MAAKA,iBAAgB;MACjC,GAAI;IAAA,CACL;EAAA;AAGH,SAAa,oBAAY,QAAQ,CAAE,CAAA;AACrC;AAiBO,SAAS,SAMd,OAA0E;AACpE,QAAA,EAAE,SAAA,IAAa,UAAA;AACrB,QAAM,QAAQ,SAAS,EAAE,QAAQ,MAAO,CAAA;AAExC,EAAM,kBAAU,MAAM;AACX,aAAA;MACP,MAAM,MAAM,KAAK,MAAM,WAAW;MAClC,GAAG;IAAA,CACG;EACV,GAAG,CAAE,CAAA;AAEE,SAAA;AACT;;;ACpEO,SAAS,gBAMd,MAGW;AACX,SAAO,SAAS;IACd,GAAI;IACJ,QAAQ,CAAC,WACP,QAAA,OAAA,SAAA,KAAM,UACF,KAAK,OAAO,MAAM,OAAwB,IAC1C,MAAM;EAAA,CACb;AACH;;;ACrBO,IAAM,2BAA2B;AACjC,IAAM,4BAA4B;AAyDzB,SAAA,eAId,QACA,IAC8B;AAE9B,QAAM,aAAa;AAEnB;IACE,WAAW;IACX;EAAA;AAGF,SAAO,OAAO;IACZ,OAAO,SAAmB,SAA0B;AAClD,aAAO,WAAW;QAChB;QACA,SAAS,WAAW;QACpB,aAAa,QAAA,OAAA,SAAA,KAAM;MAAA,CACpB;IACH;IACA;MACE,KAAK,GAAG;IACV;EAAA;AAEJ;AAEgB,SAAA,KACd,SACA,MAKqB;AACrB,SAAO,IAAI,SAAS,KAAK,UAAU,OAAO,GAAG;IAC3C,SAAQ,QAAA,OAAA,SAAA,KAAM,WAAU;IACxB,aAAY,QAAA,OAAA,SAAA,KAAM,gBAAc,QAAA,OAAA,SAAA,KAAM,YAAW,MAAM,OAAO;IAC9D,SAAS;MACP,gBAAgB;MAChB,CAAC,wBAAwB,GAAG;MAC5B,GAAG,QAAA,OAAA,SAAA,KAAM;IACX;EAAA,CACD;AACH;", "names": ["React", "useState", "useEffect", "useLayoutEffect", "error", "React", "useRef", "useEffect", "useSyncExternalStoreWithSelector", "isProduction", "shallow", "useLayoutEffect", "_a", "Outlet", "options", "lazyFn", "useTransition", "routerContext", "useLayoutEffect", "d", "searchError", "startTransition", "_a", "_b", "notFound", "_c", "match", "_a", "opts", "matchPathname", "startTransition", "target", "useLayoutEffect", "<PERSON><PERSON><PERSON>", "matchPathname"]}