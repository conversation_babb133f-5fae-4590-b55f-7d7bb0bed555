{"version": 3, "sources": ["../../@tanstack/router-devtools/src/useLocalStorage.ts", "../../@tanstack/router-devtools/src/utils.ts", "../../goober/dist/goober.modern.js", "../../@tanstack/router-devtools/src/tokens.ts", "../../@tanstack/router-devtools/src/Explorer.tsx", "../../@tanstack/router-devtools/src/logo.tsx", "../../@tanstack/router-devtools/src/devtools.tsx"], "sourcesContent": ["import React from 'react'\n\nconst getItem = (key: string): unknown => {\n  try {\n    const itemValue = localStorage.getItem(key)\n    if (typeof itemValue === 'string') {\n      return JSON.parse(itemValue)\n    }\n    return undefined\n  } catch {\n    return undefined\n  }\n}\n\nexport default function useLocalStorage<T>(\n  key: string,\n  defaultValue: T | undefined,\n): [T | undefined, (newVal: T | ((prevVal: T) => T)) => void] {\n  const [value, setValue] = React.useState<T>()\n\n  React.useEffect(() => {\n    const initialValue = getItem(key) as T | undefined\n\n    if (typeof initialValue === 'undefined' || initialValue === null) {\n      setValue(\n        typeof defaultValue === 'function' ? defaultValue() : defaultValue,\n      )\n    } else {\n      setValue(initialValue)\n    }\n  }, [defaultValue, key])\n\n  const setter = React.useCallback(\n    (updater: any) => {\n      setValue((old) => {\n        let newVal = updater\n\n        if (typeof updater == 'function') {\n          newVal = updater(old)\n        }\n        try {\n          localStorage.setItem(key, JSON.stringify(newVal))\n        } catch {}\n\n        return newVal\n      })\n    },\n    [key],\n  )\n\n  return [value, setter]\n}\n", "import React from 'react'\nimport { AnyRootRoute, AnyRoute, AnyRouteMatch } from '@tanstack/react-router'\n\nimport { Theme, useTheme } from './theme'\nimport useMediaQuery from './useMediaQuery'\n\nexport const isServer = typeof window === 'undefined'\n\ntype StyledComponent<T> = T extends 'button'\n  ? React.DetailedHTMLProps<\n      React.ButtonHTMLAttributes<HTMLButtonElement>,\n      HTMLButtonElement\n    >\n  : T extends 'input'\n    ? React.DetailedHTMLProps<\n        React.InputHTMLAttributes<HTMLInputElement>,\n        HTMLInputElement\n      >\n    : T extends 'select'\n      ? React.DetailedHTMLProps<\n          React.SelectHTMLAttributes<HTMLSelectElement>,\n          HTMLSelectElement\n        >\n      : T extends keyof HTMLElementTagNameMap\n        ? React.HTMLAttributes<HTMLElementTagNameMap[T]>\n        : never\n\nexport function getStatusColor(match: AnyRouteMatch) {\n  return match.status === 'success' && match.isFetching\n    ? 'blue'\n    : match.status === 'pending'\n      ? 'yellow'\n      : match.status === 'error'\n        ? 'red'\n        : match.status === 'success'\n          ? 'green'\n          : 'gray'\n}\n\nexport function getRouteStatusColor(\n  matches: AnyRouteMatch[],\n  route: AnyRoute | AnyRootRoute,\n) {\n  const found = matches.find((d) => d.routeId === route.id)\n  if (!found) return 'gray'\n  return getStatusColor(found)\n}\n\ntype Styles =\n  | React.CSSProperties\n  | ((props: Record<string, any>, theme: Theme) => React.CSSProperties)\n\nexport function styled<T extends keyof HTMLElementTagNameMap>(\n  type: T,\n  newStyles: Styles,\n  queries: Record<string, Styles> = {},\n) {\n  return React.forwardRef<HTMLElementTagNameMap[T], StyledComponent<T>>(\n    ({ style, ...rest }, ref) => {\n      const theme = useTheme()\n\n      const mediaStyles = Object.entries(queries).reduce(\n        (current, [key, value]) => {\n          // eslint-disable-next-line react-hooks/rules-of-hooks\n          return useMediaQuery(key)\n            ? {\n                ...current,\n                ...(typeof value === 'function' ? value(rest, theme) : value),\n              }\n            : current\n        },\n        {},\n      )\n\n      return React.createElement(type, {\n        ...rest,\n        style: {\n          ...(typeof newStyles === 'function'\n            ? newStyles(rest, theme)\n            : newStyles),\n          ...style,\n          ...mediaStyles,\n        },\n        ref,\n      })\n    },\n  )\n}\n\nexport function useIsMounted() {\n  const mountedRef = React.useRef(false)\n  const isMounted = React.useCallback(() => mountedRef.current, [])\n\n  React[isServer ? 'useEffect' : 'useLayoutEffect'](() => {\n    mountedRef.current = true\n    return () => {\n      mountedRef.current = false\n    }\n  }, [])\n\n  return isMounted\n}\n\n/**\n * Displays a string regardless the type of the data\n * @param {unknown} value Value to be stringified\n */\nexport const displayValue = (value: unknown) => {\n  const name = Object.getOwnPropertyNames(Object(value))\n  const newValue = typeof value === 'bigint' ? `${value.toString()}n` : value\n  try {\n    return JSON.stringify(newValue, name)\n  } catch (e) {\n    return `unable to stringify`\n  }\n}\n\n/**\n * This hook is a safe useState version which schedules state updates in microtasks\n * to prevent updating a component state while React is rendering different components\n * or when the component is not mounted anymore.\n */\nexport function useSafeState<T>(initialState: T): [T, (value: T) => void] {\n  const isMounted = useIsMounted()\n  const [state, setState] = React.useState(initialState)\n\n  const safeSetState = React.useCallback(\n    (value: T) => {\n      scheduleMicrotask(() => {\n        if (isMounted()) {\n          setState(value)\n        }\n      })\n    },\n    [isMounted],\n  )\n\n  return [state, safeSetState]\n}\n\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\nfunction scheduleMicrotask(callback: () => void) {\n  Promise.resolve()\n    .then(callback)\n    .catch((error) =>\n      setTimeout(() => {\n        throw error\n      }),\n    )\n}\n\nexport function multiSortBy<T>(\n  arr: T[],\n  accessors: ((item: T) => any)[] = [(d) => d],\n): T[] {\n  return arr\n    .map((d, i) => [d, i] as const)\n    .sort(([a, ai], [b, bi]) => {\n      for (const accessor of accessors) {\n        const ao = accessor(a)\n        const bo = accessor(b)\n\n        if (typeof ao === 'undefined') {\n          if (typeof bo === 'undefined') {\n            continue\n          }\n          return 1\n        }\n\n        if (ao === bo) {\n          continue\n        }\n\n        return ao > bo ? 1 : -1\n      }\n\n      return ai - bi\n    })\n    .map(([d]) => d)\n}\n", "let e={data:\"\"},t=t=>\"object\"==typeof window?((t?t.querySelector(\"#_goober\"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement(\"style\")),{innerHTML:\" \",id:\"_goober\"})).firstChild:t||e,r=e=>{let r=t(e),l=r.data;return r.data=\"\",l},l=/(?:([\\u0080-\\uFFFF\\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\\s*)/g,a=/\\/\\*[^]*?\\*\\/|  +/g,n=/\\n+/g,o=(e,t)=>{let r=\"\",l=\"\",a=\"\";for(let n in e){let c=e[n];\"@\"==n[0]?\"i\"==n[1]?r=n+\" \"+c+\";\":l+=\"f\"==n[1]?o(c,n):n+\"{\"+o(c,\"k\"==n[1]?\"\":t)+\"}\":\"object\"==typeof c?l+=o(c,t?t.replace(/([^,])+/g,e=>n.replace(/(^:.*)|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+\" \"+t:t)):n):null!=c&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,\"-$&\").toLowerCase(),a+=o.p?o.p(n,c):n+\":\"+c+\";\")}return r+(t&&a?t+\"{\"+a+\"}\":a)+l},c={},s=e=>{if(\"object\"==typeof e){let t=\"\";for(let r in e)t+=r+s(e[r]);return t}return e},i=(e,t,r,i,p)=>{let u=s(e),d=c[u]||(c[u]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return\"go\"+r})(u));if(!c[d]){let t=u!==e?e:(e=>{let t,r,o=[{}];for(;t=l.exec(e.replace(a,\"\"));)t[4]?o.shift():t[3]?(r=t[3].replace(n,\" \").trim(),o.unshift(o[0][r]=o[0][r]||{})):o[0][t[1]]=t[2].replace(n,\" \").trim();return o[0]})(e);c[d]=o(p?{[\"@keyframes \"+d]:t}:t,r?\"\":\".\"+d)}let f=r&&c.g?c.g:null;return r&&(c.g=c[d]),((e,t,r,l)=>{l?t.data=t.data.replace(l,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(c[d],t,i,f),d},p=(e,t,r)=>e.reduce((e,l,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?\".\"+t:e&&\"object\"==typeof e?e.props?\"\":o(e,\"\"):!1===e?\"\":e}return e+l+(null==n?\"\":n)},\"\");function u(e){let r=this||{},l=e.call?e(r.p):e;return i(l.unshift?l.raw?p(l,[].slice.call(arguments,1),r.p):l.reduce((e,t)=>Object.assign(e,t&&t.call?t(r.p):t),{}):l,t(r.target),r.g,r.o,r.k)}let d,f,g,b=u.bind({g:1}),h=u.bind({k:1});function m(e,t,r,l){o.p=t,d=e,f=r,g=l}function j(e,t){let r=this||{};return function(){let l=arguments;function a(n,o){let c=Object.assign({},n),s=c.className||a.className;r.p=Object.assign({theme:f&&f()},c),r.o=/ *go\\d+/.test(s),c.className=u.apply(r,l)+(s?\" \"+s:\"\"),t&&(c.ref=o);let i=e;return e[0]&&(i=c.as||e,delete c.as),g&&i[0]&&g(c),d(i,c)}return t?t(a):a}}export{u as css,r as extractCss,b as glob,h as keyframes,m as setup,j as styled};\n", "export const tokens = {\n  colors: {\n    inherit: 'inherit',\n    current: 'currentColor',\n    transparent: 'transparent',\n    black: '#000000',\n    white: '#ffffff',\n    neutral: {\n      50: '#f9fafb',\n      100: '#f2f4f7',\n      200: '#eaecf0',\n      300: '#d0d5dd',\n      400: '#98a2b3',\n      500: '#667085',\n      600: '#475467',\n      700: '#344054',\n      800: '#1d2939',\n      900: '#101828',\n    },\n    darkGray: {\n      50: '#525c7a',\n      100: '#49536e',\n      200: '#414962',\n      300: '#394056',\n      400: '#313749',\n      500: '#292e3d',\n      600: '#212530',\n      700: '#191c24',\n      800: '#111318',\n      900: '#0b0d10',\n    },\n    gray: {\n      50: '#f9fafb',\n      100: '#f2f4f7',\n      200: '#eaecf0',\n      300: '#d0d5dd',\n      400: '#98a2b3',\n      500: '#667085',\n      600: '#475467',\n      700: '#344054',\n      800: '#1d2939',\n      900: '#101828',\n    },\n    blue: {\n      25: '#F5FAFF',\n      50: '#EFF8FF',\n      100: '#D1E9FF',\n      200: '#B2DDFF',\n      300: '#84CAFF',\n      400: '#53B1FD',\n      500: '#2E90FA',\n      600: '#1570EF',\n      700: '#175CD3',\n      800: '#1849A9',\n      900: '#194185',\n    },\n    green: {\n      25: '#F6FEF9',\n      50: '#ECFDF3',\n      100: '#D1FADF',\n      200: '#A6F4C5',\n      300: '#6CE9A6',\n      400: '#32D583',\n      500: '#12B76A',\n      600: '#039855',\n      700: '#027A48',\n      800: '#05603A',\n      900: '#054F31',\n    },\n    red: {\n      50: '#fef2f2',\n      100: '#fee2e2',\n      200: '#fecaca',\n      300: '#fca5a5',\n      400: '#f87171',\n      500: '#ef4444',\n      600: '#dc2626',\n      700: '#b91c1c',\n      800: '#991b1b',\n      900: '#7f1d1d',\n      950: '#450a0a',\n    },\n    yellow: {\n      25: '#FFFCF5',\n      50: '#FFFAEB',\n      100: '#FEF0C7',\n      200: '#FEDF89',\n      300: '#FEC84B',\n      400: '#FDB022',\n      500: '#F79009',\n      600: '#DC6803',\n      700: '#B54708',\n      800: '#93370D',\n      900: '#7A2E0E',\n    },\n    purple: {\n      25: '#FAFAFF',\n      50: '#F4F3FF',\n      100: '#EBE9FE',\n      200: '#D9D6FE',\n      300: '#BDB4FE',\n      400: '#9B8AFB',\n      500: '#7A5AF8',\n      600: '#6938EF',\n      700: '#5925DC',\n      800: '#4A1FB8',\n      900: '#3E1C96',\n    },\n    teal: {\n      25: '#F6FEFC',\n      50: '#F0FDF9',\n      100: '#CCFBEF',\n      200: '#99F6E0',\n      300: '#5FE9D0',\n      400: '#2ED3B7',\n      500: '#15B79E',\n      600: '#0E9384',\n      700: '#107569',\n      800: '#125D56',\n      900: '#134E48',\n    },\n    pink: {\n      25: '#fdf2f8',\n      50: '#fce7f3',\n      100: '#fbcfe8',\n      200: '#f9a8d4',\n      300: '#f472b6',\n      400: '#ec4899',\n      500: '#db2777',\n      600: '#be185d',\n      700: '#9d174d',\n      800: '#831843',\n      900: '#500724',\n    },\n    cyan: {\n      25: '#ecfeff',\n      50: '#cffafe',\n      100: '#a5f3fc',\n      200: '#67e8f9',\n      300: '#22d3ee',\n      400: '#06b6d4',\n      500: '#0891b2',\n      600: '#0e7490',\n      700: '#155e75',\n      800: '#164e63',\n      900: '#083344',\n    },\n  },\n  alpha: {\n    100: 'ff',\n    90: 'e5',\n    80: 'cc',\n    70: 'b3',\n    60: '99',\n    50: '80',\n    40: '66',\n    30: '4d',\n    20: '33',\n    10: '1a',\n    0: '00',\n  },\n  font: {\n    size: {\n      '2xs': 'calc(var(--tsrd-font-size) * 0.625)',\n      xs: 'calc(var(--tsrd-font-size) * 0.75)',\n      sm: 'calc(var(--tsrd-font-size) * 0.875)',\n      md: 'var(--tsrd-font-size)',\n      lg: 'calc(var(--tsrd-font-size) * 1.125)',\n      xl: 'calc(var(--tsrd-font-size) * 1.25)',\n      '2xl': 'calc(var(--tsrd-font-size) * 1.5)',\n      '3xl': 'calc(var(--tsrd-font-size) * 1.875)',\n      '4xl': 'calc(var(--tsrd-font-size) * 2.25)',\n      '5xl': 'calc(var(--tsrd-font-size) * 3)',\n      '6xl': 'calc(var(--tsrd-font-size) * 3.75)',\n      '7xl': 'calc(var(--tsrd-font-size) * 4.5)',\n      '8xl': 'calc(var(--tsrd-font-size) * 6)',\n      '9xl': 'calc(var(--tsrd-font-size) * 8)',\n    },\n    lineHeight: {\n      '3xs': 'calc(var(--tsrd-font-size) * 0.75)',\n      '2xs': 'calc(var(--tsrd-font-size) * 0.875)',\n      xs: 'calc(var(--tsrd-font-size) * 1)',\n      sm: 'calc(var(--tsrd-font-size) * 1.25)',\n      md: 'calc(var(--tsrd-font-size) * 1.5)',\n      lg: 'calc(var(--tsrd-font-size) * 1.75)',\n      xl: 'calc(var(--tsrd-font-size) * 2)',\n      '2xl': 'calc(var(--tsrd-font-size) * 2.25)',\n      '3xl': 'calc(var(--tsrd-font-size) * 2.5)',\n      '4xl': 'calc(var(--tsrd-font-size) * 2.75)',\n      '5xl': 'calc(var(--tsrd-font-size) * 3)',\n      '6xl': 'calc(var(--tsrd-font-size) * 3.25)',\n      '7xl': 'calc(var(--tsrd-font-size) * 3.5)',\n      '8xl': 'calc(var(--tsrd-font-size) * 3.75)',\n      '9xl': 'calc(var(--tsrd-font-size) * 4)',\n    },\n    weight: {\n      thin: '100',\n      extralight: '200',\n      light: '300',\n      normal: '400',\n      medium: '500',\n      semibold: '600',\n      bold: '700',\n      extrabold: '800',\n      black: '900',\n    },\n    fontFamily: {\n      sans: 'ui-sans-serif, Inter, system-ui, sans-serif, sans-serif',\n      mono: `ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace`,\n    },\n  },\n  breakpoints: {\n    xs: '320px',\n    sm: '640px',\n    md: '768px',\n    lg: '1024px',\n    xl: '1280px',\n    '2xl': '1536px',\n  },\n  border: {\n    radius: {\n      none: '0px',\n      xs: 'calc(var(--tsrd-font-size) * 0.125)',\n      sm: 'calc(var(--tsrd-font-size) * 0.25)',\n      md: 'calc(var(--tsrd-font-size) * 0.375)',\n      lg: 'calc(var(--tsrd-font-size) * 0.5)',\n      xl: 'calc(var(--tsrd-font-size) * 0.75)',\n      '2xl': 'calc(var(--tsrd-font-size) * 1)',\n      '3xl': 'calc(var(--tsrd-font-size) * 1.5)',\n      full: '9999px',\n    },\n  },\n  size: {\n    0: '0px',\n    0.25: 'calc(var(--tsrd-font-size) * 0.0625)',\n    0.5: 'calc(var(--tsrd-font-size) * 0.125)',\n    1: 'calc(var(--tsrd-font-size) * 0.25)',\n    1.5: 'calc(var(--tsrd-font-size) * 0.375)',\n    2: 'calc(var(--tsrd-font-size) * 0.5)',\n    2.5: 'calc(var(--tsrd-font-size) * 0.625)',\n    3: 'calc(var(--tsrd-font-size) * 0.75)',\n    3.5: 'calc(var(--tsrd-font-size) * 0.875)',\n    4: 'calc(var(--tsrd-font-size) * 1)',\n    4.5: 'calc(var(--tsrd-font-size) * 1.125)',\n    5: 'calc(var(--tsrd-font-size) * 1.25)',\n    5.5: 'calc(var(--tsrd-font-size) * 1.375)',\n    6: 'calc(var(--tsrd-font-size) * 1.5)',\n    6.5: 'calc(var(--tsrd-font-size) * 1.625)',\n    7: 'calc(var(--tsrd-font-size) * 1.75)',\n    8: 'calc(var(--tsrd-font-size) * 2)',\n    9: 'calc(var(--tsrd-font-size) * 2.25)',\n    10: 'calc(var(--tsrd-font-size) * 2.5)',\n    11: 'calc(var(--tsrd-font-size) * 2.75)',\n    12: 'calc(var(--tsrd-font-size) * 3)',\n    14: 'calc(var(--tsrd-font-size) * 3.5)',\n    16: 'calc(var(--tsrd-font-size) * 4)',\n    20: 'calc(var(--tsrd-font-size) * 5)',\n    24: 'calc(var(--tsrd-font-size) * 6)',\n    28: 'calc(var(--tsrd-font-size) * 7)',\n    32: 'calc(var(--tsrd-font-size) * 8)',\n    36: 'calc(var(--tsrd-font-size) * 9)',\n    40: 'calc(var(--tsrd-font-size) * 10)',\n    44: 'calc(var(--tsrd-font-size) * 11)',\n    48: 'calc(var(--tsrd-font-size) * 12)',\n    52: 'calc(var(--tsrd-font-size) * 13)',\n    56: 'calc(var(--tsrd-font-size) * 14)',\n    60: 'calc(var(--tsrd-font-size) * 15)',\n    64: 'calc(var(--tsrd-font-size) * 16)',\n    72: 'calc(var(--tsrd-font-size) * 18)',\n    80: 'calc(var(--tsrd-font-size) * 20)',\n    96: 'calc(var(--tsrd-font-size) * 24)',\n  },\n  shadow: {\n    xs: (_: string = 'rgb(0 0 0 / 0.1)') =>\n      `0 1px 2px 0 rgb(0 0 0 / 0.05)` as const,\n    sm: (color: string = 'rgb(0 0 0 / 0.1)') =>\n      `0 1px 3px 0 ${color}, 0 1px 2px -1px ${color}` as const,\n    md: (color: string = 'rgb(0 0 0 / 0.1)') =>\n      `0 4px 6px -1px ${color}, 0 2px 4px -2px ${color}` as const,\n    lg: (color: string = 'rgb(0 0 0 / 0.1)') =>\n      `0 10px 15px -3px ${color}, 0 4px 6px -4px ${color}` as const,\n    xl: (color: string = 'rgb(0 0 0 / 0.1)') =>\n      `0 20px 25px -5px ${color}, 0 8px 10px -6px ${color}` as const,\n    '2xl': (color: string = 'rgb(0 0 0 / 0.25)') =>\n      `0 25px 50px -12px ${color}` as const,\n    inner: (color: string = 'rgb(0 0 0 / 0.05)') =>\n      `inset 0 2px 4px 0 ${color}` as const,\n    none: () => `none` as const,\n  },\n  zIndices: {\n    hide: -1,\n    auto: 'auto',\n    base: 0,\n    docked: 10,\n    dropdown: 1000,\n    sticky: 1100,\n    banner: 1200,\n    overlay: 1300,\n    modal: 1400,\n    popover: 1500,\n    skipLink: 1600,\n    toast: 1700,\n    tooltip: 1800,\n  },\n} as const\n", "import * as React from 'react'\nimport { clsx as cx } from 'clsx'\nimport { tokens } from './tokens'\nimport { displayValue, styled } from './utils'\nimport { css } from 'goober'\n\ntype ExpanderProps = {\n  expanded: boolean\n  style?: React.CSSProperties\n}\n\nexport const Expander = ({ expanded, style = {} }: ExpanderProps) => (\n  <span className={getStyles().expander}>\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"12\"\n      height=\"12\"\n      fill=\"none\"\n      viewBox=\"0 0 24 24\"\n      className={cx(getStyles().expanderIcon(expanded))}\n    >\n      <path\n        stroke=\"currentColor\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        strokeWidth=\"2\"\n        d=\"M9 18l6-6-6-6\"\n      ></path>\n    </svg>\n  </span>\n)\n\ntype Entry = {\n  label: string\n}\n\ntype RendererProps = {\n  handleEntry: HandleEntryFn\n  label?: React.ReactNode\n  value: unknown\n  subEntries: Entry[]\n  subEntryPages: Entry[][]\n  type: string\n  expanded: boolean\n  toggleExpanded: () => void\n  pageSize: number\n  renderer?: Renderer\n  filterSubEntries?: (subEntries: Property[]) => Property[]\n}\n\n/**\n * Chunk elements in the array by size\n *\n * when the array cannot be chunked evenly by size, the last chunk will be\n * filled with the remaining elements\n *\n * @example\n * chunkArray(['a','b', 'c', 'd', 'e'], 2) // returns [['a','b'], ['c', 'd'], ['e']]\n */\nexport function chunkArray<T>(array: T[], size: number): T[][] {\n  if (size < 1) return []\n  let i = 0\n  const result: T[][] = []\n  while (i < array.length) {\n    result.push(array.slice(i, i + size))\n    i = i + size\n  }\n  return result\n}\n\ntype Renderer = (props: RendererProps) => React.ReactNode\n\nexport const DefaultRenderer: Renderer = ({\n  handleEntry,\n  label,\n  value,\n  subEntries = [],\n  subEntryPages = [],\n  type,\n  expanded = false,\n  toggleExpanded,\n  pageSize,\n  renderer,\n}) => {\n  const [expandedPages, setExpandedPages] = React.useState<number[]>([])\n  const [valueSnapshot, setValueSnapshot] = React.useState(undefined)\n\n  const refreshValueSnapshot = () => {\n    setValueSnapshot((value as () => any)())\n  }\n\n  return (\n    <div className={getStyles().entry}>\n      {subEntryPages.length ? (\n        <>\n          <button\n            className={getStyles().expandButton}\n            onClick={() => toggleExpanded()}\n          >\n            <Expander expanded={expanded} />\n            {label}\n            <span className={getStyles().info}>\n              {String(type).toLowerCase() === 'iterable' ? '(Iterable) ' : ''}\n              {subEntries.length} {subEntries.length > 1 ? `items` : `item`}\n            </span>\n          </button>\n          {expanded ? (\n            subEntryPages.length === 1 ? (\n              <div className={getStyles().subEntries}>\n                {subEntries.map((entry, index) => handleEntry(entry))}\n              </div>\n            ) : (\n              <div className={getStyles().subEntries}>\n                {subEntryPages.map((entries, index) => {\n                  return (\n                    <div key={index}>\n                      <div className={getStyles().entry}>\n                        <button\n                          className={cx(getStyles().labelButton, 'labelButton')}\n                          onClick={() =>\n                            setExpandedPages((old) =>\n                              old.includes(index)\n                                ? old.filter((d) => d !== index)\n                                : [...old, index],\n                            )\n                          }\n                        >\n                          <Expander expanded={expandedPages.includes(index)} />{' '}\n                          [{index * pageSize} ...{' '}\n                          {index * pageSize + pageSize - 1}]\n                        </button>\n                        {expandedPages.includes(index) ? (\n                          <div className={getStyles().subEntries}>\n                            {entries.map((entry) => handleEntry(entry))}\n                          </div>\n                        ) : null}\n                      </div>\n                    </div>\n                  )\n                })}\n              </div>\n            )\n          ) : null}\n        </>\n      ) : type === 'function' ? (\n        <>\n          <Explorer\n            renderer={renderer}\n            label={\n              <button\n                onClick={refreshValueSnapshot}\n                className={getStyles().refreshValueBtn}\n              >\n                <span>{label}</span> 🔄{' '}\n              </button>\n            }\n            value={valueSnapshot}\n            defaultExpanded={{}}\n          />\n        </>\n      ) : (\n        <>\n          <span>{label}:</span>{' '}\n          <span className={getStyles().value}>{displayValue(value)}</span>\n        </>\n      )}\n    </div>\n  )\n}\n\ntype HandleEntryFn = (entry: Entry) => React.ReactNode\n\ntype ExplorerProps = Partial<RendererProps> & {\n  renderer?: Renderer\n  defaultExpanded?: true | Record<string, boolean>\n}\n\ntype Property = {\n  defaultExpanded?: boolean | Record<string, boolean>\n  label: string\n  value: unknown\n}\n\nfunction isIterable(x: any): x is Iterable<unknown> {\n  return Symbol.iterator in x\n}\n\nexport default function Explorer({\n  value,\n  defaultExpanded,\n  renderer = DefaultRenderer,\n  pageSize = 100,\n  filterSubEntries,\n  ...rest\n}: ExplorerProps) {\n  const [expanded, setExpanded] = React.useState(Boolean(defaultExpanded))\n  const toggleExpanded = React.useCallback(() => setExpanded((old) => !old), [])\n\n  let type: string = typeof value\n  let subEntries: Property[] = []\n\n  const makeProperty = (sub: { label: string; value: unknown }): Property => {\n    const subDefaultExpanded =\n      defaultExpanded === true\n        ? { [sub.label]: true }\n        : defaultExpanded?.[sub.label]\n    return {\n      ...sub,\n      defaultExpanded: subDefaultExpanded,\n    }\n  }\n\n  if (Array.isArray(value)) {\n    type = 'array'\n    subEntries = value.map((d, i) =>\n      makeProperty({\n        label: i.toString(),\n        value: d,\n      }),\n    )\n  } else if (\n    value !== null &&\n    typeof value === 'object' &&\n    isIterable(value) &&\n    typeof value[Symbol.iterator] === 'function'\n  ) {\n    type = 'Iterable'\n    subEntries = Array.from(value, (val, i) =>\n      makeProperty({\n        label: i.toString(),\n        value: val,\n      }),\n    )\n  } else if (typeof value === 'object' && value !== null) {\n    type = 'object'\n    subEntries = Object.entries(value).map(([key, val]) =>\n      makeProperty({\n        label: key,\n        value: val,\n      }),\n    )\n  }\n\n  subEntries = filterSubEntries ? filterSubEntries(subEntries) : subEntries\n\n  const subEntryPages = chunkArray(subEntries, pageSize)\n\n  return renderer({\n    handleEntry: (entry) => (\n      <Explorer\n        key={entry.label}\n        value={value}\n        renderer={renderer}\n        filterSubEntries={filterSubEntries}\n        {...rest}\n        {...entry}\n      />\n    ),\n    type,\n    subEntries,\n    subEntryPages,\n    value,\n    expanded,\n    toggleExpanded,\n    pageSize,\n    ...rest,\n  })\n}\n\nconst stylesFactory = () => {\n  const { colors, font, size, alpha, shadow, border } = tokens\n  const { fontFamily, lineHeight, size: fontSize } = font\n\n  return {\n    entry: css`\n      font-family: ${fontFamily.mono};\n      font-size: ${fontSize.xs};\n      line-height: ${lineHeight.sm};\n      outline: none;\n      word-break: break-word;\n    `,\n    labelButton: css`\n      cursor: pointer;\n      color: inherit;\n      font: inherit;\n      outline: inherit;\n      background: transparent;\n      border: none;\n      padding: 0;\n    `,\n    expander: css`\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      width: ${size[3]};\n      height: ${size[3]};\n      padding-left: 3px;\n      box-sizing: content-box;\n    `,\n    expanderIcon: (expanded: boolean) => {\n      if (expanded) {\n        return css`\n          transform: rotate(90deg);\n          transition: transform 0.1s ease;\n        `\n      }\n      return css`\n        transform: rotate(0deg);\n        transition: transform 0.1s ease;\n      `\n    },\n    expandButton: css`\n      display: flex;\n      gap: ${size[1]};\n      align-items: center;\n      cursor: pointer;\n      color: inherit;\n      font: inherit;\n      outline: inherit;\n      background: transparent;\n      border: none;\n      padding: 0;\n    `,\n    value: css`\n      color: ${colors.purple[400]};\n    `,\n    subEntries: css`\n      margin-left: ${size[2]};\n      padding-left: ${size[2]};\n      border-left: 2px solid ${colors.darkGray[400]};\n    `,\n    info: css`\n      color: ${colors.gray[500]};\n      font-size: ${fontSize['2xs']};\n      padding-left: ${size[1]};\n    `,\n    refreshValueBtn: css`\n      appearance: none;\n      border: 0;\n      cursor: pointer;\n      background: transparent;\n      color: inherit;\n      padding: 0;\n      font-family: ${fontFamily.mono};\n      font-size: ${fontSize.xs};\n    `,\n  }\n}\n\nlet _styles: ReturnType<typeof stylesFactory> | null = null\n\nfunction getStyles() {\n  if (_styles) return _styles\n  _styles = stylesFactory()\n\n  return _styles\n}\n", "import React from 'react'\n\nexport function TanStackLogo() {\n  const id = React.useId()\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      enableBackground=\"new 0 0 634 633\"\n      viewBox=\"0 0 634 633\"\n    >\n      <g transform=\"translate(1)\">\n        <linearGradient\n          id={`a-${id}`}\n          x1=\"-641.486\"\n          x2=\"-641.486\"\n          y1=\"856.648\"\n          y2=\"855.931\"\n          gradientTransform=\"matrix(633 0 0 -633 406377 542258)\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop offset=\"0\" stopColor=\"#6bdaff\"></stop>\n          <stop offset=\"0.319\" stopColor=\"#f9ffb5\"></stop>\n          <stop offset=\"0.706\" stopColor=\"#ffa770\"></stop>\n          <stop offset=\"1\" stopColor=\"#ff7373\"></stop>\n        </linearGradient>\n        <circle\n          cx=\"316.5\"\n          cy=\"316.5\"\n          r=\"316.5\"\n          fill={`url(#a-${id})`}\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n        ></circle>\n        <defs>\n          <filter\n            id={`b-${id}`}\n            width=\"454\"\n            height=\"396.9\"\n            x=\"-137.5\"\n            y=\"412\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`c-${id}`}\n          width=\"454\"\n          height=\"396.9\"\n          x=\"-137.5\"\n          y=\"412\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#b-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <ellipse\n          cx=\"89.5\"\n          cy=\"610.5\"\n          fill=\"#015064\"\n          fillRule=\"evenodd\"\n          stroke=\"#00CFE2\"\n          strokeWidth=\"25\"\n          clipRule=\"evenodd\"\n          mask={`url(#c-${id})`}\n          rx=\"214.5\"\n          ry=\"186\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`d-${id}`}\n            width=\"454\"\n            height=\"396.9\"\n            x=\"316.5\"\n            y=\"412\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`e-${id}`}\n          width=\"454\"\n          height=\"396.9\"\n          x=\"316.5\"\n          y=\"412\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#d-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <ellipse\n          cx=\"543.5\"\n          cy=\"610.5\"\n          fill=\"#015064\"\n          fillRule=\"evenodd\"\n          stroke=\"#00CFE2\"\n          strokeWidth=\"25\"\n          clipRule=\"evenodd\"\n          mask={`url(#e-${id})`}\n          rx=\"214.5\"\n          ry=\"186\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`f-${id}`}\n            width=\"454\"\n            height=\"396.9\"\n            x=\"-137.5\"\n            y=\"450\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`g-${id}`}\n          width=\"454\"\n          height=\"396.9\"\n          x=\"-137.5\"\n          y=\"450\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#f-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <ellipse\n          cx=\"89.5\"\n          cy=\"648.5\"\n          fill=\"#015064\"\n          fillRule=\"evenodd\"\n          stroke=\"#00A8B8\"\n          strokeWidth=\"25\"\n          clipRule=\"evenodd\"\n          mask={`url(#g-${id})`}\n          rx=\"214.5\"\n          ry=\"186\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`h-${id}`}\n            width=\"454\"\n            height=\"396.9\"\n            x=\"316.5\"\n            y=\"450\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`i-${id}`}\n          width=\"454\"\n          height=\"396.9\"\n          x=\"316.5\"\n          y=\"450\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#h-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <ellipse\n          cx=\"543.5\"\n          cy=\"648.5\"\n          fill=\"#015064\"\n          fillRule=\"evenodd\"\n          stroke=\"#00A8B8\"\n          strokeWidth=\"25\"\n          clipRule=\"evenodd\"\n          mask={`url(#i-${id})`}\n          rx=\"214.5\"\n          ry=\"186\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`j-${id}`}\n            width=\"454\"\n            height=\"396.9\"\n            x=\"-137.5\"\n            y=\"486\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`k-${id}`}\n          width=\"454\"\n          height=\"396.9\"\n          x=\"-137.5\"\n          y=\"486\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#j-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <ellipse\n          cx=\"89.5\"\n          cy=\"684.5\"\n          fill=\"#015064\"\n          fillRule=\"evenodd\"\n          stroke=\"#007782\"\n          strokeWidth=\"25\"\n          clipRule=\"evenodd\"\n          mask={`url(#k-${id})`}\n          rx=\"214.5\"\n          ry=\"186\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`l-${id}`}\n            width=\"454\"\n            height=\"396.9\"\n            x=\"316.5\"\n            y=\"486\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`m-${id}`}\n          width=\"454\"\n          height=\"396.9\"\n          x=\"316.5\"\n          y=\"486\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#l-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <ellipse\n          cx=\"543.5\"\n          cy=\"684.5\"\n          fill=\"#015064\"\n          fillRule=\"evenodd\"\n          stroke=\"#007782\"\n          strokeWidth=\"25\"\n          clipRule=\"evenodd\"\n          mask={`url(#m-${id})`}\n          rx=\"214.5\"\n          ry=\"186\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`n-${id}`}\n            width=\"176.9\"\n            height=\"129.3\"\n            x=\"272.2\"\n            y=\"308\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`o-${id}`}\n          width=\"176.9\"\n          height=\"129.3\"\n          x=\"272.2\"\n          y=\"308\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#n-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <g mask={`url(#o-${id})`}>\n          <path\n            fill=\"none\"\n            stroke=\"#000\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"bevel\"\n            strokeWidth=\"11\"\n            d=\"M436 403.2l-5 28.6m-140-90.3l-10.9 62m52.8-19.4l-4.3 27.1\"\n          ></path>\n          <linearGradient\n            id={`p-${id}`}\n            x1=\"-645.656\"\n            x2=\"-646.499\"\n            y1=\"854.878\"\n            y2=\"854.788\"\n            gradientTransform=\"matrix(-184.159 -32.4722 11.4608 -64.9973 -128419.844 34938.836)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stopColor=\"#ee2700\"></stop>\n            <stop offset=\"1\" stopColor=\"#ff008e\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#p-${id})`}\n            fillRule=\"evenodd\"\n            d=\"M344.1 363l97.7 17.2c5.8 2.1 8.2 6.2 7.1 12.1-1 5.9-4.7 9.2-11 9.9l-106-18.7-57.5-59.2c-3.2-4.8-2.9-9.1.8-12.8 3.7-3.7 8.3-4.4 13.7-2.1l55.2 53.6z\"\n            clipRule=\"evenodd\"\n          ></path>\n          <path\n            fill=\"#D8D8D8\"\n            fillRule=\"evenodd\"\n            stroke=\"#FFF\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"bevel\"\n            strokeWidth=\"7\"\n            d=\"M428.3 384.5l.9-6.5m-33.9 1.5l.9-6.5m-34 .5l.9-6.1m-38.9-16.1l4.2-3.9m-25.2-16.1l4.2-3.9\"\n            clipRule=\"evenodd\"\n          ></path>\n        </g>\n        <defs>\n          <filter\n            id={`q-${id}`}\n            width=\"280.6\"\n            height=\"317.4\"\n            x=\"73.2\"\n            y=\"113.9\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`r-${id}`}\n          width=\"280.6\"\n          height=\"317.4\"\n          x=\"73.2\"\n          y=\"113.9\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#q-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <g mask={`url(#r-${id})`}>\n          <linearGradient\n            id={`s-${id}`}\n            x1=\"-646.8\"\n            x2=\"-646.8\"\n            y1=\"854.844\"\n            y2=\"853.844\"\n            gradientTransform=\"matrix(-100.1751 48.8587 -97.9753 -200.879 19124.773 203538.61)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stopColor=\"#a17500\"></stop>\n            <stop offset=\"1\" stopColor=\"#5d2100\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#s-${id})`}\n            fillRule=\"evenodd\"\n            d=\"M192.3 203c8.1 37.3 14 73.6 17.8 109.1 3.8 35.4 2.8 75.2-2.9 119.2l61.2-16.7c-15.6-59-25.2-97.9-28.6-116.6-3.4-18.7-10.8-51.8-22.2-99.6l-25.3 4.6\"\n            clipRule=\"evenodd\"\n          ></path>\n          <linearGradient\n            id={`t-${id}`}\n            x1=\"-635.467\"\n            x2=\"-635.467\"\n            y1=\"852.115\"\n            y2=\"851.115\"\n            gradientTransform=\"matrix(92.6873 4.8575 2.0257 -38.6535 57323.695 36176.047)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stopColor=\"#2f8a00\"></stop>\n            <stop offset=\"1\" stopColor=\"#90ff57\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#t-${id})`}\n            fillRule=\"evenodd\"\n            stroke=\"#2F8A00\"\n            strokeWidth=\"13\"\n            d=\"M195 183.9s-12.6-22.1-36.5-29.9c-15.9-5.2-34.4-1.5-55.5 11.1 15.9 14.3 29.5 22.6 40.7 24.9 16.8 3.6 51.3-6.1 51.3-6.1z\"\n            clipRule=\"evenodd\"\n          ></path>\n          <linearGradient\n            id={`u-${id}`}\n            x1=\"-636.573\"\n            x2=\"-636.573\"\n            y1=\"855.444\"\n            y2=\"854.444\"\n            gradientTransform=\"matrix(109.9945 5.7646 6.3597 -121.3507 64719.133 107659.336)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stopColor=\"#2f8a00\"></stop>\n            <stop offset=\"1\" stopColor=\"#90ff57\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#u-${id})`}\n            fillRule=\"evenodd\"\n            stroke=\"#2F8A00\"\n            strokeWidth=\"13\"\n            d=\"M194.9 184.5s-47.5-8.5-83.2 15.7c-23.8 16.2-34.3 49.3-31.6 99.3 30.3-27.8 52.1-48.5 65.2-61.9 19.8-20 49.6-53.1 49.6-53.1z\"\n            clipRule=\"evenodd\"\n          ></path>\n          <linearGradient\n            id={`v-${id}`}\n            x1=\"-632.145\"\n            x2=\"-632.145\"\n            y1=\"854.174\"\n            y2=\"853.174\"\n            gradientTransform=\"matrix(62.9558 3.2994 3.5021 -66.8246 37035.367 59284.227)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stopColor=\"#2f8a00\"></stop>\n            <stop offset=\"1\" stopColor=\"#90ff57\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#v-${id})`}\n            fillRule=\"evenodd\"\n            stroke=\"#2F8A00\"\n            strokeWidth=\"13\"\n            d=\"M195 183.9c-.8-21.9 6-38 20.6-48.2 14.6-10.2 29.8-15.3 45.5-15.3-6.1 21.4-14.5 35.8-25.2 43.4-10.7 7.5-24.4 14.2-40.9 20.1z\"\n            clipRule=\"evenodd\"\n          ></path>\n          <linearGradient\n            id={`w-${id}`}\n            x1=\"-638.224\"\n            x2=\"-638.224\"\n            y1=\"853.801\"\n            y2=\"852.801\"\n            gradientTransform=\"matrix(152.4666 7.9904 3.0934 -59.0251 94939.86 55646.855)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stopColor=\"#2f8a00\"></stop>\n            <stop offset=\"1\" stopColor=\"#90ff57\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#w-${id})`}\n            fillRule=\"evenodd\"\n            stroke=\"#2F8A00\"\n            strokeWidth=\"13\"\n            d=\"M194.9 184.5c31.9-30 64.1-39.7 96.7-29 32.6 10.7 50.8 30.4 54.6 59.1-35.2-5.5-60.4-9.6-75.8-12.1-15.3-2.6-40.5-8.6-75.5-18z\"\n            clipRule=\"evenodd\"\n          ></path>\n          <linearGradient\n            id={`x-${id}`}\n            x1=\"-637.723\"\n            x2=\"-637.723\"\n            y1=\"855.103\"\n            y2=\"854.103\"\n            gradientTransform=\"matrix(136.467 7.1519 5.2165 -99.5377 82830.875 89859.578)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stopColor=\"#2f8a00\"></stop>\n            <stop offset=\"1\" stopColor=\"#90ff57\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#x-${id})`}\n            fillRule=\"evenodd\"\n            stroke=\"#2F8A00\"\n            strokeWidth=\"13\"\n            d=\"M194.9 184.5c35.8-7.6 65.6-.2 89.2 22 23.6 22.2 37.7 49 42.3 80.3-39.8-9.7-68.3-23.8-85.5-42.4-17.2-18.5-32.5-38.5-46-59.9z\"\n            clipRule=\"evenodd\"\n          ></path>\n          <linearGradient\n            id={`y-${id}`}\n            x1=\"-631.79\"\n            x2=\"-631.79\"\n            y1=\"855.872\"\n            y2=\"854.872\"\n            gradientTransform=\"matrix(60.8683 3.19 8.7771 -167.4773 31110.818 145537.61)\"\n            gradientUnits=\"userSpaceOnUse\"\n          >\n            <stop offset=\"0\" stopColor=\"#2f8a00\"></stop>\n            <stop offset=\"1\" stopColor=\"#90ff57\"></stop>\n          </linearGradient>\n          <path\n            fill={`url(#y-${id})`}\n            fillRule=\"evenodd\"\n            stroke=\"#2F8A00\"\n            strokeWidth=\"13\"\n            d=\"M194.9 184.5c-33.6 13.8-53.6 35.7-60.1 65.6-6.5 29.9-3.6 63.1 8.7 99.6 27.4-40.3 43.2-69.6 47.4-88 4.2-18.3 5.5-44.1 4-77.2z\"\n            clipRule=\"evenodd\"\n          ></path>\n          <path\n            fill=\"none\"\n            stroke=\"#2F8A00\"\n            strokeLinecap=\"round\"\n            strokeWidth=\"8\"\n            d=\"M196.5 182.3c-14.8 21.6-25.1 41.4-30.8 59.4-5.7 18-9.4 33-11.1 45.1\"\n          ></path>\n          <path\n            fill=\"none\"\n            stroke=\"#2F8A00\"\n            strokeLinecap=\"round\"\n            strokeWidth=\"8\"\n            d=\"M194.8 185.7c-24.4 1.7-43.8 9-58.1 21.8-14.3 12.8-24.7 25.4-31.3 37.8m99.1-68.9c29.7-6.7 52-8.4 67-5 15 3.4 26.9 8.7 35.8 15.9m-110.8-5.9c20.3 9.9 38.2 20.5 53.9 31.9 15.7 11.4 27.4 22.1 35.1 32\"\n          ></path>\n        </g>\n        <defs>\n          <filter\n            id={`z-${id}`}\n            width=\"532\"\n            height=\"633\"\n            x=\"50.5\"\n            y=\"399\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`A-${id}`}\n          width=\"532\"\n          height=\"633\"\n          x=\"50.5\"\n          y=\"399\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#z-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <linearGradient\n          id={`B-${id}`}\n          x1=\"-641.104\"\n          x2=\"-641.278\"\n          y1=\"856.577\"\n          y2=\"856.183\"\n          gradientTransform=\"matrix(532 0 0 -633 341484.5 542657)\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop offset=\"0\" stopColor=\"#fff400\"></stop>\n          <stop offset=\"1\" stopColor=\"#3c8700\"></stop>\n        </linearGradient>\n        <ellipse\n          cx=\"316.5\"\n          cy=\"715.5\"\n          fill={`url(#B-${id})`}\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          mask={`url(#A-${id})`}\n          rx=\"266\"\n          ry=\"316.5\"\n        ></ellipse>\n        <defs>\n          <filter\n            id={`C-${id}`}\n            width=\"288\"\n            height=\"283\"\n            x=\"391\"\n            y=\"-24\"\n            filterUnits=\"userSpaceOnUse\"\n          >\n            <feColorMatrix values=\"1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0\"></feColorMatrix>\n          </filter>\n        </defs>\n        <mask\n          id={`D-${id}`}\n          width=\"288\"\n          height=\"283\"\n          x=\"391\"\n          y=\"-24\"\n          maskUnits=\"userSpaceOnUse\"\n        >\n          <g filter={`url(#C-${id})`}>\n            <circle\n              cx=\"316.5\"\n              cy=\"316.5\"\n              r=\"316.5\"\n              fill=\"#FFF\"\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n            ></circle>\n          </g>\n        </mask>\n        <g mask={`url(#D-${id})`}>\n          <g transform=\"translate(397 -24)\">\n            <linearGradient\n              id={`E-${id}`}\n              x1=\"-1036.672\"\n              x2=\"-1036.672\"\n              y1=\"880.018\"\n              y2=\"879.018\"\n              gradientTransform=\"matrix(227 0 0 -227 235493 199764)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stopColor=\"#ffdf00\"></stop>\n              <stop offset=\"1\" stopColor=\"#ff9d00\"></stop>\n            </linearGradient>\n            <circle\n              cx=\"168.5\"\n              cy=\"113.5\"\n              r=\"113.5\"\n              fill={`url(#E-${id})`}\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n            ></circle>\n            <linearGradient\n              id={`F-${id}`}\n              x1=\"-1017.329\"\n              x2=\"-1018.602\"\n              y1=\"658.003\"\n              y2=\"657.998\"\n              gradientTransform=\"matrix(30 0 0 -1 30558 771)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stopColor=\"#ffa400\"></stop>\n              <stop offset=\"1\" stopColor=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#F-${id})`}\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"bevel\"\n              strokeWidth=\"12\"\n              d=\"M30 113H0\"\n            ></path>\n            <linearGradient\n              id={`G-${id}`}\n              x1=\"-1014.501\"\n              x2=\"-1015.774\"\n              y1=\"839.985\"\n              y2=\"839.935\"\n              gradientTransform=\"matrix(26.5 0 0 -5.5 26925 4696.5)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stopColor=\"#ffa400\"></stop>\n              <stop offset=\"1\" stopColor=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#G-${id})`}\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"bevel\"\n              strokeWidth=\"12\"\n              d=\"M33.5 79.5L7 74\"\n            ></path>\n            <linearGradient\n              id={`H-${id}`}\n              x1=\"-1016.59\"\n              x2=\"-1017.862\"\n              y1=\"852.671\"\n              y2=\"852.595\"\n              gradientTransform=\"matrix(29 0 0 -8 29523 6971)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stopColor=\"#ffa400\"></stop>\n              <stop offset=\"1\" stopColor=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#H-${id})`}\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"bevel\"\n              strokeWidth=\"12\"\n              d=\"M34 146l-29 8\"\n            ></path>\n            <linearGradient\n              id={`I-${id}`}\n              x1=\"-1011.984\"\n              x2=\"-1013.257\"\n              y1=\"863.523\"\n              y2=\"863.229\"\n              gradientTransform=\"matrix(24 0 0 -13 24339 11407)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stopColor=\"#ffa400\"></stop>\n              <stop offset=\"1\" stopColor=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#I-${id})`}\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"bevel\"\n              strokeWidth=\"12\"\n              d=\"M45 177l-24 13\"\n            ></path>\n            <linearGradient\n              id={`J-${id}`}\n              x1=\"-1006.673\"\n              x2=\"-1007.946\"\n              y1=\"869.279\"\n              y2=\"868.376\"\n              gradientTransform=\"matrix(20 0 0 -19 20205 16720)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stopColor=\"#ffa400\"></stop>\n              <stop offset=\"1\" stopColor=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#J-${id})`}\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"bevel\"\n              strokeWidth=\"12\"\n              d=\"M67 204l-20 19\"\n            ></path>\n            <linearGradient\n              id={`K-${id}`}\n              x1=\"-992.85\"\n              x2=\"-993.317\"\n              y1=\"871.258\"\n              y2=\"870.258\"\n              gradientTransform=\"matrix(13.8339 0 0 -22.8467 13825.796 20131.938)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stopColor=\"#ffa400\"></stop>\n              <stop offset=\"1\" stopColor=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#K-${id})`}\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"bevel\"\n              strokeWidth=\"12\"\n              d=\"M94.4 227l-13.8 22.8\"\n            ></path>\n            <linearGradient\n              id={`L-${id}`}\n              x1=\"-953.835\"\n              x2=\"-953.965\"\n              y1=\"871.9\"\n              y2=\"870.9\"\n              gradientTransform=\"matrix(7.5 0 0 -24.5 7278 21605)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stopColor=\"#ffa400\"></stop>\n              <stop offset=\"1\" stopColor=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#L-${id})`}\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"bevel\"\n              strokeWidth=\"12\"\n              d=\"M127.5 243.5L120 268\"\n            ></path>\n            <linearGradient\n              id={`M-${id}`}\n              x1=\"244.504\"\n              x2=\"244.496\"\n              y1=\"871.898\"\n              y2=\"870.898\"\n              gradientTransform=\"matrix(.5 0 0 -24.5 45.5 21614)\"\n              gradientUnits=\"userSpaceOnUse\"\n            >\n              <stop offset=\"0\" stopColor=\"#ffa400\"></stop>\n              <stop offset=\"1\" stopColor=\"#ff5e00\"></stop>\n            </linearGradient>\n            <path\n              fill=\"none\"\n              stroke={`url(#M-${id})`}\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"bevel\"\n              strokeWidth=\"12\"\n              d=\"M167.5 252.5l.5 24.5\"\n            ></path>\n          </g>\n        </g>\n      </g>\n    </svg>\n  )\n}\n", "import React from 'react'\nimport {\n  invariant,\n  AnyRouter,\n  Route,\n  AnyRoute,\n  AnyRoot<PERSON>oute,\n  trimPath,\n  useRouter,\n  useRouterState,\n  AnyRouteMatch,\n  rootRouteId,\n} from '@tanstack/react-router'\n\nimport useLocalStorage from './useLocalStorage'\nimport {\n  getRouteStatusColor,\n  getStatusColor,\n  multiSortBy,\n  useIsMounted,\n  useSafeState,\n} from './utils'\nimport { css } from 'goober'\nimport { clsx as cx } from 'clsx'\nimport Explorer from './Explorer'\nimport { tokens } from './tokens'\nimport { TanStackLogo } from './logo'\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\n\ninterface DevtoolsOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * Use this to add props to the panel. For example, you can add className, style (merge and override default style), etc.\n   */\n  panelProps?: React.DetailedHTMLProps<\n    React.HTMLAttributes<HTMLDivElement>,\n    HTMLDivElement\n  >\n  /**\n   * Use this to add props to the close button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: React.DetailedHTMLProps<\n    React.ButtonHTMLAttributes<HTMLButtonElement>,\n    HTMLButtonElement\n  >\n  /**\n   * Use this to add props to the toggle button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  toggleButtonProps?: React.DetailedHTMLProps<\n    React.ButtonHTMLAttributes<HTMLButtonElement>,\n    HTMLButtonElement\n  >\n  /**\n   * The position of the TanStack Router logo to open and close the devtools panel.\n   * Defaults to 'bottom-left'.\n   */\n  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\n  /**\n   * Use this to render the devtools inside a different type of container element for a11y purposes.\n   * Any string which corresponds to a valid intrinsic JSX element is allowed.\n   * Defaults to 'footer'.\n   */\n  containerElement?: string | any\n  /**\n   * A boolean variable indicating if the \"lite\" version of the library is being used\n   */\n  router?: AnyRouter\n}\n\ninterface DevtoolsPanelOptions {\n  /**\n   * The standard React style object used to style a component with inline styles\n   */\n  style?: React.CSSProperties\n  /**\n   * The standard React className property used to style a component with classes\n   */\n  className?: string\n  /**\n   * A boolean variable indicating whether the panel is open or closed\n   */\n  isOpen?: boolean\n  /**\n   * A function that toggles the open and close state of the panel\n   */\n  setIsOpen: (isOpen: boolean) => void\n  /**\n   * Handles the opening and closing the devtools panel\n   */\n  handleDragStart?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void\n  /**\n   * A boolean variable indicating if the \"lite\" version of the library is being used\n   */\n  router?: AnyRouter\n}\n\nconst isServer = typeof window === 'undefined'\n\nfunction Logo(props: React.HTMLAttributes<HTMLButtonElement>) {\n  const { className, ...rest } = props\n  return (\n    <button {...rest} className={cx(getStyles().logo, className)}>\n      <div className={getStyles().tanstackLogo}>TANSTACK</div>\n      <div className={getStyles().routerLogo}>React Router v1</div>\n    </button>\n  )\n}\n\nconst DevtoolsOnCloseContext = React.createContext<\n  | {\n      onCloseClick: (e: React.MouseEvent<HTMLButtonElement>) => void\n    }\n  | undefined\n>(undefined)\n\nconst useDevtoolsOnClose = () => {\n  const context = React.useContext(DevtoolsOnCloseContext)\n  if (!context) {\n    throw new Error(\n      'useDevtoolsOnClose must be used within a TanStackRouterDevtools component',\n    )\n  }\n  return context\n}\n\nexport function TanStackRouterDevtools({\n  initialIsOpen,\n  panelProps = {},\n  closeButtonProps = {},\n  toggleButtonProps = {},\n  position = 'bottom-left',\n  containerElement: Container = 'footer',\n  router,\n}: DevtoolsOptions): React.ReactElement | null {\n  const [rootEl, setRootEl] = React.useState<HTMLDivElement>(null!)\n  const panelRef = React.useRef<HTMLDivElement>(null)\n  const [isOpen, setIsOpen] = useLocalStorage(\n    'tanstackRouterDevtoolsOpen',\n    initialIsOpen,\n  )\n  const [devtoolsHeight, setDevtoolsHeight] = useLocalStorage<number | null>(\n    'tanstackRouterDevtoolsHeight',\n    null,\n  )\n  const [isResolvedOpen, setIsResolvedOpen] = useSafeState(false)\n  const [isResizing, setIsResizing] = useSafeState(false)\n  const isMounted = useIsMounted()\n\n  const handleDragStart = (\n    panelElement: HTMLDivElement | null,\n    startEvent: React.MouseEvent<HTMLDivElement, MouseEvent>,\n  ) => {\n    if (startEvent.button !== 0) return // Only allow left click for drag\n\n    setIsResizing(true)\n\n    const dragInfo = {\n      originalHeight: panelElement?.getBoundingClientRect().height ?? 0,\n      pageY: startEvent.pageY,\n    }\n\n    const run = (moveEvent: MouseEvent) => {\n      const delta = dragInfo.pageY - moveEvent.pageY\n      const newHeight = dragInfo?.originalHeight + delta\n\n      setDevtoolsHeight(newHeight)\n\n      if (newHeight < 70) {\n        setIsOpen(false)\n      } else {\n        setIsOpen(true)\n      }\n    }\n\n    const unsub = () => {\n      setIsResizing(false)\n      document.removeEventListener('mousemove', run)\n      document.removeEventListener('mouseUp', unsub)\n    }\n\n    document.addEventListener('mousemove', run)\n    document.addEventListener('mouseup', unsub)\n  }\n\n  const isButtonClosed = isOpen ?? false\n\n  React.useEffect(() => {\n    setIsResolvedOpen(isOpen ?? false)\n  }, [isOpen, isResolvedOpen, setIsResolvedOpen])\n\n  React.useEffect(() => {\n    if (isResolvedOpen) {\n      const previousValue = rootEl?.parentElement?.style.paddingBottom\n\n      const run = () => {\n        const containerHeight = panelRef.current?.getBoundingClientRect().height\n        if (rootEl?.parentElement) {\n          rootEl.parentElement.style.paddingBottom = `${containerHeight}px`\n        }\n      }\n\n      run()\n\n      if (typeof window !== 'undefined') {\n        window.addEventListener('resize', run)\n\n        return () => {\n          window.removeEventListener('resize', run)\n          if (rootEl?.parentElement && typeof previousValue === 'string') {\n            rootEl.parentElement.style.paddingBottom = previousValue\n          }\n        }\n      }\n    }\n    return\n  }, [isResolvedOpen])\n\n  React.useEffect(() => {\n    if (rootEl) {\n      const el = rootEl\n      const fontSize = getComputedStyle(el).fontSize\n      el.style.setProperty('--tsrd-font-size', fontSize)\n    }\n  }, [rootEl])\n\n  const { style: panelStyle = {}, ...otherPanelProps } = panelProps\n\n  const {\n    style: closeButtonStyle = {},\n    onClick: onCloseClick,\n    ...otherCloseButtonProps\n  } = closeButtonProps\n\n  const {\n    style: toggleButtonStyle = {},\n    onClick: onToggleClick,\n    ...otherToggleButtonProps\n  } = toggleButtonProps\n\n  // Do not render on the server\n  if (!isMounted()) return null\n\n  const resolvedHeight = devtoolsHeight ?? 500\n\n  return (\n    <Container ref={setRootEl} className=\"TanStackRouterDevtools\">\n      <DevtoolsOnCloseContext.Provider\n        value={{\n          onCloseClick: onCloseClick ?? (() => {}),\n        }}\n      >\n        <TanStackRouterDevtoolsPanel\n          ref={panelRef as any}\n          {...otherPanelProps}\n          router={router}\n          className={cx(\n            getStyles().devtoolsPanelContainer,\n            getStyles().devtoolsPanelContainerVisibility(!!isOpen),\n            getStyles().devtoolsPanelContainerResizing(isResizing),\n            getStyles().devtoolsPanelContainerAnimation(\n              isResolvedOpen,\n              resolvedHeight + 16,\n            ),\n          )}\n          style={{\n            height: resolvedHeight,\n            ...panelStyle,\n          }}\n          isOpen={isResolvedOpen}\n          setIsOpen={setIsOpen}\n          handleDragStart={(e) => handleDragStart(panelRef.current, e)}\n        />\n      </DevtoolsOnCloseContext.Provider>\n\n      <button\n        type=\"button\"\n        {...otherToggleButtonProps}\n        aria-label=\"Open TanStack Router Devtools\"\n        onClick={(e) => {\n          setIsOpen(true)\n          onToggleClick && onToggleClick(e)\n        }}\n        className={cx(\n          getStyles().mainCloseBtn,\n          getStyles().mainCloseBtnPosition(position),\n          getStyles().mainCloseBtnAnimation(!isButtonClosed),\n        )}\n      >\n        <div className={getStyles().mainCloseBtnIconContainer}>\n          <div className={getStyles().mainCloseBtnIconOuter}>\n            <TanStackLogo />\n          </div>\n          <div className={getStyles().mainCloseBtnIconInner}>\n            <TanStackLogo />\n          </div>\n        </div>\n        <div className={getStyles().mainCloseBtnDivider}>-</div>\n        <div className={getStyles().routerLogoCloseButton}>TanStack Router</div>\n      </button>\n    </Container>\n  )\n}\n\nfunction RouteComp({\n  route,\n  isRoot,\n  activeId,\n  setActiveId,\n}: {\n  route: AnyRootRoute | AnyRoute\n  isRoot?: boolean\n  activeId: string | undefined\n  setActiveId: (id: string) => void\n}) {\n  const routerState = useRouterState()\n  const matches =\n    routerState.status === 'pending'\n      ? routerState.pendingMatches ?? []\n      : routerState.matches\n\n  const match = routerState.matches.find((d) => d.routeId === route.id)\n\n  const param = React.useMemo(() => {\n    try {\n      if (match?.params) {\n        const p = match.params\n        const r: string = route.path || trimPath(route.id)\n        if (r.startsWith('$')) {\n          const trimmed = r.slice(1)\n          if (p[trimmed]) {\n            return `(${p[trimmed]})`\n          }\n        }\n      }\n      return ''\n    } catch (error) {\n      return ''\n    }\n  }, [match, route])\n\n  return (\n    <div>\n      <div\n        role=\"button\"\n        aria-label={`Open match details for ${route.id}`}\n        onClick={() => {\n          if (match) {\n            setActiveId(activeId === route.id ? '' : route.id)\n          }\n        }}\n        className={cx(\n          getStyles().routesRowContainer(route.id === activeId, !!match),\n        )}\n      >\n        <div\n          className={cx(\n            getStyles().matchIndicator(getRouteStatusColor(matches, route)),\n          )}\n        />\n        <div className={cx(getStyles().routesRow(!!match))}>\n          <div>\n            <code className={getStyles().code}>\n              {isRoot ? rootRouteId : route.path || trimPath(route.id)}{' '}\n            </code>\n            <code className={getStyles().routeParamInfo}>{param}</code>\n          </div>\n          <AgeTicker match={match} />\n        </div>\n      </div>\n      {(route.children as Route[])?.length ? (\n        <div className={getStyles().nestedRouteRow(!!isRoot)}>\n          {[...(route.children as Route[])]\n            .sort((a, b) => {\n              return a.rank - b.rank\n            })\n            .map((r) => (\n              <RouteComp\n                key={r.id}\n                route={r}\n                activeId={activeId}\n                setActiveId={setActiveId}\n              />\n            ))}\n        </div>\n      ) : null}\n    </div>\n  )\n}\n\nexport const TanStackRouterDevtoolsPanel = React.forwardRef<\n  HTMLDivElement,\n  DevtoolsPanelOptions\n>(function TanStackRouterDevtoolsPanel(props, ref): React.ReactElement {\n  const {\n    isOpen = true,\n    setIsOpen,\n    handleDragStart,\n    router: userRouter,\n    ...panelProps\n  } = props\n\n  const { onCloseClick } = useDevtoolsOnClose()\n  const { className, ...otherPanelProps } = panelProps\n\n  const contextRouter = useRouter({ warn: false })\n  const router = userRouter ?? contextRouter\n  const routerState = useRouterState({\n    router,\n  } as any)\n\n  const matches = [\n    ...(routerState.pendingMatches ?? []),\n    ...routerState.matches,\n    ...routerState.cachedMatches,\n  ]\n\n  invariant(\n    router,\n    'No router was found for the TanStack Router Devtools. Please place the devtools in the <RouterProvider> component tree or pass the router instance to the devtools manually.',\n  )\n\n  // useStore(router.__store)\n\n  const [showMatches, setShowMatches] = useLocalStorage(\n    'tanstackRouterDevtoolsShowMatches',\n    true,\n  )\n\n  const [activeId, setActiveId] = useLocalStorage(\n    'tanstackRouterDevtoolsActiveRouteId',\n    '',\n  )\n\n  const activeMatch = React.useMemo(\n    () => matches.find((d) => d.routeId === activeId || d.id === activeId),\n    [matches, activeId],\n  )\n\n  const hasSearch = Object.keys(routerState.location.search || {}).length\n\n  const explorerState = {\n    ...router,\n    state: router.state,\n  }\n\n  return (\n    <div\n      ref={ref}\n      className={cx(\n        getStyles().devtoolsPanel,\n        'TanStackRouterDevtoolsPanel',\n        className,\n      )}\n      {...otherPanelProps}\n    >\n      {handleDragStart ? (\n        <div\n          className={getStyles().dragHandle}\n          onMouseDown={handleDragStart}\n        ></div>\n      ) : null}\n      <button\n        className={getStyles().panelCloseBtn}\n        onClick={(e) => {\n          setIsOpen(false)\n          onCloseClick(e)\n        }}\n      >\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          width=\"10\"\n          height=\"6\"\n          fill=\"none\"\n          viewBox=\"0 0 10 6\"\n          className={getStyles().panelCloseBtnIcon}\n        >\n          <path\n            stroke=\"currentColor\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth=\"1.667\"\n            d=\"M1 1l4 4 4-4\"\n          ></path>\n        </svg>\n      </button>\n      <div className={getStyles().firstContainer}>\n        <div className={getStyles().row}>\n          <Logo\n            aria-hidden\n            onClick={(e) => {\n              setIsOpen(false)\n              onCloseClick(e)\n            }}\n          />\n        </div>\n        <div className={getStyles().routerExplorerContainer}>\n          <div className={getStyles().routerExplorer}>\n            <Explorer\n              label=\"Router\"\n              value={Object.fromEntries(\n                multiSortBy(\n                  Object.keys(explorerState),\n                  (\n                    [\n                      'state',\n                      'routesById',\n                      'routesByPath',\n                      'flatRoutes',\n                      'options',\n                    ] as const\n                  ).map((d) => (dd) => dd !== d),\n                )\n                  .map((key) => [key, (explorerState as any)[key]])\n                  .filter(\n                    (d) =>\n                      typeof d[1] !== 'function' &&\n                      ![\n                        '__store',\n                        'basepath',\n                        'injectedHtml',\n                        'subscribers',\n                        'latestLoadPromise',\n                        'navigateTimeout',\n                        'resetNextScroll',\n                        'tempLocationKey',\n                        'latestLocation',\n                        'routeTree',\n                        'history',\n                      ].includes(d[0]),\n                  ),\n              )}\n              defaultExpanded={{\n                state: {} as any,\n                context: {} as any,\n                options: {} as any,\n              }}\n              filterSubEntries={(subEntries) => {\n                return subEntries.filter((d) => typeof d.value !== 'function')\n              }}\n            />\n          </div>\n        </div>\n      </div>\n      <div className={getStyles().secondContainer}>\n        <div className={getStyles().matchesContainer}>\n          <div className={getStyles().detailsHeader}>\n            <span>Pathname</span>\n            {routerState.location.maskedLocation ? (\n              <div className={getStyles().maskedBadgeContainer}>\n                <span className={getStyles().maskedBadge}>masked</span>\n              </div>\n            ) : null}\n          </div>\n          <div className={getStyles().detailsContent}>\n            <code>{routerState.location.pathname}</code>\n            {routerState.location.maskedLocation ? (\n              <code className={getStyles().maskedLocation}>\n                {routerState.location.maskedLocation.pathname}\n              </code>\n            ) : null}\n          </div>\n          <div className={getStyles().detailsHeader}>\n            <div className={getStyles().routeMatchesToggle}>\n              <button\n                type=\"button\"\n                onClick={() => {\n                  setShowMatches(false)\n                }}\n                disabled={!showMatches}\n                className={cx(\n                  getStyles().routeMatchesToggleBtn(!showMatches, true),\n                )}\n              >\n                Routes\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => {\n                  setShowMatches(true)\n                }}\n                disabled={showMatches}\n                className={cx(\n                  getStyles().routeMatchesToggleBtn(!!showMatches, false),\n                )}\n              >\n                Matches\n              </button>\n            </div>\n            <div className={getStyles().detailsHeaderInfo}>\n              <div>age / staleTime / gcTime</div>\n            </div>\n          </div>\n          <div className={cx(getStyles().routesContainer)}>\n            {!showMatches ? (\n              <RouteComp\n                route={router.routeTree}\n                isRoot\n                activeId={activeId}\n                setActiveId={setActiveId}\n              />\n            ) : (\n              <div>\n                {(routerState.status === 'pending'\n                  ? routerState.pendingMatches ?? []\n                  : routerState.matches\n                ).map((match, i) => {\n                  return (\n                    <div\n                      key={match.id || i}\n                      role=\"button\"\n                      aria-label={`Open match details for ${match.id}`}\n                      onClick={() =>\n                        setActiveId(activeId === match.id ? '' : match.id)\n                      }\n                      className={cx(\n                        getStyles().matchRow(match === activeMatch),\n                      )}\n                    >\n                      <div\n                        className={cx(\n                          getStyles().matchIndicator(getStatusColor(match)),\n                        )}\n                      />\n\n                      <code\n                        className={getStyles().matchID}\n                      >{`${match.routeId === rootRouteId ? rootRouteId : match.pathname}`}</code>\n                      <AgeTicker match={match} />\n                    </div>\n                  )\n                })}\n              </div>\n            )}\n          </div>\n        </div>\n        {routerState.cachedMatches?.length ? (\n          <div className={getStyles().cachedMatchesContainer}>\n            <div className={getStyles().detailsHeader}>\n              <div>Cached Matches</div>\n              <div className={getStyles().detailsHeaderInfo}>\n                age / staleTime / gcTime\n              </div>\n            </div>\n            <div>\n              {routerState.cachedMatches.map((match) => {\n                return (\n                  <div\n                    key={match.id}\n                    role=\"button\"\n                    aria-label={`Open match details for ${match.id}`}\n                    onClick={() =>\n                      setActiveId(activeId === match.id ? '' : match.id)\n                    }\n                    className={cx(getStyles().matchRow(match === activeMatch))}\n                  >\n                    <div\n                      className={cx(\n                        getStyles().matchIndicator(getStatusColor(match)),\n                      )}\n                    />\n\n                    <code className={getStyles().matchID}>{`${match.id}`}</code>\n\n                    <AgeTicker match={match} />\n                  </div>\n                )\n              })}\n            </div>\n          </div>\n        ) : null}\n      </div>\n      {activeMatch ? (\n        <div className={getStyles().thirdContainer}>\n          <div className={getStyles().detailsHeader}>Match Details</div>\n          <div>\n            <div className={getStyles().matchDetails}>\n              <div\n                className={getStyles().matchStatus(\n                  activeMatch.status,\n                  activeMatch.isFetching,\n                )}\n              >\n                <div>\n                  {activeMatch.status === 'success' && activeMatch.isFetching\n                    ? 'fetching'\n                    : activeMatch.status}\n                </div>\n              </div>\n              <div className={getStyles().matchDetailsInfoLabel}>\n                <div>ID:</div>\n                <div className={getStyles().matchDetailsInfo}>\n                  <code>{activeMatch.id}</code>\n                </div>\n              </div>\n              <div className={getStyles().matchDetailsInfoLabel}>\n                <div>State:</div>\n                <div className={getStyles().matchDetailsInfo}>\n                  {routerState.pendingMatches?.find(\n                    (d) => d.id === activeMatch.id,\n                  )\n                    ? 'Pending'\n                    : routerState.matches?.find((d) => d.id === activeMatch.id)\n                      ? 'Active'\n                      : 'Cached'}\n                </div>\n              </div>\n              <div className={getStyles().matchDetailsInfoLabel}>\n                <div>Last Updated:</div>\n                <div className={getStyles().matchDetailsInfo}>\n                  {activeMatch.updatedAt\n                    ? new Date(\n                        activeMatch.updatedAt as number,\n                      ).toLocaleTimeString()\n                    : 'N/A'}\n                </div>\n              </div>\n            </div>\n          </div>\n          {activeMatch.loaderData ? (\n            <>\n              <div className={getStyles().detailsHeader}>Loader Data</div>\n              <div className={getStyles().detailsContent}>\n                <Explorer\n                  label=\"loaderData\"\n                  value={activeMatch.loaderData}\n                  defaultExpanded={{}}\n                />\n              </div>\n            </>\n          ) : null}\n          <div className={getStyles().detailsHeader}>Explorer</div>\n          <div className={getStyles().detailsContent}>\n            <Explorer label=\"Match\" value={activeMatch} defaultExpanded={{}} />\n          </div>\n        </div>\n      ) : null}\n      {hasSearch ? (\n        <div className={getStyles().fourthContainer}>\n          <div className={getStyles().detailsHeader}>Search Params</div>\n          <div className={getStyles().detailsContent}>\n            <Explorer\n              value={routerState.location.search || {}}\n              defaultExpanded={Object.keys(\n                (routerState.location.search as {}) || {},\n              ).reduce((obj: any, next) => {\n                obj[next] = {}\n                return obj\n              }, {})}\n            />\n          </div>\n        </div>\n      ) : null}\n    </div>\n  )\n})\n\nfunction AgeTicker({ match }: { match?: AnyRouteMatch }) {\n  const router = useRouter()\n\n  const rerender = React.useReducer(\n    () => ({}),\n    () => ({}),\n  )[1]\n\n  React.useEffect(() => {\n    const interval = setInterval(() => {\n      rerender()\n    }, 1000)\n\n    return () => {\n      clearInterval(interval)\n    }\n  }, [])\n\n  if (!match) {\n    return null\n  }\n\n  const route = router.looseRoutesById[match?.routeId]!\n\n  if (!route.options.loader) {\n    return null\n  }\n\n  const age = Date.now() - match?.updatedAt\n  const staleTime =\n    route.options.staleTime ?? router.options.defaultStaleTime ?? 0\n  const gcTime =\n    route.options.gcTime ?? router.options.defaultGcTime ?? 30 * 60 * 1000\n\n  return (\n    <div className={cx(getStyles().ageTicker(age > staleTime))}>\n      <div>{formatTime(age)}</div>\n      <div>/</div>\n      <div>{formatTime(staleTime)}</div>\n      <div>/</div>\n      <div>{formatTime(gcTime)}</div>\n    </div>\n  )\n}\n\nfunction formatTime(ms: number) {\n  const units = ['s', 'min', 'h', 'd']\n  const values = [ms / 1000, ms / 60000, ms / 3600000, ms / 86400000]\n\n  let chosenUnitIndex = 0\n  for (let i = 1; i < values.length; i++) {\n    if (values[i]! < 1) break\n    chosenUnitIndex = i\n  }\n\n  const formatter = new Intl.NumberFormat(navigator.language, {\n    compactDisplay: 'short',\n    notation: 'compact',\n    maximumFractionDigits: 0,\n  })\n\n  return formatter.format(values[chosenUnitIndex]!) + units[chosenUnitIndex]\n}\n\nconst stylesFactory = () => {\n  const { colors, font, size, alpha, shadow, border } = tokens\n  const { fontFamily, lineHeight, size: fontSize } = font\n\n  return {\n    devtoolsPanelContainer: css`\n      direction: ltr;\n      position: fixed;\n      bottom: 0;\n      right: 0;\n      z-index: 99999;\n      width: 100%;\n      max-height: 90%;\n      border-top: 1px solid ${colors.gray[700]};\n      transform-origin: top;\n    `,\n    devtoolsPanelContainerVisibility: (isOpen: boolean) => {\n      return css`\n        visibility: ${isOpen ? 'visible' : 'hidden'};\n      `\n    },\n    devtoolsPanelContainerResizing: (isResizing: boolean) => {\n      if (isResizing) {\n        return css`\n          transition: none;\n        `\n      }\n\n      return css`\n        transition: all 0.4s ease;\n      `\n    },\n    devtoolsPanelContainerAnimation: (isOpen: boolean, height: number) => {\n      if (isOpen) {\n        return css`\n          pointer-events: auto;\n          transform: translateY(0);\n        `\n      }\n      return css`\n        pointer-events: none;\n        transform: translateY(${height}px);\n      `\n    },\n    logo: css`\n      cursor: pointer;\n      display: flex;\n      flex-direction: column;\n      background-color: transparent;\n      border: none;\n      font-family: ${fontFamily.sans};\n      gap: ${tokens.size[0.5]};\n      padding: 0px;\n      &:hover {\n        opacity: 0.7;\n      }\n      &:focus-visible {\n        outline-offset: 4px;\n        border-radius: ${border.radius.xs};\n        outline: 2px solid ${colors.blue[800]};\n      }\n    `,\n    tanstackLogo: css`\n      font-size: ${font.size.md};\n      font-weight: ${font.weight.bold};\n      line-height: ${font.lineHeight.xs};\n      white-space: nowrap;\n      color: ${colors.gray[300]};\n    `,\n    routerLogo: css`\n      font-weight: ${font.weight.semibold};\n      font-size: ${font.size.xs};\n      background: linear-gradient(to right, #84cc16, #10b981);\n      background-clip: text;\n      -webkit-background-clip: text;\n      line-height: 1;\n      -webkit-text-fill-color: transparent;\n      white-space: nowrap;\n    `,\n    devtoolsPanel: css`\n      display: flex;\n      font-size: ${fontSize.sm};\n      font-family: ${fontFamily.sans};\n      background-color: ${colors.darkGray[700]};\n      color: ${colors.gray[300]};\n\n      @media (max-width: 700px) {\n        flex-direction: column;\n      }\n      @media (max-width: 600px) {\n        font-size: ${fontSize.xs};\n      }\n    `,\n    dragHandle: css`\n      position: absolute;\n      left: 0;\n      top: 0;\n      width: 100%;\n      height: 4px;\n      cursor: row-resize;\n      z-index: 100000;\n      &:hover {\n        background-color: ${colors.purple[400]}${alpha[90]};\n      }\n    `,\n    firstContainer: css`\n      flex: 1 1 500px;\n      min-height: 40%;\n      max-height: 100%;\n      overflow: auto;\n      border-right: 1px solid ${colors.gray[700]};\n      display: flex;\n      flex-direction: column;\n    `,\n    routerExplorerContainer: css`\n      overflow-y: auto;\n      flex: 1;\n    `,\n    routerExplorer: css`\n      padding: ${tokens.size[2]};\n    `,\n    row: css`\n      display: flex;\n      align-items: center;\n      padding: ${tokens.size[2]} ${tokens.size[2.5]};\n      gap: ${tokens.size[2.5]};\n      border-bottom: ${colors.darkGray[500]} 1px solid;\n      align-items: center;\n    `,\n    detailsHeader: css`\n      font-family: ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;\n      position: sticky;\n      top: 0;\n      z-index: 2;\n      background-color: ${colors.darkGray[600]};\n      padding: 0px ${tokens.size[2]};\n      font-weight: ${font.weight.medium};\n      font-size: ${font.size.xs};\n      min-height: ${tokens.size[8]};\n      line-height: ${font.lineHeight.xs};\n      text-align: left;\n      display: flex;\n      align-items: center;\n    `,\n    maskedBadge: css`\n      background: ${colors.yellow[900]}${alpha[70]};\n      color: ${colors.yellow[300]};\n      display: inline-block;\n      padding: ${tokens.size[0]} ${tokens.size[2.5]};\n      border-radius: ${border.radius.full};\n      font-size: ${font.size.xs};\n      font-weight: ${font.weight.normal};\n      border: 1px solid ${colors.yellow[300]};\n    `,\n    maskedLocation: css`\n      color: ${colors.yellow[300]};\n    `,\n    detailsContent: css`\n      padding: ${tokens.size[1.5]} ${tokens.size[2]};\n      display: flex;\n      align-items: center;\n      font-size: ${font.size.xs};\n    `,\n    routeMatchesToggle: css`\n      display: flex;\n      align-items: center;\n      border: 1px solid ${colors.gray[500]};\n      border-radius: ${border.radius.sm};\n      overflow: hidden;\n    `,\n    routeMatchesToggleBtn: (active: boolean, showBorder: boolean) => {\n      const base = css`\n        appearance: none;\n        border: none;\n        font-size: 12px;\n        padding: 4px 8px;\n        background: transparent;\n        cursor: pointer;\n        font-family: ${fontFamily.sans};\n        font-weight: ${font.weight.medium};\n      `\n      const classes = [base]\n\n      if (active) {\n        const activeStyles = css`\n          background: ${colors.darkGray[400]};\n          color: ${colors.gray[300]};\n        `\n        classes.push(activeStyles)\n      } else {\n        const inactiveStyles = css`\n          color: ${colors.gray[500]};\n          background: ${colors.darkGray[800]}${alpha[20]};\n        `\n        classes.push(inactiveStyles)\n      }\n\n      if (showBorder) {\n        const border = css`\n          border-right: 1px solid ${tokens.colors.gray[500]};\n        `\n        classes.push(border)\n      }\n\n      return classes\n    },\n    detailsHeaderInfo: css`\n      flex: 1;\n      justify-content: flex-end;\n      display: flex;\n      align-items: center;\n      font-weight: ${font.weight.normal};\n      color: ${colors.gray[400]};\n    `,\n    matchRow: (active: boolean) => {\n      const base = css`\n        display: flex;\n        border-bottom: 1px solid ${colors.darkGray[400]};\n        cursor: pointer;\n        align-items: center;\n        padding: ${size[1]} ${size[2]};\n        gap: ${size[2]};\n        font-size: ${fontSize.xs};\n        color: ${colors.gray[300]};\n      `\n      const classes = [base]\n\n      if (active) {\n        const activeStyles = css`\n          background: ${colors.darkGray[500]};\n        `\n        classes.push(activeStyles)\n      }\n\n      return classes\n    },\n    matchIndicator: (color: 'green' | 'red' | 'yellow' | 'gray' | 'blue') => {\n      const base = css`\n        flex: 0 0 auto;\n        width: ${size[3]};\n        height: ${size[3]};\n        background: ${colors[color][900]};\n        border: 1px solid ${colors[color][500]};\n        border-radius: ${border.radius.full};\n        transition: all 0.25s ease-out;\n        box-sizing: border-box;\n      `\n      const classes = [base]\n\n      if (color === 'gray') {\n        const grayStyles = css`\n          background: ${colors.gray[700]};\n          border-color: ${colors.gray[400]};\n        `\n        classes.push(grayStyles)\n      }\n\n      return classes\n    },\n    matchID: css`\n      flex: 1;\n      line-height: ${lineHeight['xs']};\n    `,\n    ageTicker: (showWarning: boolean) => {\n      const base = css`\n        display: flex;\n        gap: ${size[1]};\n        font-size: ${fontSize.xs};\n        color: ${colors.gray[400]};\n        font-variant-numeric: tabular-nums;\n        line-height: ${lineHeight['xs']};\n      `\n\n      const classes = [base]\n\n      if (showWarning) {\n        const warningStyles = css`\n          color: ${colors.yellow[400]};\n        `\n        classes.push(warningStyles)\n      }\n\n      return classes\n    },\n    secondContainer: css`\n      flex: 1 1 500px;\n      min-height: 40%;\n      max-height: 100%;\n      overflow: auto;\n      border-right: 1px solid ${colors.gray[700]};\n      display: flex;\n      flex-direction: column;\n    `,\n    thirdContainer: css`\n      flex: 1 1 500px;\n      overflow: auto;\n      display: flex;\n      flex-direction: column;\n      height: 100%;\n      border-right: 1px solid ${colors.gray[700]};\n\n      @media (max-width: 700px) {\n        border-top: 2px solid ${colors.gray[700]};\n      }\n    `,\n    fourthContainer: css`\n      flex: 1 1 500px;\n      min-height: 40%;\n      max-height: 100%;\n      overflow: auto;\n      display: flex;\n      flex-direction: column;\n    `,\n    routesContainer: css`\n      overflow-x: auto;\n      overflow-y: visible;\n    `,\n    routesRowContainer: (active: boolean, isMatch: boolean) => {\n      const base = css`\n        display: flex;\n        border-bottom: 1px solid ${colors.darkGray[400]};\n        align-items: center;\n        padding: ${size[1]} ${size[2]};\n        gap: ${size[2]};\n        font-size: ${fontSize.xs};\n        color: ${colors.gray[300]};\n        cursor: ${isMatch ? 'pointer' : 'default'};\n        line-height: ${lineHeight['xs']};\n      `\n      const classes = [base]\n\n      if (active) {\n        const activeStyles = css`\n          background: ${colors.darkGray[500]};\n        `\n        classes.push(activeStyles)\n      }\n\n      return classes\n    },\n    routesRow: (isMatch: boolean) => {\n      const base = css`\n        flex: 1 0 auto;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        font-size: ${fontSize.xs};\n        line-height: ${lineHeight['xs']};\n      `\n\n      const classes = [base]\n\n      if (!isMatch) {\n        const matchStyles = css`\n          color: ${colors.gray[400]};\n        `\n        classes.push(matchStyles)\n      }\n\n      return classes\n    },\n    routeParamInfo: css`\n      color: ${colors.gray[400]};\n      font-size: ${fontSize.xs};\n      line-height: ${lineHeight['xs']};\n    `,\n    nestedRouteRow: (isRoot: boolean) => {\n      const base = css`\n        margin-left: ${isRoot ? 0 : size[3.5]};\n        border-left: ${isRoot ? '' : `solid 1px ${colors.gray[700]}`};\n      `\n      return base\n    },\n    code: css`\n      font-size: ${fontSize.xs};\n      line-height: ${lineHeight['xs']};\n    `,\n    matchesContainer: css`\n      flex: 1 1 auto;\n      overflow-y: auto;\n    `,\n    cachedMatchesContainer: css`\n      flex: 1 1 auto;\n      overflow-y: auto;\n      max-height: 50%;\n    `,\n    maskedBadgeContainer: css`\n      flex: 1;\n      justify-content: flex-end;\n      display: flex;\n    `,\n    matchDetails: css`\n      display: flex;\n      flex-direction: column;\n      padding: ${tokens.size[2]};\n      font-size: ${tokens.font.size.xs};\n      color: ${tokens.colors.gray[300]};\n      line-height: ${tokens.font.lineHeight.sm};\n    `,\n    matchStatus: (\n      status: 'pending' | 'success' | 'error' | 'notFound' | 'redirected',\n      isFetching: boolean,\n    ) => {\n      const colorMap = {\n        pending: 'yellow',\n        success: 'green',\n        error: 'red',\n        notFound: 'purple',\n        redirected: 'gray',\n      } as const\n\n      const color =\n        isFetching && status === 'success' ? 'blue' : colorMap[status]\n\n      return css`\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        height: 40px;\n        border-radius: ${tokens.border.radius.sm};\n        font-weight: ${tokens.font.weight.normal};\n        background-color: ${tokens.colors[color][900]}${tokens.alpha[90]};\n        color: ${tokens.colors[color][300]};\n        border: 1px solid ${tokens.colors[color][600]};\n        margin-bottom: ${tokens.size[2]};\n        transition: all 0.25s ease-out;\n      `\n    },\n    matchDetailsInfo: css`\n      display: flex;\n      justify-content: flex-end;\n      flex: 1;\n    `,\n    matchDetailsInfoLabel: css`\n      display: flex;\n    `,\n    mainCloseBtn: css`\n      background: ${colors.darkGray[700]};\n      padding: ${size[1]} ${size[2]} ${size[1]} ${size[1.5]};\n      border-radius: ${border.radius.md};\n      position: fixed;\n      z-index: 99999;\n      display: inline-flex;\n      width: fit-content;\n      cursor: pointer;\n      appearance: none;\n      border: 0;\n      gap: 8px;\n      align-items: center;\n      border: 1px solid ${colors.gray[500]};\n      font-size: ${font.size.xs};\n      cursor: pointer;\n      transition: all 0.25s ease-out;\n\n      &:hover {\n        background: ${colors.darkGray[500]};\n      }\n    `,\n    mainCloseBtnPosition: (\n      position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right',\n    ) => {\n      const base = css`\n        ${position === 'top-left' ? `top: ${size[2]}; left: ${size[2]};` : ''}\n        ${position === 'top-right' ? `top: ${size[2]}; right: ${size[2]};` : ''}\n        ${position === 'bottom-left'\n          ? `bottom: ${size[2]}; left: ${size[2]};`\n          : ''}\n        ${position === 'bottom-right'\n          ? `bottom: ${size[2]}; right: ${size[2]};`\n          : ''}\n      `\n      return base\n    },\n    mainCloseBtnAnimation: (isOpen: boolean) => {\n      if (isOpen) {\n        return css`\n          opacity: 1;\n          pointer-events: auto;\n          visibility: visible;\n        `\n      }\n      return css`\n        opacity: 0;\n        pointer-events: none;\n        visibility: hidden;\n      `\n    },\n    routerLogoCloseButton: css`\n      font-weight: ${font.weight.semibold};\n      font-size: ${font.size.xs};\n      background: linear-gradient(to right, #98f30c, #00f4a3);\n      background-clip: text;\n      -webkit-background-clip: text;\n      line-height: 1;\n      -webkit-text-fill-color: transparent;\n      white-space: nowrap;\n    `,\n    mainCloseBtnDivider: css`\n      width: 1px;\n      background: ${tokens.colors.gray[600]};\n      height: 100%;\n      border-radius: 999999px;\n      color: transparent;\n    `,\n    mainCloseBtnIconContainer: css`\n      position: relative;\n      width: ${size[5]};\n      height: ${size[5]};\n      background: pink;\n      border-radius: 999999px;\n      overflow: hidden;\n    `,\n    mainCloseBtnIconOuter: css`\n      width: ${size[5]};\n      height: ${size[5]};\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      filter: blur(3px) saturate(1.8) contrast(2);\n    `,\n    mainCloseBtnIconInner: css`\n      width: ${size[4]};\n      height: ${size[4]};\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n    `,\n    panelCloseBtn: css`\n      position: absolute;\n      cursor: pointer;\n      z-index: 100001;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      outline: none;\n      background-color: ${colors.darkGray[700]};\n      &:hover {\n        background-color: ${colors.darkGray[500]};\n      }\n\n      top: 0;\n      right: ${size[2]};\n      transform: translate(0, -100%);\n      border-right: ${colors.darkGray[300]} 1px solid;\n      border-left: ${colors.darkGray[300]} 1px solid;\n      border-top: ${colors.darkGray[300]} 1px solid;\n      border-bottom: none;\n      border-radius: ${border.radius.sm} ${border.radius.sm} 0px 0px;\n      padding: ${size[1]} ${size[1.5]} ${size[0.5]} ${size[1.5]};\n\n      &::after {\n        content: ' ';\n        position: absolute;\n        top: 100%;\n        left: -${size[2.5]};\n        height: ${size[1.5]};\n        width: calc(100% + ${size[5]});\n      }\n    `,\n    panelCloseBtnIcon: css`\n      color: ${colors.gray[400]};\n      width: ${size[2]};\n      height: ${size[2]};\n    `,\n  }\n}\n\nlet _styles: ReturnType<typeof stylesFactory> | null = null\n\nfunction getStyles() {\n  if (_styles) return _styles\n  _styles = stylesFactory()\n\n  return _styles\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,UAAU,CAAC,QAAyB;AACpC,MAAA;AACI,UAAA,YAAY,aAAa,QAAQ,GAAG;AACtC,QAAA,OAAO,cAAc,UAAU;AAC1B,aAAA,KAAK,MAAM,SAAS;IAC7B;AACO,WAAA;EAAA,QACD;AACC,WAAA;EACT;AACF;AAEwB,SAAA,gBACtB,KACA,cAC4D;AAC5D,QAAM,CAAC,OAAO,QAAQ,IAAIA,aAAAA,QAAM,SAAY;AAE5CA,eAAAA,QAAM,UAAU,MAAM;AACd,UAAA,eAAe,QAAQ,GAAG;AAEhC,QAAI,OAAO,iBAAiB,eAAe,iBAAiB,MAAM;AAChE;QACE,OAAO,iBAAiB,aAAa,aAAA,IAAiB;MAAA;IACxD,OACK;AACL,eAAS,YAAY;IACvB;EAAA,GACC,CAAC,cAAc,GAAG,CAAC;AAEtB,QAAM,SAASA,aAAAA,QAAM;IACnB,CAAC,YAAiB;AAChB,eAAS,CAAC,QAAQ;AAChB,YAAI,SAAS;AAET,YAAA,OAAO,WAAW,YAAY;AAChC,mBAAS,QAAQ,GAAG;QACtB;AACI,YAAA;AACF,uBAAa,QAAQ,KAAK,KAAK,UAAU,MAAM,CAAC;QAAA,QAC1C;QAAC;AAEF,eAAA;MAAA,CACR;IACH;IACA,CAAC,GAAG;EAAA;AAGC,SAAA,CAAC,OAAO,MAAM;AACvB;;;;AC7Ca,IAAA,WAAW,OAAO,WAAW;AAqBnC,SAAS,eAAe,OAAsB;AACnD,SAAO,MAAM,WAAW,aAAa,MAAM,aACvC,SACA,MAAM,WAAW,YACf,WACA,MAAM,WAAW,UACf,QACA,MAAM,WAAW,YACf,UACA;AACZ;AAEgB,SAAA,oBACd,SACA,OACA;AACM,QAAA,QAAQ,QAAQ,KAAK,CAAC,MAAM,EAAE,YAAY,MAAM,EAAE;AACxD,MAAI,CAAC;AAAc,WAAA;AACnB,SAAO,eAAe,KAAK;AAC7B;AA2CO,SAAS,eAAe;AACvB,QAAA,aAAaC,cAAAA,QAAM,OAAO,KAAK;AACrC,QAAM,YAAYA,cAAAA,QAAM,YAAY,MAAM,WAAW,SAAS,CAAA,CAAE;AAEhEA,gBAAAA,QAAM,WAAW,cAAc,iBAAiB,EAAE,MAAM;AACtD,eAAW,UAAU;AACrB,WAAO,MAAM;AACX,iBAAW,UAAU;IAAA;EAEzB,GAAG,CAAE,CAAA;AAEE,SAAA;AACT;AAMa,IAAA,eAAe,CAAC,UAAmB;AAC9C,QAAM,OAAO,OAAO,oBAAoB,OAAO,KAAK,CAAC;AAC/C,QAAA,WAAW,OAAO,UAAU,WAAW,GAAG,MAAM,SAAU,CAAA,MAAM;AAClE,MAAA;AACK,WAAA,KAAK,UAAU,UAAU,IAAI;EAAA,SAC7BC,IAAG;AACH,WAAA;EACT;AACF;AAOO,SAAS,aAAgB,cAA0C;AACxE,QAAM,YAAY,aAAA;AAClB,QAAM,CAAC,OAAO,QAAQ,IAAID,cAAAA,QAAM,SAAS,YAAY;AAErD,QAAM,eAAeA,cAAAA,QAAM;IACzB,CAAC,UAAa;AACZ,wBAAkB,MAAM;AACtB,YAAI,UAAA,GAAa;AACf,mBAAS,KAAK;QAChB;MAAA,CACD;IACH;IACA,CAAC,SAAS;EAAA;AAGL,SAAA,CAAC,OAAO,YAAY;AAC7B;AAMA,SAAS,kBAAkB,UAAsB;AAC/C,UAAQ,QAAQ,EACb,KAAK,QAAQ,EACb;IAAM,CAAC,UACN,WAAW,MAAM;AACT,YAAA;IAAA,CACP;EAAA;AAEP;AAEO,SAAS,YACd,KACA,YAAkC,CAAC,CAAC,MAAM,CAAC,GACtC;AACL,SAAO,IACJ,IAAI,CAAC,GAAGE,OAAM,CAAC,GAAGA,EAAC,CAAU,EAC7B,KAAK,CAAC,CAACC,IAAG,EAAE,GAAG,CAACC,IAAG,EAAE,MAAM;AAC1B,eAAW,YAAY,WAAW;AAC1B,YAAA,KAAK,SAASD,EAAC;AACf,YAAA,KAAK,SAASC,EAAC;AAEjB,UAAA,OAAO,OAAO,aAAa;AACzB,YAAA,OAAO,OAAO,aAAa;AAC7B;QACF;AACO,eAAA;MACT;AAEA,UAAI,OAAO,IAAI;AACb;MACF;AAEO,aAAA,KAAK,KAAK,IAAI;IACvB;AAEA,WAAO,KAAK;EACb,CAAA,EACA,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;AACnB;;;ACtLA,IAAI,IAAE,EAAC,MAAK,GAAE;AAAd,IAAgB,IAAE,CAAAC,OAAG,YAAU,OAAO,WAASA,KAAEA,GAAE,cAAc,UAAU,IAAE,OAAO,YAAU,OAAO,QAAQA,MAAG,SAAS,MAAM,YAAY,SAAS,cAAc,OAAO,CAAC,GAAE,EAAC,WAAU,KAAI,IAAG,UAAS,CAAC,GAAG,aAAWA,MAAG;AAAzN,IAAyQ,IAAE;AAA3Q,IAA+U,IAAE;AAAjV,IAAsW,IAAE;AAAxW,IAA+W,IAAE,CAACC,IAAEC,OAAI;AAAC,MAAI,IAAE,IAAGC,KAAE,IAAGC,KAAE;AAAG,WAAQC,MAAKJ,IAAE;AAAC,QAAIK,KAAEL,GAAEI,EAAC;AAAE,WAAKA,GAAE,CAAC,IAAE,OAAKA,GAAE,CAAC,IAAE,IAAEA,KAAE,MAAIC,KAAE,MAAIH,MAAG,OAAKE,GAAE,CAAC,IAAE,EAAEC,IAAED,EAAC,IAAEA,KAAE,MAAI,EAAEC,IAAE,OAAKD,GAAE,CAAC,IAAE,KAAGH,EAAC,IAAE,MAAI,YAAU,OAAOI,KAAEH,MAAG,EAAEG,IAAEJ,KAAEA,GAAE,QAAQ,YAAW,CAAAD,OAAGI,GAAE,QAAQ,mBAAkB,CAAAH,OAAG,IAAI,KAAKA,EAAC,IAAEA,GAAE,QAAQ,MAAKD,EAAC,IAAEA,KAAEA,KAAE,MAAIC,KAAEA,EAAC,CAAC,IAAEG,EAAC,IAAE,QAAMC,OAAID,KAAE,MAAM,KAAKA,EAAC,IAAEA,KAAEA,GAAE,QAAQ,UAAS,KAAK,EAAE,YAAY,GAAED,MAAG,EAAE,IAAE,EAAE,EAAEC,IAAEC,EAAC,IAAED,KAAE,MAAIC,KAAE;AAAA,EAAI;AAAC,SAAO,KAAGJ,MAAGE,KAAEF,KAAE,MAAIE,KAAE,MAAIA,MAAGD;AAAC;AAA7vB,IAA+vB,IAAE,CAAC;AAAlwB,IAAowB,IAAE,CAAAF,OAAG;AAAC,MAAG,YAAU,OAAOA,IAAE;AAAC,QAAIC,KAAE;AAAG,aAAQ,KAAKD,GAAE,CAAAC,MAAG,IAAE,EAAED,GAAE,CAAC,CAAC;AAAE,WAAOC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAv1B,IAAy1B,IAAE,CAACA,IAAEC,IAAE,GAAEK,IAAEC,OAAI;AAAC,MAAIC,KAAE,EAAER,EAAC,GAAE,IAAE,EAAEQ,EAAC,MAAI,EAAEA,EAAC,KAAG,CAAAR,OAAG;AAAC,QAAIC,KAAE,GAAEQ,KAAE;AAAG,WAAKR,KAAED,GAAE,SAAQ,CAAAS,KAAE,MAAIA,KAAET,GAAE,WAAWC,IAAG,MAAI;AAAE,WAAM,OAAKQ;AAAA,EAAC,GAAGD,EAAC;AAAG,MAAG,CAAC,EAAE,CAAC,GAAE;AAAC,QAAIP,KAAEO,OAAIR,KAAEA,MAAG,CAAAA,OAAG;AAAC,UAAIC,IAAEQ,IAAEC,KAAE,CAAC,CAAC,CAAC;AAAE,aAAKT,KAAE,EAAE,KAAKD,GAAE,QAAQ,GAAE,EAAE,CAAC,IAAG,CAAAC,GAAE,CAAC,IAAES,GAAE,MAAM,IAAET,GAAE,CAAC,KAAGQ,KAAER,GAAE,CAAC,EAAE,QAAQ,GAAE,GAAG,EAAE,KAAK,GAAES,GAAE,QAAQA,GAAE,CAAC,EAAED,EAAC,IAAEC,GAAE,CAAC,EAAED,EAAC,KAAG,CAAC,CAAC,KAAGC,GAAE,CAAC,EAAET,GAAE,CAAC,CAAC,IAAEA,GAAE,CAAC,EAAE,QAAQ,GAAE,GAAG,EAAE,KAAK;AAAE,aAAOS,GAAE,CAAC;AAAA,IAAC,GAAGV,EAAC;AAAE,MAAE,CAAC,IAAE,EAAEO,KAAE,EAAC,CAAC,gBAAc,CAAC,GAAEN,GAAC,IAAEA,IAAE,IAAE,KAAG,MAAI,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE,KAAG,EAAE,IAAE,EAAE,IAAE;AAAK,SAAO,MAAI,EAAE,IAAE,EAAE,CAAC,KAAI,CAACD,IAAEC,IAAEQ,IAAEP,OAAI;AAAC,IAAAA,KAAED,GAAE,OAAKA,GAAE,KAAK,QAAQC,IAAEF,EAAC,IAAE,OAAKC,GAAE,KAAK,QAAQD,EAAC,MAAIC,GAAE,OAAKQ,KAAET,KAAEC,GAAE,OAAKA,GAAE,OAAKD;AAAA,EAAE,GAAG,EAAE,CAAC,GAAEC,IAAEK,IAAE,CAAC,GAAE;AAAC;AAAj3C,IAAm3C,IAAE,CAACN,IAAEC,IAAE,MAAID,GAAE,OAAO,CAACA,IAAEE,IAAEC,OAAI;AAAC,MAAIC,KAAEH,GAAEE,EAAC;AAAE,MAAGC,MAAGA,GAAE,MAAK;AAAC,QAAIJ,KAAEI,GAAE,CAAC,GAAEH,KAAED,MAAGA,GAAE,SAAOA,GAAE,MAAM,aAAW,MAAM,KAAKA,EAAC,KAAGA;AAAE,IAAAI,KAAEH,KAAE,MAAIA,KAAED,MAAG,YAAU,OAAOA,KAAEA,GAAE,QAAM,KAAG,EAAEA,IAAE,EAAE,IAAE,UAAKA,KAAE,KAAGA;AAAA,EAAC;AAAC,SAAOA,KAAEE,MAAG,QAAME,KAAE,KAAGA;AAAE,GAAE,EAAE;AAAE,SAAS,EAAEJ,IAAE;AAAC,MAAI,IAAE,QAAM,CAAC,GAAEE,KAAEF,GAAE,OAAKA,GAAE,EAAE,CAAC,IAAEA;AAAE,SAAO,EAAEE,GAAE,UAAQA,GAAE,MAAI,EAAEA,IAAE,CAAC,EAAE,MAAM,KAAK,WAAU,CAAC,GAAE,EAAE,CAAC,IAAEA,GAAE,OAAO,CAACF,IAAEC,OAAI,OAAO,OAAOD,IAAEC,MAAGA,GAAE,OAAKA,GAAE,EAAE,CAAC,IAAEA,EAAC,GAAE,CAAC,CAAC,IAAEC,IAAE,EAAE,EAAE,MAAM,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC;AAAC;AAAC,IAAU,IAAE,EAAE,KAAK,EAAC,GAAE,EAAC,CAAC;AAAxB,IAA0B,IAAE,EAAE,KAAK,EAAC,GAAE,EAAC,CAAC;;;;;;;ACAryD,IAAM,SAAS;EACpB,QAAQ;IACN,SAAS;IACT,SAAS;IACT,aAAa;IACb,OAAO;IACP,OAAO;IACP,SAAS;MACP,IAAI;MACJ,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IACP;IACA,UAAU;MACR,IAAI;MACJ,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IACP;IACA,MAAM;MACJ,IAAI;MACJ,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IACP;IACA,MAAM;MACJ,IAAI;MACJ,IAAI;MACJ,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IACP;IACA,OAAO;MACL,IAAI;MACJ,IAAI;MACJ,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IACP;IACA,KAAK;MACH,IAAI;MACJ,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IACP;IACA,QAAQ;MACN,IAAI;MACJ,IAAI;MACJ,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IACP;IACA,QAAQ;MACN,IAAI;MACJ,IAAI;MACJ,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IACP;IACA,MAAM;MACJ,IAAI;MACJ,IAAI;MACJ,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IACP;IACA,MAAM;MACJ,IAAI;MACJ,IAAI;MACJ,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IACP;IACA,MAAM;MACJ,IAAI;MACJ,IAAI;MACJ,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IACP;EACF;EACA,OAAO;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,GAAG;EACL;EACA,MAAM;IACJ,MAAM;MACJ,OAAO;MACP,IAAI;MACJ,IAAI;MACJ,IAAI;MACJ,IAAI;MACJ,IAAI;MACJ,OAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;IACT;IACA,YAAY;MACV,OAAO;MACP,OAAO;MACP,IAAI;MACJ,IAAI;MACJ,IAAI;MACJ,IAAI;MACJ,IAAI;MACJ,OAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;IACT;IACA,QAAQ;MACN,MAAM;MACN,YAAY;MACZ,OAAO;MACP,QAAQ;MACR,QAAQ;MACR,UAAU;MACV,MAAM;MACN,WAAW;MACX,OAAO;IACT;IACA,YAAY;MACV,MAAM;MACN,MAAM;IACR;EACF;EACA,aAAa;IACX,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;EACT;EACA,QAAQ;IACN,QAAQ;MACN,MAAM;MACN,IAAI;MACJ,IAAI;MACJ,IAAI;MACJ,IAAI;MACJ,IAAI;MACJ,OAAO;MACP,OAAO;MACP,MAAM;IACR;EACF;EACA,MAAM;IACJ,GAAG;IACH,MAAM;IACN,KAAK;IACL,GAAG;IACH,KAAK;IACL,GAAG;IACH,KAAK;IACL,GAAG;IACH,KAAK;IACL,GAAG;IACH,KAAK;IACL,GAAG;IACH,KAAK;IACL,GAAG;IACH,KAAK;IACL,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;EACN;EACA,QAAQ;IACN,IAAI,CAAC,IAAY,uBACf;IACF,IAAI,CAAC,QAAgB,uBACnB,eAAe,KAAK,oBAAoB,KAAK;IAC/C,IAAI,CAAC,QAAgB,uBACnB,kBAAkB,KAAK,oBAAoB,KAAK;IAClD,IAAI,CAAC,QAAgB,uBACnB,oBAAoB,KAAK,oBAAoB,KAAK;IACpD,IAAI,CAAC,QAAgB,uBACnB,oBAAoB,KAAK,qBAAqB,KAAK;IACrD,OAAO,CAAC,QAAgB,wBACtB,qBAAqB,KAAK;IAC5B,OAAO,CAAC,QAAgB,wBACtB,qBAAqB,KAAK;IAC5B,MAAM,MAAM;EACd;EACA,UAAU;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,OAAO;IACP,SAAS;IACT,UAAU;IACV,OAAO;IACP,SAAS;EACX;AACF;;;ACrSO,IAAM,WAAW,CAAC,EAAE,UAAU,QAAQ,CAAG,EAAA,UAC7C,wBAAA,QAAA,EAAK,WAAW,UAAA,EAAY,UAC3B,cAAA;EAAC;EAAA;IACC,OAAM;IACN,OAAM;IACN,QAAO;IACP,MAAK;IACL,SAAQ;IACR,WAAWS,KAAG,UAAY,EAAA,aAAa,QAAQ,CAAC;IAEhD,cAAA;MAAC;MAAA;QACC,QAAO;QACP,eAAc;QACd,gBAAe;QACf,aAAY;QACZ,GAAE;MAAA;IACH;EAAA;AACH,EAAA,CACF;AA8Bc,SAAA,WAAc,OAAY,MAAqB;AAC7D,MAAI,OAAO;AAAG,WAAO,CAAA;AACrB,MAAIC,KAAI;AACR,QAAM,SAAgB,CAAA;AACf,SAAAA,KAAI,MAAM,QAAQ;AACvB,WAAO,KAAK,MAAM,MAAMA,IAAGA,KAAI,IAAI,CAAC;AACpC,IAAAA,KAAIA,KAAI;EACV;AACO,SAAA;AACT;AAIO,IAAM,kBAA4B,CAAC;EACxC;EACA;EACA;EACA,aAAa,CAAC;EACd,gBAAgB,CAAC;EACjB;EACA,WAAW;EACX;EACA;EACA;AACF,MAAM;AACJ,QAAM,CAAC,eAAe,gBAAgB,IAAU,eAAmB,CAAA,CAAE;AACrE,QAAM,CAAC,eAAe,gBAAgB,IAAU,eAAS,MAAS;AAElE,QAAM,uBAAuB,MAAM;AACjC,qBAAkB,MAAA,CAAqB;EAAA;AAIvC,aAAA,wBAAC,OAAA,EAAI,WAAW,UAAA,EAAY,OACzB,UAAA,cAAc,aAEX,yBAAA,6BAAA,EAAA,UAAA;QAAA;MAAC;MAAA;QACC,WAAW,UAAA,EAAY;QACvB,SAAS,MAAM,eAAe;QAE9B,UAAA;cAAA,wBAAC,UAAA,EAAS,SAAA,CAAoB;UAC7B;cACA,yBAAA,QAAA,EAAK,WAAW,UAAA,EAAY,MAC1B,UAAA;YAAA,OAAO,IAAI,EAAE,YAAY,MAAM,aAAa,gBAAgB;YAC5D,WAAW;YAAO;YAAE,WAAW,SAAS,IAAI,UAAU;UAAA,EAAA,CACzD;QAAA;MAAA;IACF;IACC,WACC,cAAc,WAAW,QACtB,wBAAA,OAAA,EAAI,WAAW,UAAY,EAAA,YACzB,UAAW,WAAA,IAAI,CAAC,OAAO,UAAU,YAAY,KAAK,CAAC,EACtD,CAAA,QAEA,wBAAC,OAAI,EAAA,WAAW,UAAU,EAAE,YACzB,UAAA,cAAc,IAAI,CAAC,SAAS,UAAU;AACrC,iBAAA,wBACG,OACC,EAAA,cAAA,yBAAC,OAAA,EAAI,WAAW,UAAA,EAAY,OAC1B,UAAA;YAAA;UAAC;UAAA;YACC,WAAWD,KAAG,UAAA,EAAY,aAAa,aAAa;YACpD,SAAS,MACP;cAAiB,CAAC,QAChB,IAAI,SAAS,KAAK,IACd,IAAI,OAAO,CAAC,MAAM,MAAM,KAAK,IAC7B,CAAC,GAAG,KAAK,KAAK;YACpB;YAGF,UAAA;kBAAA,wBAAC,UAAS,EAAA,UAAU,cAAc,SAAS,KAAK,EAAA,CAAG;cAAG;cAAI;cACxD,QAAQ;cAAS;cAAK;cACvB,QAAQ,WAAW,WAAW;cAAE;YAAA;UAAA;QACnC;QACC,cAAc,SAAS,KAAK,QAC1B,wBAAA,OAAA,EAAI,WAAW,UAAA,EAAY,YACzB,UAAA,QAAQ,IAAI,CAAC,UAAU,YAAY,KAAK,CAAC,EAC5C,CAAA,IACE;MAAA,EAAA,CACN,EAAA,GArBQ,KAsBV;IAAA,CAEH,EAAA,CACH,IAEA;EAAA,EACN,CAAA,IACE,SAAS,iBAET,wBAAA,6BAAA,EAAA,cAAA;IAAC;IAAA;MACC;MACA,WACE;QAAC;QAAA;UACC,SAAS;UACT,WAAW,UAAA,EAAY;UAEvB,UAAA;gBAAA,wBAAC,QAAA,EAAM,UAAM,MAAA,CAAA;YAAO;YAAI;UAAA;QAAA;MAC1B;MAEF,OAAO;MACP,iBAAiB,CAAC;IAAA;EACpB,EAAA,CACF,QAGE,yBAAA,6BAAA,EAAA,UAAA;QAAA,yBAAC,QAAM,EAAA,UAAA;MAAA;MAAM;IAAA,EAAA,CAAC;IAAQ;QACtB,wBAAC,QAAA,EAAK,WAAW,UAAA,EAAY,OAAQ,UAAA,aAAa,KAAK,EAAA,CAAE;EAAA,EAC3D,CAAA,EAEJ,CAAA;AAEJ;AAeA,SAAS,WAAW,GAAgC;AAClD,SAAO,OAAO,YAAY;AAC5B;AAEA,SAAwB,SAAS;EAC/B;EACA;EACA,WAAW;EACX,WAAW;EACX;EACA,GAAG;AACL,GAAkB;AACV,QAAA,CAAC,UAAU,WAAW,IAAU,eAAS,QAAQ,eAAe,CAAC;AACjE,QAAA,iBAAuB,kBAAY,MAAM,YAAY,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAA,CAAE;AAE7E,MAAI,OAAe,OAAO;AAC1B,MAAI,aAAyB,CAAA;AAEvB,QAAA,eAAe,CAAC,QAAqD;AACzE,UAAM,qBACJ,oBAAoB,OAChB,EAAE,CAAC,IAAI,KAAK,GAAG,KAAA,IACf,mBAAA,OAAA,SAAA,gBAAkB,IAAI,KAAA;AACrB,WAAA;MACL,GAAG;MACH,iBAAiB;IAAA;EACnB;AAGE,MAAA,MAAM,QAAQ,KAAK,GAAG;AACjB,WAAA;AACP,iBAAa,MAAM;MAAI,CAAC,GAAGC,OACzB,aAAa;QACX,OAAOA,GAAE,SAAS;QAClB,OAAO;MAAA,CACR;IAAA;EAGH,WAAA,UAAU,QACV,OAAO,UAAU,YACjB,WAAW,KAAK,KAChB,OAAO,MAAM,OAAO,QAAQ,MAAM,YAClC;AACO,WAAA;AACP,iBAAa,MAAM;MAAK;MAAO,CAAC,KAAKA,OACnC,aAAa;QACX,OAAOA,GAAE,SAAS;QAClB,OAAO;MAAA,CACR;IAAA;EAEM,WAAA,OAAO,UAAU,YAAY,UAAU,MAAM;AAC/C,WAAA;AACM,iBAAA,OAAO,QAAQ,KAAK,EAAE;MAAI,CAAC,CAAC,KAAK,GAAG,MAC/C,aAAa;QACX,OAAO;QACP,OAAO;MAAA,CACR;IAAA;EAEL;AAEa,eAAA,mBAAmB,iBAAiB,UAAU,IAAI;AAEzD,QAAA,gBAAgB,WAAW,YAAY,QAAQ;AAErD,SAAO,SAAS;IACd,aAAa,CAAC,cACZ;MAAC;MAAA;QAEC;QACA;QACA;QACC,GAAG;QACH,GAAG;MAAA;MALC,MAAM;IAMb;IAEF;IACA;IACA;IACA;IACA;IACA;IACA;IACA,GAAG;EAAA,CACJ;AACH;AAEA,IAAM,gBAAgB,MAAM;AAC1B,QAAM,EAAE,QAAQ,MAAM,MAAM,OAAO,QAAQ,OAAW,IAAA;AACtD,QAAM,EAAE,YAAY,YAAY,MAAM,SAAA,IAAa;AAE5C,SAAA;IACL,OAAO;qBACU,WAAW,IAAI;mBACjB,SAAS,EAAE;qBACT,WAAW,EAAE;;;;IAI9B,aAAa;;;;;;;;;IASb,UAAU;;;;eAIC,KAAK,CAAC,CAAC;gBACN,KAAK,CAAC,CAAC;;;;IAInB,cAAc,CAAC,aAAsB;AACnC,UAAI,UAAU;AACL,eAAA;;;;MAIT;AACO,aAAA;;;;IAIT;IACA,cAAc;;aAEL,KAAK,CAAC,CAAC;;;;;;;;;;IAUhB,OAAO;eACI,OAAO,OAAO,GAAG,CAAC;;IAE7B,YAAY;qBACK,KAAK,CAAC,CAAC;sBACN,KAAK,CAAC,CAAC;+BACE,OAAO,SAAS,GAAG,CAAC;;IAE/C,MAAM;eACK,OAAO,KAAK,GAAG,CAAC;mBACZ,SAAS,KAAK,CAAC;sBACZ,KAAK,CAAC,CAAC;;IAEzB,iBAAiB;;;;;;;qBAOA,WAAW,IAAI;mBACjB,SAAS,EAAE;;EAAA;AAG9B;AAEA,IAAI,UAAmD;AAEvD,SAAS,YAAY;AACf,MAAA;AAAgB,WAAA;AACpB,YAAU,cAAc;AAEjB,SAAA;AACT;;;;;AClWO,SAAS,eAAe;AACvB,QAAA,KAAKC,cAAAA,QAAM,MAAA;AAEf,aAAA;IAAC;IAAA;MACC,OAAM;MACN,kBAAiB;MACjB,SAAQ;MAER,cAAA,0BAAC,KAAE,EAAA,WAAU,gBACX,UAAA;YAAA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,IAAG;YACH,IAAG;YACH,IAAG;YACH,IAAG;YACH,mBAAkB;YAClB,eAAc;YAEd,UAAA;kBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;kBACpC,yBAAA,QAAA,EAAK,QAAO,SAAQ,WAAU,UAAA,CAAU;kBACxC,yBAAA,QAAA,EAAK,QAAO,SAAQ,WAAU,UAAA,CAAU;kBACxC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;YAAA;UAAA;QACvC;YACA;UAAC;UAAA;YACC,IAAG;YACH,IAAG;YACH,GAAE;YACF,MAAM,UAAU,EAAE;YAClB,UAAS;YACT,UAAS;UAAA;QACV;YAAA,yBACA,QACC,EAAA,cAAA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,aAAY;YAEZ,cAAA,yBAAC,iBAAc,EAAA,QAAO,0CAA0C,CAAA;UAAA;QAAA,EAAA,CAEpE;YACA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,WAAU;YAEV,cAAC,yBAAA,KAAA,EAAE,QAAQ,UAAU,EAAE,KACrB,cAAA;cAAC;cAAA;gBACC,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,MAAK;gBACL,UAAS;gBACT,UAAS;cAAA;YAAA,EAAA,CAEb;UAAA;QACF;YACA;UAAC;UAAA;YACC,IAAG;YACH,IAAG;YACH,MAAK;YACL,UAAS;YACT,QAAO;YACP,aAAY;YACZ,UAAS;YACT,MAAM,UAAU,EAAE;YAClB,IAAG;YACH,IAAG;UAAA;QACJ;YAAA,yBACA,QACC,EAAA,cAAA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,aAAY;YAEZ,cAAA,yBAAC,iBAAc,EAAA,QAAO,0CAA0C,CAAA;UAAA;QAAA,EAAA,CAEpE;YACA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,WAAU;YAEV,cAAC,yBAAA,KAAA,EAAE,QAAQ,UAAU,EAAE,KACrB,cAAA;cAAC;cAAA;gBACC,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,MAAK;gBACL,UAAS;gBACT,UAAS;cAAA;YAAA,EAAA,CAEb;UAAA;QACF;YACA;UAAC;UAAA;YACC,IAAG;YACH,IAAG;YACH,MAAK;YACL,UAAS;YACT,QAAO;YACP,aAAY;YACZ,UAAS;YACT,MAAM,UAAU,EAAE;YAClB,IAAG;YACH,IAAG;UAAA;QACJ;YAAA,yBACA,QACC,EAAA,cAAA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,aAAY;YAEZ,cAAA,yBAAC,iBAAc,EAAA,QAAO,0CAA0C,CAAA;UAAA;QAAA,EAAA,CAEpE;YACA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,WAAU;YAEV,cAAC,yBAAA,KAAA,EAAE,QAAQ,UAAU,EAAE,KACrB,cAAA;cAAC;cAAA;gBACC,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,MAAK;gBACL,UAAS;gBACT,UAAS;cAAA;YAAA,EAAA,CAEb;UAAA;QACF;YACA;UAAC;UAAA;YACC,IAAG;YACH,IAAG;YACH,MAAK;YACL,UAAS;YACT,QAAO;YACP,aAAY;YACZ,UAAS;YACT,MAAM,UAAU,EAAE;YAClB,IAAG;YACH,IAAG;UAAA;QACJ;YAAA,yBACA,QACC,EAAA,cAAA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,aAAY;YAEZ,cAAA,yBAAC,iBAAc,EAAA,QAAO,0CAA0C,CAAA;UAAA;QAAA,EAAA,CAEpE;YACA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,WAAU;YAEV,cAAC,yBAAA,KAAA,EAAE,QAAQ,UAAU,EAAE,KACrB,cAAA;cAAC;cAAA;gBACC,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,MAAK;gBACL,UAAS;gBACT,UAAS;cAAA;YAAA,EAAA,CAEb;UAAA;QACF;YACA;UAAC;UAAA;YACC,IAAG;YACH,IAAG;YACH,MAAK;YACL,UAAS;YACT,QAAO;YACP,aAAY;YACZ,UAAS;YACT,MAAM,UAAU,EAAE;YAClB,IAAG;YACH,IAAG;UAAA;QACJ;YAAA,yBACA,QACC,EAAA,cAAA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,aAAY;YAEZ,cAAA,yBAAC,iBAAc,EAAA,QAAO,0CAA0C,CAAA;UAAA;QAAA,EAAA,CAEpE;YACA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,WAAU;YAEV,cAAC,yBAAA,KAAA,EAAE,QAAQ,UAAU,EAAE,KACrB,cAAA;cAAC;cAAA;gBACC,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,MAAK;gBACL,UAAS;gBACT,UAAS;cAAA;YAAA,EAAA,CAEb;UAAA;QACF;YACA;UAAC;UAAA;YACC,IAAG;YACH,IAAG;YACH,MAAK;YACL,UAAS;YACT,QAAO;YACP,aAAY;YACZ,UAAS;YACT,MAAM,UAAU,EAAE;YAClB,IAAG;YACH,IAAG;UAAA;QACJ;YAAA,yBACA,QACC,EAAA,cAAA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,aAAY;YAEZ,cAAA,yBAAC,iBAAc,EAAA,QAAO,0CAA0C,CAAA;UAAA;QAAA,EAAA,CAEpE;YACA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,WAAU;YAEV,cAAC,yBAAA,KAAA,EAAE,QAAQ,UAAU,EAAE,KACrB,cAAA;cAAC;cAAA;gBACC,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,MAAK;gBACL,UAAS;gBACT,UAAS;cAAA;YAAA,EAAA,CAEb;UAAA;QACF;YACA;UAAC;UAAA;YACC,IAAG;YACH,IAAG;YACH,MAAK;YACL,UAAS;YACT,QAAO;YACP,aAAY;YACZ,UAAS;YACT,MAAM,UAAU,EAAE;YAClB,IAAG;YACH,IAAG;UAAA;QACJ;YAAA,yBACA,QACC,EAAA,cAAA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,aAAY;YAEZ,cAAA,yBAAC,iBAAc,EAAA,QAAO,0CAA0C,CAAA;UAAA;QAAA,EAAA,CAEpE;YACA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,WAAU;YAEV,cAAC,yBAAA,KAAA,EAAE,QAAQ,UAAU,EAAE,KACrB,cAAA;cAAC;cAAA;gBACC,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,MAAK;gBACL,UAAS;gBACT,UAAS;cAAA;YAAA,EAAA,CAEb;UAAA;QACF;YACC,0BAAA,KAAA,EAAE,MAAM,UAAU,EAAE,KACnB,UAAA;cAAA;YAAC;YAAA;cACC,MAAK;cACL,QAAO;cACP,eAAc;cACd,gBAAe;cACf,aAAY;cACZ,GAAE;YAAA;UACH;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAM,UAAU,EAAE;cAClB,UAAS;cACT,GAAE;cACF,UAAS;YAAA;UACV;cACD;YAAC;YAAA;cACC,MAAK;cACL,UAAS;cACT,QAAO;cACP,eAAc;cACd,gBAAe;cACf,aAAY;cACZ,GAAE;cACF,UAAS;YAAA;UACV;QAAA,EAAA,CACH;YAAA,yBACC,QACC,EAAA,cAAA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,aAAY;YAEZ,cAAA,yBAAC,iBAAc,EAAA,QAAO,0CAA0C,CAAA;UAAA;QAAA,EAAA,CAEpE;YACA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,WAAU;YAEV,cAAC,yBAAA,KAAA,EAAE,QAAQ,UAAU,EAAE,KACrB,cAAA;cAAC;cAAA;gBACC,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,MAAK;gBACL,UAAS;gBACT,UAAS;cAAA;YAAA,EAAA,CAEb;UAAA;QACF;YACC,0BAAA,KAAA,EAAE,MAAM,UAAU,EAAE,KACnB,UAAA;cAAA;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAM,UAAU,EAAE;cAClB,UAAS;cACT,GAAE;cACF,UAAS;YAAA;UACV;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAM,UAAU,EAAE;cAClB,UAAS;cACT,QAAO;cACP,aAAY;cACZ,GAAE;cACF,UAAS;YAAA;UACV;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAM,UAAU,EAAE;cAClB,UAAS;cACT,QAAO;cACP,aAAY;cACZ,GAAE;cACF,UAAS;YAAA;UACV;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAM,UAAU,EAAE;cAClB,UAAS;cACT,QAAO;cACP,aAAY;cACZ,GAAE;cACF,UAAS;YAAA;UACV;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAM,UAAU,EAAE;cAClB,UAAS;cACT,QAAO;cACP,aAAY;cACZ,GAAE;cACF,UAAS;YAAA;UACV;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAM,UAAU,EAAE;cAClB,UAAS;cACT,QAAO;cACP,aAAY;cACZ,GAAE;cACF,UAAS;YAAA;UACV;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAM,UAAU,EAAE;cAClB,UAAS;cACT,QAAO;cACP,aAAY;cACZ,GAAE;cACF,UAAS;YAAA;UACV;cACD;YAAC;YAAA;cACC,MAAK;cACL,QAAO;cACP,eAAc;cACd,aAAY;cACZ,GAAE;YAAA;UACH;cACD;YAAC;YAAA;cACC,MAAK;cACL,QAAO;cACP,eAAc;cACd,aAAY;cACZ,GAAE;YAAA;UACH;QAAA,EAAA,CACH;YAAA,yBACC,QACC,EAAA,cAAA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,aAAY;YAEZ,cAAA,yBAAC,iBAAc,EAAA,QAAO,0CAA0C,CAAA;UAAA;QAAA,EAAA,CAEpE;YACA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,WAAU;YAEV,cAAC,yBAAA,KAAA,EAAE,QAAQ,UAAU,EAAE,KACrB,cAAA;cAAC;cAAA;gBACC,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,MAAK;gBACL,UAAS;gBACT,UAAS;cAAA;YAAA,EAAA,CAEb;UAAA;QACF;YACA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,IAAG;YACH,IAAG;YACH,IAAG;YACH,IAAG;YACH,mBAAkB;YAClB,eAAc;YAEd,UAAA;kBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;kBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;YAAA;UAAA;QACvC;YACA;UAAC;UAAA;YACC,IAAG;YACH,IAAG;YACH,MAAM,UAAU,EAAE;YAClB,UAAS;YACT,UAAS;YACT,MAAM,UAAU,EAAE;YAClB,IAAG;YACH,IAAG;UAAA;QACJ;YAAA,yBACA,QACC,EAAA,cAAA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,aAAY;YAEZ,cAAA,yBAAC,iBAAc,EAAA,QAAO,0CAA0C,CAAA;UAAA;QAAA,EAAA,CAEpE;YACA;UAAC;UAAA;YACC,IAAI,KAAK,EAAE;YACX,OAAM;YACN,QAAO;YACP,GAAE;YACF,GAAE;YACF,WAAU;YAEV,cAAC,yBAAA,KAAA,EAAE,QAAQ,UAAU,EAAE,KACrB,cAAA;cAAC;cAAA;gBACC,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,MAAK;gBACL,UAAS;gBACT,UAAS;cAAA;YAAA,EAAA,CAEb;UAAA;QACF;YACA,yBAAC,KAAA,EAAE,MAAM,UAAU,EAAE,KACnB,cAAA,0BAAC,KAAE,EAAA,WAAU,sBACX,UAAA;cAAA;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,IAAG;cACH,IAAG;cACH,GAAE;cACF,MAAM,UAAU,EAAE;cAClB,UAAS;cACT,UAAS;YAAA;UACV;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAK;cACL,QAAQ,UAAU,EAAE;cACpB,eAAc;cACd,gBAAe;cACf,aAAY;cACZ,GAAE;YAAA;UACH;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAK;cACL,QAAQ,UAAU,EAAE;cACpB,eAAc;cACd,gBAAe;cACf,aAAY;cACZ,GAAE;YAAA;UACH;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAK;cACL,QAAQ,UAAU,EAAE;cACpB,eAAc;cACd,gBAAe;cACf,aAAY;cACZ,GAAE;YAAA;UACH;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAK;cACL,QAAQ,UAAU,EAAE;cACpB,eAAc;cACd,gBAAe;cACf,aAAY;cACZ,GAAE;YAAA;UACH;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAK;cACL,QAAQ,UAAU,EAAE;cACpB,eAAc;cACd,gBAAe;cACf,aAAY;cACZ,GAAE;YAAA;UACH;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAK;cACL,QAAQ,UAAU,EAAE;cACpB,eAAc;cACd,gBAAe;cACf,aAAY;cACZ,GAAE;YAAA;UACH;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAK;cACL,QAAQ,UAAU,EAAE;cACpB,eAAc;cACd,gBAAe;cACf,aAAY;cACZ,GAAE;YAAA;UACH;cACD;YAAC;YAAA;cACC,IAAI,KAAK,EAAE;cACX,IAAG;cACH,IAAG;cACH,IAAG;cACH,IAAG;cACH,mBAAkB;cAClB,eAAc;cAEd,UAAA;oBAAA,yBAAC,QAAK,EAAA,QAAO,KAAI,WAAU,UAAA,CAAU;oBACpC,yBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,UAAA,CAAU;cAAA;YAAA;UACvC;cACA;YAAC;YAAA;cACC,MAAK;cACL,QAAQ,UAAU,EAAE;cACpB,eAAc;cACd,gBAAe;cACf,aAAY;cACZ,GAAE;YAAA;UACH;QAAA,EAAA,CACH,EACF,CAAA;MAAA,EAAA,CACF;IAAA;EAAA;AAGN;;;AC1sBA,SAAS,KAAK,OAAgD;AAC5D,QAAM,EAAE,WAAW,GAAG,KAAA,IAAS;AAE7B,aAAA,0BAAC,UAAQ,EAAA,GAAG,MAAM,WAAWC,KAAGC,WAAA,EAAY,MAAM,SAAS,GACzD,UAAA;QAAA,yBAAC,OAAI,EAAA,WAAWA,WAAU,EAAE,cAAc,UAAQ,WAAA,CAAA;QAAA,yBACjD,OAAI,EAAA,WAAWA,WAAU,EAAE,YAAY,UAAe,kBAAA,CAAA;EACzD,EAAA,CAAA;AAEJ;AAEA,IAAM,yBAAyBC,cAAAA,QAAM,cAKnC,MAAS;AAEX,IAAM,qBAAqB,MAAM;AACzB,QAAA,UAAUA,cAAAA,QAAM,WAAW,sBAAsB;AACvD,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI;MACR;IAAA;EAEJ;AACO,SAAA;AACT;AAEO,SAAS,uBAAuB;EACrC;EACA,aAAa,CAAC;EACd,mBAAmB,CAAC;EACpB,oBAAoB,CAAC;EACrB,WAAW;EACX,kBAAkB,YAAY;EAC9B;AACF,GAA+C;AAC7C,QAAM,CAAC,QAAQ,SAAS,IAAIA,cAAAA,QAAM,SAAyB,IAAK;AAC1D,QAAA,WAAWA,cAAAA,QAAM,OAAuB,IAAI;AAC5C,QAAA,CAAC,QAAQ,SAAS,IAAI;IAC1B;IACA;EAAA;AAEI,QAAA,CAAC,gBAAgB,iBAAiB,IAAI;IAC1C;IACA;EAAA;AAEF,QAAM,CAAC,gBAAgB,iBAAiB,IAAI,aAAa,KAAK;AAC9D,QAAM,CAAC,YAAY,aAAa,IAAI,aAAa,KAAK;AACtD,QAAM,YAAY,aAAA;AAEZ,QAAA,kBAAkB,CACtB,cACA,eACG;AACH,QAAI,WAAW,WAAW;AAAG;AAE7B,kBAAc,IAAI;AAElB,UAAM,WAAW;MACf,iBAAgB,gBAAA,OAAA,SAAA,aAAc,sBAAA,EAAwB,WAAU;MAChE,OAAO,WAAW;IAAA;AAGd,UAAA,MAAM,CAAC,cAA0B;AAC/B,YAAA,QAAQ,SAAS,QAAQ,UAAU;AACnC,YAAA,aAAY,YAAA,OAAA,SAAA,SAAU,kBAAiB;AAE7C,wBAAkB,SAAS;AAE3B,UAAI,YAAY,IAAI;AAClB,kBAAU,KAAK;MAAA,OACV;AACL,kBAAU,IAAI;MAChB;IAAA;AAGF,UAAM,QAAQ,MAAM;AAClB,oBAAc,KAAK;AACV,eAAA,oBAAoB,aAAa,GAAG;AACpC,eAAA,oBAAoB,WAAW,KAAK;IAAA;AAGtC,aAAA,iBAAiB,aAAa,GAAG;AACjC,aAAA,iBAAiB,WAAW,KAAK;EAAA;AAG5C,QAAM,iBAAiB,UAAU;AAEjCA,gBAAAA,QAAM,UAAU,MAAM;AACpB,sBAAkB,UAAU,KAAK;EAChC,GAAA,CAAC,QAAQ,gBAAgB,iBAAiB,CAAC;AAE9CA,gBAAAA,QAAM,UAAU,MAAM;;AACpB,QAAI,gBAAgB;AACZ,YAAA,iBAAgB,KAAA,UAAA,OAAA,SAAA,OAAQ,kBAAR,OAAA,SAAA,GAAuB,MAAM;AAEnD,YAAM,MAAM,MAAM;;AAChB,cAAM,mBAAkBC,MAAA,SAAS,YAAT,OAAA,SAAAA,IAAkB,sBAAA,EAAwB;AAClE,YAAI,UAAA,OAAA,SAAA,OAAQ,eAAe;AACzB,iBAAO,cAAc,MAAM,gBAAgB,GAAG,eAAe;QAC/D;MAAA;AAGE,UAAA;AAEA,UAAA,OAAO,WAAW,aAAa;AAC1B,eAAA,iBAAiB,UAAU,GAAG;AAErC,eAAO,MAAM;AACJ,iBAAA,oBAAoB,UAAU,GAAG;AACxC,eAAI,UAAA,OAAA,SAAA,OAAQ,kBAAiB,OAAO,kBAAkB,UAAU;AACvD,mBAAA,cAAc,MAAM,gBAAgB;UAC7C;QAAA;MAEJ;IACF;AACA;EAAA,GACC,CAAC,cAAc,CAAC;AAEnBD,gBAAAA,QAAM,UAAU,MAAM;AACpB,QAAI,QAAQ;AACV,YAAM,KAAK;AACL,YAAA,WAAW,iBAAiB,EAAE,EAAE;AACnC,SAAA,MAAM,YAAY,oBAAoB,QAAQ;IACnD;EAAA,GACC,CAAC,MAAM,CAAC;AAEX,QAAM,EAAE,OAAO,aAAa,CAAI,GAAA,GAAG,gBAAoB,IAAA;AAEjD,QAAA;IACJ,OAAO,mBAAmB,CAAC;IAC3B,SAAS;IACT,GAAG;EACD,IAAA;AAEE,QAAA;IACJ,OAAO,oBAAoB,CAAC;IAC5B,SAAS;IACT,GAAG;EACD,IAAA;AAGJ,MAAI,CAAC,UAAU;AAAU,WAAA;AAEzB,QAAM,iBAAiB,kBAAkB;AAEzC,aACG,0BAAA,WAAA,EAAU,KAAK,WAAW,WAAU,0BACnC,UAAA;QAAA;MAAC,uBAAuB;MAAvB;QACC,OAAO;UACL,cAAc,iBAAiB,MAAM;UAAA;QACvC;QAEA,cAAA;UAAC;UAAA;YACC,KAAK;YACJ,GAAG;YACJ;YACA,WAAWF;cACTC,WAAY,EAAA;cACZA,WAAA,EAAY,iCAAiC,CAAC,CAAC,MAAM;cACrDA,WAAY,EAAA,+BAA+B,UAAU;cACrDA,WAAY,EAAA;gBACV;gBACA,iBAAiB;cACnB;YACF;YACA,OAAO;cACL,QAAQ;cACR,GAAG;YACL;YACA,QAAQ;YACR;YACA,iBAAiB,CAACG,OAAM,gBAAgB,SAAS,SAASA,EAAC;UAAA;QAC7D;MAAA;IACF;QAEA;MAAC;MAAA;QACC,MAAK;QACJ,GAAG;QACJ,cAAW;QACX,SAAS,CAACA,OAAM;AACd,oBAAU,IAAI;AACd,2BAAiB,cAAcA,EAAC;QAClC;QACA,WAAWJ;UACTC,WAAY,EAAA;UACZA,WAAY,EAAA,qBAAqB,QAAQ;UACzCA,WAAU,EAAE,sBAAsB,CAAC,cAAc;QACnD;QAEA,UAAA;cAAA,0BAAC,OAAI,EAAA,WAAWA,WAAU,EAAE,2BAC1B,UAAA;gBAAA,yBAAC,OAAA,EAAI,WAAWA,WAAA,EAAY,uBAC1B,cAAA,yBAAC,cAAA,CAAA,CAAa,EAChB,CAAA;gBACA,yBAAC,OAAA,EAAI,WAAWA,WAAA,EAAY,uBAC1B,cAAA,yBAAC,cAAA,CAAA,CAAa,EAChB,CAAA;UAAA,EAAA,CACF;cAAA,yBACC,OAAI,EAAA,WAAWA,WAAU,EAAE,qBAAqB,UAAC,IAAA,CAAA;cAAA,yBACjD,OAAI,EAAA,WAAWA,WAAU,EAAE,uBAAuB,UAAe,kBAAA,CAAA;QAAA;MAAA;IACpE;EACF,EAAA,CAAA;AAEJ;AAEA,SAAS,UAAU;EACjB;EACA;EACA;EACA;AACF,GAKG;;AACD,QAAM,cAAc,eAAA;AACd,QAAA,UACJ,YAAY,WAAW,YACnB,YAAY,kBAAkB,CAAA,IAC9B,YAAY;AAEZ,QAAA,QAAQ,YAAY,QAAQ,KAAK,CAAC,MAAM,EAAE,YAAY,MAAM,EAAE;AAE9D,QAAA,QAAQC,cAAAA,QAAM,QAAQ,MAAM;AAC5B,QAAA;AACF,UAAI,SAAA,OAAA,SAAA,MAAO,QAAQ;AACjB,cAAMG,KAAI,MAAM;AAChB,cAAM,IAAY,MAAM,QAAQ,SAAS,MAAM,EAAE;AAC7C,YAAA,EAAE,WAAW,GAAG,GAAG;AACf,gBAAA,UAAU,EAAE,MAAM,CAAC;AACrB,cAAAA,GAAE,OAAO,GAAG;AACP,mBAAA,IAAIA,GAAE,OAAO,CAAC;UACvB;QACF;MACF;AACO,aAAA;IAAA,SACA,OAAO;AACP,aAAA;IACT;EAAA,GACC,CAAC,OAAO,KAAK,CAAC;AAEjB,aAAA,0BACG,OACC,EAAA,UAAA;QAAA;MAAC;MAAA;QACC,MAAK;QACL,cAAY,0BAA0B,MAAM,EAAE;QAC9C,SAAS,MAAM;AACb,cAAI,OAAO;AACT,wBAAY,aAAa,MAAM,KAAK,KAAK,MAAM,EAAE;UACnD;QACF;QACA,WAAWL;UACTC,WAAA,EAAY,mBAAmB,MAAM,OAAO,UAAU,CAAC,CAAC,KAAK;QAC/D;QAEA,UAAA;cAAA;YAAC;YAAA;cACC,WAAWD;gBACTC,WAAY,EAAA,eAAe,oBAAoB,SAAS,KAAK,CAAC;cAChE;YAAA;UACF;cACA,0BAAC,OAAI,EAAA,WAAWD,KAAGC,WAAA,EAAY,UAAU,CAAC,CAAC,KAAK,CAAC,GAC/C,UAAA;gBAAA,0BAAC,OACC,EAAA,UAAA;kBAAA,0BAAC,QAAK,EAAA,WAAWA,WAAU,EAAE,MAC1B,UAAA;gBAAA,SAAS,cAAc,MAAM,QAAQ,SAAS,MAAM,EAAE;gBAAG;cAAA,EAAA,CAC5D;kBAAA,yBACC,QAAK,EAAA,WAAWA,WAAU,EAAE,gBAAiB,UAAM,MAAA,CAAA;YAAA,EAAA,CACtD;gBACA,yBAAC,WAAA,EAAU,MAAA,CAAc;UAAA,EAAA,CAC3B;QAAA;MAAA;IACF;MACE,KAAA,MAAM,aAAN,OAAA,SAAA,GAA4B,cAC5B,yBAAC,OAAA,EAAI,WAAWA,WAAA,EAAY,eAAe,CAAC,CAAC,MAAM,GAChD,UAAA,CAAC,GAAI,MAAM,QAAoB,EAC7B,KAAK,CAACK,IAAGC,OAAM;AACP,aAAAD,GAAE,OAAOC,GAAE;IAAA,CACnB,EACA,IAAI,CAAC,UACJ;MAAC;MAAA;QAEC,OAAO;QACP;QACA;MAAA;MAHK,EAAE;IAAA,CAKV,EAAA,CACL,IACE;EACN,EAAA,CAAA;AAEJ;AAEO,IAAM,8BAA8BL,cAAAA,QAAM,WAG/C,SAASM,6BAA4B,OAAO,KAAyB;;AAC/D,QAAA;IACJ,SAAS;IACT;IACA;IACA,QAAQ;IACR,GAAG;EACD,IAAA;AAEE,QAAA,EAAE,aAAA,IAAiB,mBAAA;AACzB,QAAM,EAAE,WAAW,GAAG,gBAAA,IAAoB;AAE1C,QAAM,gBAAgB,UAAU,EAAE,MAAM,MAAO,CAAA;AAC/C,QAAM,SAAS,cAAc;AAC7B,QAAM,cAAc,eAAe;IACjC;EAAA,CACM;AAER,QAAM,UAAU;IACd,GAAI,YAAY,kBAAkB,CAAC;IACnC,GAAG,YAAY;IACf,GAAG,YAAY;EAAA;AAGjB;IACE;IACA;EAAA;AAKI,QAAA,CAAC,aAAa,cAAc,IAAI;IACpC;IACA;EAAA;AAGI,QAAA,CAAC,UAAU,WAAW,IAAI;IAC9B;IACA;EAAA;AAGF,QAAM,cAAcN,cAAAA,QAAM;IACxB,MAAM,QAAQ,KAAK,CAAC,MAAM,EAAE,YAAY,YAAY,EAAE,OAAO,QAAQ;IACrE,CAAC,SAAS,QAAQ;EAAA;AAGd,QAAA,YAAY,OAAO,KAAK,YAAY,SAAS,UAAU,CAAA,CAAE,EAAE;AAEjE,QAAM,gBAAgB;IACpB,GAAG;IACH,OAAO,OAAO;EAAA;AAId,aAAA;IAAC;IAAA;MACC;MACA,WAAWF;QACTC,WAAY,EAAA;QACZ;QACA;MACF;MACC,GAAG;MAEH,UAAA;QACC,sBAAA;UAAC;UAAA;YACC,WAAWA,WAAA,EAAY;YACvB,aAAa;UAAA;QAAA,IAEb;YACJ;UAAC;UAAA;YACC,WAAWA,WAAA,EAAY;YACvB,SAAS,CAACG,OAAM;AACd,wBAAU,KAAK;AACf,2BAAaA,EAAC;YAChB;YAEA,cAAA;cAAC;cAAA;gBACC,OAAM;gBACN,OAAM;gBACN,QAAO;gBACP,MAAK;gBACL,SAAQ;gBACR,WAAWH,WAAA,EAAY;gBAEvB,cAAA;kBAAC;kBAAA;oBACC,QAAO;oBACP,eAAc;oBACd,gBAAe;oBACf,aAAY;oBACZ,GAAE;kBAAA;gBACH;cAAA;YACH;UAAA;QACF;YACC,0BAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,gBAC1B,UAAA;cAAA,yBAAC,OAAI,EAAA,WAAWA,WAAU,EAAE,KAC1B,cAAA;YAAC;YAAA;cACC,eAAW;cACX,SAAS,CAACG,OAAM;AACd,0BAAU,KAAK;AACf,6BAAaA,EAAC;cAChB;YAAA;UAAA,EAAA,CAEJ;cACA,yBAAC,OAAI,EAAA,WAAWH,WAAU,EAAE,yBAC1B,cAAA,yBAAC,OAAI,EAAA,WAAWA,WAAU,EAAE,gBAC1B,cAAA;YAAC;YAAA;cACC,OAAM;cACN,OAAO,OAAO;gBACZ;kBACE,OAAO,KAAK,aAAa;kBAEvB;oBACE;oBACA;oBACA;oBACA;oBACA;kBAAA,EAEF,IAAI,CAAC,MAAM,CAAC,OAAO,OAAO,CAAC;gBAAA,EAE5B,IAAI,CAAC,QAAQ,CAAC,KAAM,cAAsB,GAAG,CAAC,CAAC,EAC/C;kBACC,CAAC,MACC,OAAO,EAAE,CAAC,MAAM,cAChB,CAAC;oBACC;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;kBAAA,EACA,SAAS,EAAE,CAAC,CAAC;gBACnB;cACJ;cACA,iBAAiB;gBACf,OAAO,CAAC;gBACR,SAAS,CAAC;gBACV,SAAS,CAAC;cACZ;cACA,kBAAkB,CAAC,eAAe;AAChC,uBAAO,WAAW,OAAO,CAAC,MAAM,OAAO,EAAE,UAAU,UAAU;cAC/D;YAAA;UAAA,EAAA,CAEJ,EACF,CAAA;QAAA,EAAA,CACF;YACC,0BAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,iBAC1B,UAAA;cAAA,0BAAC,OAAI,EAAA,WAAWA,WAAU,EAAE,kBAC1B,UAAA;gBAAA,0BAAC,OAAI,EAAA,WAAWA,WAAU,EAAE,eAC1B,UAAA;kBAAA,yBAAC,QAAA,EAAK,UAAQ,WAAA,CAAA;cACb,YAAY,SAAS,qBACnB,yBAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,sBAC1B,cAAA,yBAAC,QAAA,EAAK,WAAWA,WAAA,EAAY,aAAa,UAAA,SAAA,CAAM,EAClD,CAAA,IACE;YAAA,EAAA,CACN;gBACC,0BAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,gBAC1B,UAAA;kBAAC,yBAAA,QAAA,EAAM,UAAY,YAAA,SAAS,SAAA,CAAS;cACpC,YAAY,SAAS,qBACpB,yBAAC,QAAK,EAAA,WAAWA,WAAU,EAAE,gBAC1B,UAAA,YAAY,SAAS,eAAe,SACvC,CAAA,IACE;YAAA,EAAA,CACN;gBACC,0BAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,eAC1B,UAAA;kBAAA,0BAAC,OAAI,EAAA,WAAWA,WAAU,EAAE,oBAC1B,UAAA;oBAAA;kBAAC;kBAAA;oBACC,MAAK;oBACL,SAAS,MAAM;AACb,qCAAe,KAAK;oBACtB;oBACA,UAAU,CAAC;oBACX,WAAWD;sBACTC,WAAA,EAAY,sBAAsB,CAAC,aAAa,IAAI;oBACtD;oBACD,UAAA;kBAAA;gBAED;oBACA;kBAAC;kBAAA;oBACC,MAAK;oBACL,SAAS,MAAM;AACb,qCAAe,IAAI;oBACrB;oBACA,UAAU;oBACV,WAAWD;sBACTC,WAAY,EAAA,sBAAsB,CAAC,CAAC,aAAa,KAAK;oBACxD;oBACD,UAAA;kBAAA;gBAED;cAAA,EAAA,CACF;kBACA,yBAAC,OAAA,EAAI,WAAWA,WAAA,EAAY,mBAC1B,cAAA,yBAAC,OAAI,EAAA,UAAA,2BAAA,CAAwB,EAC/B,CAAA;YAAA,EAAA,CACF;gBACA,yBAAC,OAAA,EAAI,WAAWD,KAAGC,WAAA,EAAY,eAAe,GAC3C,UAAA,CAAC,kBACA;cAAC;cAAA;gBACC,OAAO,OAAO;gBACd,QAAM;gBACN;gBACA;cAAA;YAAA,QAGF,yBAAC,OACG,EAAA,WAAA,YAAY,WAAW,YACrB,YAAY,kBAAkB,CAAA,IAC9B,YAAY,SACd,IAAI,CAAC,OAAOQ,OAAM;AAEhB,yBAAA;gBAAC;gBAAA;kBAEC,MAAK;kBACL,cAAY,0BAA0B,MAAM,EAAE;kBAC9C,SAAS,MACP,YAAY,aAAa,MAAM,KAAK,KAAK,MAAM,EAAE;kBAEnD,WAAWT;oBACTC,WAAA,EAAY,SAAS,UAAU,WAAW;kBAC5C;kBAEA,UAAA;wBAAA;sBAAC;sBAAA;wBACC,WAAWD;0BACTC,WAAA,EAAY,eAAe,eAAe,KAAK,CAAC;wBAClD;sBAAA;oBACF;wBAEA;sBAAC;sBAAA;wBACC,WAAWA,WAAA,EAAY;wBACvB,UAAA,GAAG,MAAM,YAAY,cAAc,cAAc,MAAM,QAAQ;sBAAA;oBAAG;wBACpE,yBAAC,WAAA,EAAU,MAAA,CAAc;kBAAA;gBAAA;gBAnBpB,MAAM,MAAMQ;cAAA;YAoBnB,CAEH,EAAA,CACH,EAEJ,CAAA;UAAA,EAAA,CACF;YACC,KAAA,YAAY,kBAAZ,OAAA,SAAA,GAA2B,cAC1B,0BAAC,OAAA,EAAI,WAAWR,WAAA,EAAY,wBAC1B,UAAA;gBAAA,0BAAC,OAAI,EAAA,WAAWA,WAAU,EAAE,eAC1B,UAAA;kBAAA,yBAAC,OAAA,EAAI,UAAc,iBAAA,CAAA;kBAAA,yBAClB,OAAI,EAAA,WAAWA,WAAU,EAAE,mBAAmB,UAE/C,2BAAA,CAAA;YAAA,EAAA,CACF;gBAAA,yBACC,OACE,EAAA,UAAA,YAAY,cAAc,IAAI,CAAC,UAAU;AAEtC,yBAAA;gBAAC;gBAAA;kBAEC,MAAK;kBACL,cAAY,0BAA0B,MAAM,EAAE;kBAC9C,SAAS,MACP,YAAY,aAAa,MAAM,KAAK,KAAK,MAAM,EAAE;kBAEnD,WAAWD,KAAGC,WAAA,EAAY,SAAS,UAAU,WAAW,CAAC;kBAEzD,UAAA;wBAAA;sBAAC;sBAAA;wBACC,WAAWD;0BACTC,WAAA,EAAY,eAAe,eAAe,KAAK,CAAC;wBAClD;sBAAA;oBACF;wBAEA,yBAAC,QAAA,EAAK,WAAWA,WAAA,EAAY,SAAU,UAAA,GAAG,MAAM,EAAE,GAAG,CAAA;wBAErD,yBAAC,WAAA,EAAU,MAAA,CAAc;kBAAA;gBAAA;gBAhBpB,MAAM;cAAA;YAmBhB,CAAA,EAAA,CACH;UAAA,EAAA,CACF,IACE;QAAA,EAAA,CACN;QACC,kBACE,0BAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,gBAC1B,UAAA;cAAA,yBAAC,OAAI,EAAA,WAAWA,WAAU,EAAE,eAAe,UAAa,gBAAA,CAAA;cAAA,yBACvD,OACC,EAAA,cAAA,0BAAC,OAAA,EAAI,WAAWA,WAAA,EAAY,cAC1B,UAAA;gBAAA;cAAC;cAAA;gBACC,WAAWA,WAAA,EAAY;kBACrB,YAAY;kBACZ,YAAY;gBACd;gBAEA,cAAA,yBAAC,OAAA,EACE,UAAY,YAAA,WAAW,aAAa,YAAY,aAC7C,aACA,YAAY,OAClB,CAAA;cAAA;YACF;gBACC,0BAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,uBAC1B,UAAA;kBAAA,yBAAC,OAAA,EAAI,UAAG,MAAA,CAAA;kBACR,yBAAC,OAAI,EAAA,WAAWA,WAAU,EAAE,kBAC1B,cAAC,yBAAA,QAAA,EAAM,UAAY,YAAA,GAAA,CAAG,EACxB,CAAA;YAAA,EAAA,CACF;gBACC,0BAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,uBAC1B,UAAA;kBAAA,yBAAC,OAAA,EAAI,UAAM,SAAA,CAAA;kBAAA,yBACV,OAAI,EAAA,WAAWA,WAAA,EAAY,kBACzB,YAAA,KAAA,YAAY,mBAAA,OAAA,SAAA,GAAgB;gBAC3B,CAAC,MAAM,EAAE,OAAO,YAAY;cAAA,KAE1B,cACA,KAAA,YAAY,YAAZ,OAAA,SAAA,GAAqB,KAAK,CAAC,MAAM,EAAE,OAAO,YAAY,EAAA,KACpD,WACA,SACR,CAAA;YAAA,EAAA,CACF;gBACC,0BAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,uBAC1B,UAAA;kBAAA,yBAAC,OAAA,EAAI,UAAa,gBAAA,CAAA;kBAClB,yBAAC,OAAA,EAAI,WAAWA,WAAA,EAAY,kBACzB,UAAA,YAAY,YACT,IAAI;gBACF,YAAY;cAAA,EACZ,mBAAmB,IACrB,MAAA,CACN;YAAA,EAAA,CACF;UAAA,EAAA,CACF,EACF,CAAA;UACC,YAAY,iBAET,0BAAA,8BAAA,EAAA,UAAA;gBAAA,yBAAC,OAAI,EAAA,WAAWA,WAAU,EAAE,eAAe,UAAW,cAAA,CAAA;gBACrD,yBAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,gBAC1B,cAAA;cAAC;cAAA;gBACC,OAAM;gBACN,OAAO,YAAY;gBACnB,iBAAiB,CAAC;cAAA;YAAA,EAAA,CAEtB;UAAA,EAAA,CACF,IACE;cAAA,yBACH,OAAI,EAAA,WAAWA,WAAU,EAAE,eAAe,UAAQ,WAAA,CAAA;cAClD,yBAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,gBAC1B,cAAA,yBAAC,UAAS,EAAA,OAAM,SAAQ,OAAO,aAAa,iBAAiB,CAAA,EAAI,CAAA,EAAA,CACnE;QAAA,EAAA,CACF,IACE;QACH,gBACE,0BAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,iBAC1B,UAAA;cAAA,yBAAC,OAAI,EAAA,WAAWA,WAAU,EAAE,eAAe,UAAa,gBAAA,CAAA;cACvD,yBAAA,OAAA,EAAI,WAAWA,WAAA,EAAY,gBAC1B,cAAA;YAAC;YAAA;cACC,OAAO,YAAY,SAAS,UAAU,CAAC;cACvC,iBAAiB,OAAO;gBACrB,YAAY,SAAS,UAAiB,CAAC;cAAA,EACxC,OAAO,CAAC,KAAU,SAAS;AACvB,oBAAA,IAAI,IAAI,CAAA;AACL,uBAAA;cACT,GAAG,CAAA,CAAE;YAAA;UAAA,EAAA,CAET;QAAA,EAAA,CACF,IACE;MAAA;IAAA;EAAA;AAGV,CAAC;AAED,SAAS,UAAU,EAAE,MAAA,GAAoC;AACvD,QAAM,SAAS,UAAA;AAEf,QAAM,WAAWC,cAAAA,QAAM;IACrB,OAAO,CAAA;IACP,OAAO,CAAA;IACP,CAAC;AAEHA,gBAAAA,QAAM,UAAU,MAAM;AACd,UAAA,WAAW,YAAY,MAAM;AACxB,eAAA;OACR,GAAI;AAEP,WAAO,MAAM;AACX,oBAAc,QAAQ;IAAA;EAE1B,GAAG,CAAE,CAAA;AAEL,MAAI,CAAC,OAAO;AACH,WAAA;EACT;AAEA,QAAM,QAAQ,OAAO,gBAAgB,SAAA,OAAA,SAAA,MAAO,OAAO;AAE/C,MAAA,CAAC,MAAM,QAAQ,QAAQ;AAClB,WAAA;EACT;AAEA,QAAM,MAAM,KAAK,IAAI,KAAI,SAAA,OAAA,SAAA,MAAO;AAChC,QAAM,YACJ,MAAM,QAAQ,aAAa,OAAO,QAAQ,oBAAoB;AAC1D,QAAA,SACJ,MAAM,QAAQ,UAAU,OAAO,QAAQ,iBAAiB,KAAK,KAAK;AAGlE,aAAA,0BAAC,OAAI,EAAA,WAAWF,KAAGC,WAAA,EAAY,UAAU,MAAM,SAAS,CAAC,GACvD,UAAA;QAAC,yBAAA,OAAA,EAAK,UAAW,WAAA,GAAG,EAAE,CAAA;QACtB,yBAAC,OAAA,EAAI,UAAC,IAAA,CAAA;QACL,yBAAA,OAAA,EAAK,UAAW,WAAA,SAAS,EAAE,CAAA;QAC5B,yBAAC,OAAA,EAAI,UAAC,IAAA,CAAA;QACL,yBAAA,OAAA,EAAK,UAAW,WAAA,MAAM,EAAE,CAAA;EAC3B,EAAA,CAAA;AAEJ;AAEA,SAAS,WAAW,IAAY;AAC9B,QAAM,QAAQ,CAAC,KAAK,OAAO,KAAK,GAAG;AAC7B,QAAA,SAAS,CAAC,KAAK,KAAM,KAAK,KAAO,KAAK,MAAS,KAAK,KAAQ;AAElE,MAAI,kBAAkB;AACtB,WAASQ,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AAClC,QAAA,OAAOA,EAAC,IAAK;AAAG;AACF,sBAAAA;EACpB;AAEA,QAAM,YAAY,IAAI,KAAK,aAAa,UAAU,UAAU;IAC1D,gBAAgB;IAChB,UAAU;IACV,uBAAuB;EAAA,CACxB;AAED,SAAO,UAAU,OAAO,OAAO,eAAe,CAAE,IAAI,MAAM,eAAe;AAC3E;AAEA,IAAMC,iBAAgB,MAAM;AAC1B,QAAM,EAAE,QAAQ,MAAM,MAAM,OAAO,QAAQ,OAAW,IAAA;AACtD,QAAM,EAAE,YAAY,YAAY,MAAM,SAAA,IAAa;AAE5C,SAAA;IACL,wBAAwB;;;;;;;;8BAQE,OAAO,KAAK,GAAG,CAAC;;;IAG1C,kCAAkC,CAAC,WAAoB;AAC9C,aAAA;sBACS,SAAS,YAAY,QAAQ;;IAE/C;IACA,gCAAgC,CAAC,eAAwB;AACvD,UAAI,YAAY;AACP,eAAA;;;MAGT;AAEO,aAAA;;;IAGT;IACA,iCAAiC,CAAC,QAAiB,WAAmB;AACpE,UAAI,QAAQ;AACH,eAAA;;;;MAIT;AACO,aAAA;;gCAEmB,MAAM;;IAElC;IACA,MAAM;;;;;;qBAMW,WAAW,IAAI;aACvB,OAAO,KAAK,GAAG,CAAC;;;;;;;yBAOJ,OAAO,OAAO,EAAE;6BACZ,OAAO,KAAK,GAAG,CAAC;;;IAGzC,cAAc;mBACC,KAAK,KAAK,EAAE;qBACV,KAAK,OAAO,IAAI;qBAChB,KAAK,WAAW,EAAE;;eAExB,OAAO,KAAK,GAAG,CAAC;;IAE3B,YAAY;qBACK,KAAK,OAAO,QAAQ;mBACtB,KAAK,KAAK,EAAE;;;;;;;;IAQ3B,eAAe;;mBAEA,SAAS,EAAE;qBACT,WAAW,IAAI;0BACV,OAAO,SAAS,GAAG,CAAC;eAC/B,OAAO,KAAK,GAAG,CAAC;;;;;;qBAMV,SAAS,EAAE;;;IAG5B,YAAY;;;;;;;;;4BASY,OAAO,OAAO,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC;;;IAGtD,gBAAgB;;;;;gCAKY,OAAO,KAAK,GAAG,CAAC;;;;IAI5C,yBAAyB;;;;IAIzB,gBAAgB;iBACH,OAAO,KAAK,CAAC,CAAC;;IAE3B,KAAK;;;iBAGQ,OAAO,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,GAAG,CAAC;aACtC,OAAO,KAAK,GAAG,CAAC;uBACN,OAAO,SAAS,GAAG,CAAC;;;IAGvC,eAAe;;;;;0BAKO,OAAO,SAAS,GAAG,CAAC;qBACzB,OAAO,KAAK,CAAC,CAAC;qBACd,KAAK,OAAO,MAAM;mBACpB,KAAK,KAAK,EAAE;oBACX,OAAO,KAAK,CAAC,CAAC;qBACb,KAAK,WAAW,EAAE;;;;;IAKnC,aAAa;oBACG,OAAO,OAAO,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC;eACnC,OAAO,OAAO,GAAG,CAAC;;iBAEhB,OAAO,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,GAAG,CAAC;uBAC5B,OAAO,OAAO,IAAI;mBACtB,KAAK,KAAK,EAAE;qBACV,KAAK,OAAO,MAAM;0BACb,OAAO,OAAO,GAAG,CAAC;;IAExC,gBAAgB;eACL,OAAO,OAAO,GAAG,CAAC;;IAE7B,gBAAgB;iBACH,OAAO,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;;;mBAGhC,KAAK,KAAK,EAAE;;IAE3B,oBAAoB;;;0BAGE,OAAO,KAAK,GAAG,CAAC;uBACnB,OAAO,OAAO,EAAE;;;IAGnC,uBAAuB,CAAC,QAAiB,eAAwB;AAC/D,YAAM,OAAO;;;;;;;uBAOI,WAAW,IAAI;uBACf,KAAK,OAAO,MAAM;;AAE7B,YAAA,UAAU,CAAC,IAAI;AAErB,UAAI,QAAQ;AACV,cAAM,eAAe;wBACL,OAAO,SAAS,GAAG,CAAC;mBACzB,OAAO,KAAK,GAAG,CAAC;;AAE3B,gBAAQ,KAAK,YAAY;MAAA,OACpB;AACL,cAAM,iBAAiB;mBACZ,OAAO,KAAK,GAAG,CAAC;wBACX,OAAO,SAAS,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC;;AAEhD,gBAAQ,KAAK,cAAc;MAC7B;AAEA,UAAI,YAAY;AACd,cAAMC,UAAS;oCACa,OAAO,OAAO,KAAK,GAAG,CAAC;;AAEnD,gBAAQ,KAAKA,OAAM;MACrB;AAEO,aAAA;IACT;IACA,mBAAmB;;;;;qBAKF,KAAK,OAAO,MAAM;eACxB,OAAO,KAAK,GAAG,CAAC;;IAE3B,UAAU,CAAC,WAAoB;AAC7B,YAAM,OAAO;;mCAEgB,OAAO,SAAS,GAAG,CAAC;;;mBAGpC,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC;eACtB,KAAK,CAAC,CAAC;qBACD,SAAS,EAAE;iBACf,OAAO,KAAK,GAAG,CAAC;;AAErB,YAAA,UAAU,CAAC,IAAI;AAErB,UAAI,QAAQ;AACV,cAAM,eAAe;wBACL,OAAO,SAAS,GAAG,CAAC;;AAEpC,gBAAQ,KAAK,YAAY;MAC3B;AAEO,aAAA;IACT;IACA,gBAAgB,CAAC,UAAwD;AACvE,YAAM,OAAO;;iBAEF,KAAK,CAAC,CAAC;kBACN,KAAK,CAAC,CAAC;sBACH,OAAO,KAAK,EAAE,GAAG,CAAC;4BACZ,OAAO,KAAK,EAAE,GAAG,CAAC;yBACrB,OAAO,OAAO,IAAI;;;;AAI/B,YAAA,UAAU,CAAC,IAAI;AAErB,UAAI,UAAU,QAAQ;AACpB,cAAM,aAAa;wBACH,OAAO,KAAK,GAAG,CAAC;0BACd,OAAO,KAAK,GAAG,CAAC;;AAElC,gBAAQ,KAAK,UAAU;MACzB;AAEO,aAAA;IACT;IACA,SAAS;;qBAEQ,WAAW,IAAI,CAAC;;IAEjC,WAAW,CAAC,gBAAyB;AACnC,YAAM,OAAO;;eAEJ,KAAK,CAAC,CAAC;qBACD,SAAS,EAAE;iBACf,OAAO,KAAK,GAAG,CAAC;;uBAEV,WAAW,IAAI,CAAC;;AAG3B,YAAA,UAAU,CAAC,IAAI;AAErB,UAAI,aAAa;AACf,cAAM,gBAAgB;mBACX,OAAO,OAAO,GAAG,CAAC;;AAE7B,gBAAQ,KAAK,aAAa;MAC5B;AAEO,aAAA;IACT;IACA,iBAAiB;;;;;gCAKW,OAAO,KAAK,GAAG,CAAC;;;;IAI5C,gBAAgB;;;;;;gCAMY,OAAO,KAAK,GAAG,CAAC;;;gCAGhB,OAAO,KAAK,GAAG,CAAC;;;IAG5C,iBAAiB;;;;;;;;IAQjB,iBAAiB;;;;IAIjB,oBAAoB,CAAC,QAAiB,YAAqB;AACzD,YAAM,OAAO;;mCAEgB,OAAO,SAAS,GAAG,CAAC;;mBAEpC,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC;eACtB,KAAK,CAAC,CAAC;qBACD,SAAS,EAAE;iBACf,OAAO,KAAK,GAAG,CAAC;kBACf,UAAU,YAAY,SAAS;uBAC1B,WAAW,IAAI,CAAC;;AAE3B,YAAA,UAAU,CAAC,IAAI;AAErB,UAAI,QAAQ;AACV,cAAM,eAAe;wBACL,OAAO,SAAS,GAAG,CAAC;;AAEpC,gBAAQ,KAAK,YAAY;MAC3B;AAEO,aAAA;IACT;IACA,WAAW,CAAC,YAAqB;AAC/B,YAAM,OAAO;;;;;qBAKE,SAAS,EAAE;uBACT,WAAW,IAAI,CAAC;;AAG3B,YAAA,UAAU,CAAC,IAAI;AAErB,UAAI,CAAC,SAAS;AACZ,cAAM,cAAc;mBACT,OAAO,KAAK,GAAG,CAAC;;AAE3B,gBAAQ,KAAK,WAAW;MAC1B;AAEO,aAAA;IACT;IACA,gBAAgB;eACL,OAAO,KAAK,GAAG,CAAC;mBACZ,SAAS,EAAE;qBACT,WAAW,IAAI,CAAC;;IAEjC,gBAAgB,CAAC,WAAoB;AACnC,YAAM,OAAO;uBACI,SAAS,IAAI,KAAK,GAAG,CAAC;uBACtB,SAAS,KAAK,aAAa,OAAO,KAAK,GAAG,CAAC,EAAE;;AAEvD,aAAA;IACT;IACA,MAAM;mBACS,SAAS,EAAE;qBACT,WAAW,IAAI,CAAC;;IAEjC,kBAAkB;;;;IAIlB,wBAAwB;;;;;IAKxB,sBAAsB;;;;;IAKtB,cAAc;;;iBAGD,OAAO,KAAK,CAAC,CAAC;mBACZ,OAAO,KAAK,KAAK,EAAE;eACvB,OAAO,OAAO,KAAK,GAAG,CAAC;qBACjB,OAAO,KAAK,WAAW,EAAE;;IAE1C,aAAa,CACX,QACA,eACG;AACH,YAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,OAAO;QACP,UAAU;QACV,YAAY;MAAA;AAGd,YAAM,QACJ,cAAc,WAAW,YAAY,SAAS,SAAS,MAAM;AAExD,aAAA;;;;;yBAKY,OAAO,OAAO,OAAO,EAAE;uBACzB,OAAO,KAAK,OAAO,MAAM;4BACpB,OAAO,OAAO,KAAK,EAAE,GAAG,CAAC,GAAG,OAAO,MAAM,EAAE,CAAC;iBACvD,OAAO,OAAO,KAAK,EAAE,GAAG,CAAC;4BACd,OAAO,OAAO,KAAK,EAAE,GAAG,CAAC;yBAC5B,OAAO,KAAK,CAAC,CAAC;;;IAGnC;IACA,kBAAkB;;;;;IAKlB,uBAAuB;;;IAGvB,cAAc;oBACE,OAAO,SAAS,GAAG,CAAC;iBACvB,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC;uBACpC,OAAO,OAAO,EAAE;;;;;;;;;;0BAUb,OAAO,KAAK,GAAG,CAAC;mBACvB,KAAK,KAAK,EAAE;;;;;sBAKT,OAAO,SAAS,GAAG,CAAC;;;IAGtC,sBAAsB,CACpB,aACG;AACH,YAAM,OAAO;UACT,aAAa,aAAa,QAAQ,KAAK,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,MAAM,EAAE;UACnE,aAAa,cAAc,QAAQ,KAAK,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,MAAM,EAAE;UACrE,aAAa,gBACX,WAAW,KAAK,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,MACpC,EAAE;UACJ,aAAa,iBACX,WAAW,KAAK,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,MACrC,EAAE;;AAED,aAAA;IACT;IACA,uBAAuB,CAAC,WAAoB;AAC1C,UAAI,QAAQ;AACH,eAAA;;;;;MAKT;AACO,aAAA;;;;;IAKT;IACA,uBAAuB;qBACN,KAAK,OAAO,QAAQ;mBACtB,KAAK,KAAK,EAAE;;;;;;;;IAQ3B,qBAAqB;;oBAEL,OAAO,OAAO,KAAK,GAAG,CAAC;;;;;IAKvC,2BAA2B;;eAEhB,KAAK,CAAC,CAAC;gBACN,KAAK,CAAC,CAAC;;;;;IAKnB,uBAAuB;eACZ,KAAK,CAAC,CAAC;gBACN,KAAK,CAAC,CAAC;;;;;;;IAOnB,uBAAuB;eACZ,KAAK,CAAC,CAAC;gBACN,KAAK,CAAC,CAAC;;;;;;IAMnB,eAAe;;;;;;;;0BAQO,OAAO,SAAS,GAAG,CAAC;;4BAElB,OAAO,SAAS,GAAG,CAAC;;;;eAIjC,KAAK,CAAC,CAAC;;sBAEA,OAAO,SAAS,GAAG,CAAC;qBACrB,OAAO,SAAS,GAAG,CAAC;oBACrB,OAAO,SAAS,GAAG,CAAC;;uBAEjB,OAAO,OAAO,EAAE,IAAI,OAAO,OAAO,EAAE;iBAC1C,KAAK,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;;;;;;iBAM9C,KAAK,GAAG,CAAC;kBACR,KAAK,GAAG,CAAC;6BACE,KAAK,CAAC,CAAC;;;IAGhC,mBAAmB;eACR,OAAO,KAAK,GAAG,CAAC;eAChB,KAAK,CAAC,CAAC;gBACN,KAAK,CAAC,CAAC;;EAAA;AAGvB;AAEA,IAAIC,WAAmD;AAEvD,SAASX,aAAY;AACf,MAAAW;AAAgB,WAAAA;AACpB,EAAAA,WAAUF,eAAc;AAEjB,SAAAE;AACT;", "names": ["React", "React", "e", "i", "a", "b", "t", "e", "t", "l", "a", "n", "c", "i", "p", "u", "r", "o", "cx", "i", "React", "cx", "getStyles", "React", "_a", "e", "p", "a", "b", "TanStackRouterDevtoolsPanel", "i", "stylesFactory", "border", "_styles"]}