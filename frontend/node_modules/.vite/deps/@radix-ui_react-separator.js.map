{"version": 3, "sources": ["../../@radix-ui/react-separator/src/Separator.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n *  Separator\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Separator';\nconst DEFAULT_ORIENTATION = 'horizontal';\nconst ORIENTATIONS = ['horizontal', 'vertical'] as const;\n\ntype Orientation = (typeof ORIENTATIONS)[number];\ntype SeparatorElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SeparatorProps extends PrimitiveDivProps {\n  /**\n   * Either `vertical` or `horizontal`. Defaults to `horizontal`.\n   */\n  orientation?: Orientation;\n  /**\n   * Whether or not the component is purely decorative. When true, accessibility-related attributes\n   * are updated so that that the rendered element is removed from the accessibility tree.\n   */\n  decorative?: boolean;\n}\n\nconst Separator = React.forwardRef<SeparatorElement, SeparatorProps>((props, forwardedRef) => {\n  const { decorative, orientation: orientationProp = DEFAULT_ORIENTATION, ...domProps } = props;\n  const orientation = isValidOrientation(orientationProp) ? orientationProp : DEFAULT_ORIENTATION;\n  // `aria-orientation` defaults to `horizontal` so we only need it if `orientation` is vertical\n  const ariaOrientation = orientation === 'vertical' ? orientation : undefined;\n  const semanticProps = decorative\n    ? { role: 'none' }\n    : { 'aria-orientation': ariaOrientation, role: 'separator' };\n\n  return (\n    <Primitive.div\n      data-orientation={orientation}\n      {...semanticProps}\n      {...domProps}\n      ref={forwardedRef}\n    />\n  );\n});\n\nSeparator.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isValidOrientation(orientation: any): orientation is Orientation {\n  return ORIENTATIONS.includes(orientation);\n}\n\nconst Root = Separator;\n\nexport {\n  Separator,\n  //\n  Root,\n};\nexport type { SeparatorProps };\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,YAAuB;AAoCnB,yBAAA;AA7BJ,IAAM,OAAO;AACb,IAAM,sBAAsB;AAC5B,IAAM,eAAe,CAAC,cAAc,UAAU;AAiB9C,IAAM,YAAkB,iBAA6C,CAAC,OAAO,iBAAiB;AAC5F,QAAM,EAAE,YAAY,aAAa,kBAAkB,qBAAqB,GAAG,SAAS,IAAI;AACxF,QAAM,cAAc,mBAAmB,eAAe,IAAI,kBAAkB;AAE5E,QAAM,kBAAkB,gBAAgB,aAAa,cAAc;AACnE,QAAM,gBAAgB,aAClB,EAAE,MAAM,OAAO,IACf,EAAE,oBAAoB,iBAAiB,MAAM,YAAY;AAE7D,aACE;IAAC,UAAU;IAAV;MACC,oBAAkB;MACjB,GAAG;MACH,GAAG;MACJ,KAAK;IAAA;EACP;AAEJ,CAAC;AAED,UAAU,cAAc;AAIxB,SAAS,mBAAmB,aAA8C;AACxE,SAAO,aAAa,SAAS,WAAW;AAC1C;AAEA,IAAM,OAAO;", "names": []}